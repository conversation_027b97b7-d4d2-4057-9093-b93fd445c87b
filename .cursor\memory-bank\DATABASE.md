# Database Memory

## Supabase Configuration
- Project ID: uoldttyukavqjnrqhych
- Region: ap-southeast-1
- Status: Active
- Database: PostgreSQL with Supabase Auth for authentication

## Schema Tables
1. users
   - Core user profile data
   - Authentication linked with Supa<PERSON> Auth via auth_user_id field
   - Includes fields: name, nationality, ic, passport, gender, dateOfBirth, contactNo, address, postcode, country, state, interests, howeiCoin, greenPoints
   - Emergency contact fields: emergencyContactName, emergencyContactNo, emergencyContactRelationship

2. events
   - Event management data
   - Relationships with users and organizations

3. organizations
   - Organization management
   - Multi-tenant support

4. ticket_types
   - Event ticket configurations

5. registrations
   - Event registration records

6. payments
   - Payment transaction records

7. collections
   - General data collections

## API Routes
- `/api/user/profile`
  - GET: Fetch user profile
  - PUT: Update user profile
  - Authentication: Supabase Auth session-based

## Middleware
- Supabase Auth middleware configured
- Handles session management and authentication
- Protects routes requiring authentication
- Integrates with Row Level Security (RLS) policies

## Schema Migrations
1. Initial Schema (0000_skinny_monster_badoon.sql)
   - Created core tables: events, organizations, users, ticket_types, registrations, payments, collections
   - Established relationships and indexes

2. Webhook Logs (0000_webhook_logs.sql)
   - Added webhook_logs table for Supabase Auth webhook tracking
   - Created indexes on event and tenant_id

3. Promotion Applications (0003_promotion_applications.sql)
   - Added promotion_applications table
   - Created indexes for efficient lookups

## Connection Test
- Endpoint: `/api/test`
- Type: GET
- Purpose: Tests database connectivity by fetching organizations
- Status: Working (200 OK)