export interface DashboardCardProps {
  title: string;
  description: string;
  href: string;
  count: number;
}

export function DashboardCard({
  title,
  description,
  href,
  count
}: DashboardCardProps) {
  return (
    <a
      href={href}
      className="block p-6 bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg border border-[hsl(var(--border))] hover:shadow-md transition-shadow"
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-medium text-[hsl(var(--foreground))]">{title}</h3>
          <p className="mt-1 text-sm text-[hsl(var(--muted-foreground))]">{description}</p>
        </div>
        <span className="inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[hsl(var(--primary-50))] text-[hsl(var(--primary))] dark:bg-[hsl(var(--primary-subtle))] dark:text-[hsl(var(--primary-foreground))]">
          {count}
        </span>
      </div>
    </a>
  );
}