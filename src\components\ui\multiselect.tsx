"use client"

import * as React from "react"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "./badge"

export type Option = {
  label: string
  value: string
}

interface MultiSelectProps {
  options: Option[]
  selected: Option[]
  onChange: (options: Option[]) => void
  className?: string
}

export function MultiSelect({
  options,
  selected,
  onChange,
  className,
}: MultiSelectProps) {
  const handleSelect = (option: Option) => {
    if (selected.some(s => s.value === option.value)) {
      onChange(selected.filter(s => s.value !== option.value))
    } else {
      onChange([...selected, option])
    }
  }

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {options.map((option) => {
        const isSelected = selected.some(s => s.value === option.value)
        return (
          <Badge
            key={option.value}
            variant={isSelected ? "default" : "outline"}
            className={cn(
              "cursor-pointer hover:bg-primary/90",
              isSelected && "bg-primary text-primary-foreground",
              !isSelected && "hover:text-primary-foreground"
            )}
            onClick={() => handleSelect(option)}
          >
            {option.label}
            {isSelected && (
              <X className="ml-1 h-3 w-3 text-current" />
            )}
          </Badge>
        )
      })}
    </div>
  )
} 