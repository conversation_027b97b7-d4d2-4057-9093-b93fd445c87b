# Environment Variables

This document outlines the environment variables used in the Fuiyoo application.

## Core Environment Variables

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `NEXT_PUBLIC_SITE_URL` | The public URL of the site in production | `https://fuiyoo.netlify.app` | Yes, in production |
| `NEXT_PUBLIC_DEV_URL` | The development URL for local development | `http://localhost:3000` | No |
| `NEXT_PUBLIC_DEV_API_URL` | The API base URL for development | `http://localhost:3000/api` | No |
| `NEXT_PUBLIC_SUPABASE_URL` | The URL of your Supabase project | - | Yes |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | The anonymous key for Supabase client access | - | Yes |
| `SUPABASE_SERVICE_ROLE_KEY` | The service role key for admin Supabase operations | - | Yes, for admin operations |

## Development Environment

For local development, create a `.env.local` file in the project root with the following variables:

```bash
# Site URLs
NEXT_PUBLIC_DEV_URL=http://localhost:3000
NEXT_PUBLIC_DEV_API_URL=http://localhost:3000/api

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
```

## Netlify Environment Configuration

The application is configured to use Netlify's built-in environment variables system, which supports different environments (contexts) like production, staging, and branch deploys.

### Environment Variables in Netlify Dashboard

Set environment variables in the Netlify dashboard under **Site settings > Build & deploy > Environment variables**. You can set:

1. **Site-wide variables** - Applied to all deploys
2. **Context-specific variables** - Applied only to specific contexts (production, staging, etc.)

### Context-Specific Configuration

The `netlify.toml` file includes context-specific environment settings:

```toml
# Production context (main branch)
[context.production.environment]
  NEXT_PUBLIC_SITE_URL = "https://fuiyoo.netlify.app"
  NODE_ENV = "production"

# Staging context (staging branch)
[context.staging.environment]
  NEXT_PUBLIC_SITE_URL = "https://staging--fuiyoo.netlify.app"
  NODE_ENV = "staging"
```

### Required Environment Variables

For each environment (production, staging, etc.), set these variables in the Netlify dashboard:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Authentication
NEXT_PUBLIC_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_AFTER_SIGN_UP_URL=/dashboard
```

> **Important**: The build process is configured to prioritize Netlify environment variables and ignore any local `.env` files during builds. This ensures consistent environment variables across all environments.

## URL Handling

The application uses a utility function `getBaseUrl()` in `src/utils/url-utilities.ts` to determine the base URL:

- In client-side code, it uses `window.location.origin`
- In server-side code, it uses `NEXT_PUBLIC_SITE_URL` with a fallback to `https://fuiyoo.netlify.app`

For API URLs, the application uses the `apiBaseUrl` configuration in `src/lib/env-config.ts`:

- In development, it uses `NEXT_PUBLIC_DEV_API_URL` with a fallback to `http://localhost:3000/api`
- In production, it uses `${NEXT_PUBLIC_SITE_URL}/api` with a fallback to `https://fuiyoo.netlify.app/api`

## Adding New Environment Variables

When adding new environment variables:

1. Add them to `.env.example` as a reference
2. Document them in this file
3. Update the `netlify.toml` file if they need to be set in the Netlify build environment
4. For client-side variables, prefix them with `NEXT_PUBLIC_`
