# 📄 Product Requirements Document (PRD)

## 🏷 Project Name: Fuiyoo

Fuiyoo is a modern multitenant event management and ticketing platform tailored for organizers of fundraising runs, awareness events, seminars, concerts, and more.

---

## 🧩 Key Problems to Solve

### 1. Reliable & Accurate Participant Data
- Current platforms (e.g. Howei.com) frequently deliver **inaccurate data** to organizers.
- Issues arise during event logistics (e.g. **wrong t-shirt sizes**, missing categories).
- Fuiyoo aims to implement a **robust and validated schema** with proper relations to avoid such issues.

### 2. Explicit Schema per Event Type
- **Dynamic event schema** based on event type (e.g. Concert vs Fun Run).
- Example:
  - **Concert**: Define multiple zones, ticket prices per zone, capacity per zone.
  - **Fun Run**: Capture run category (e.g. 3km, 5km), t-shirt size, emergency contact.
- Schema should be **extensible**, **validatable**, and **enforce input constraints** using Zod and dynamic form rendering.

### 3. Clear Monetization Strategy
- Platform charges **6% or RM2.50**, whichever is higher, **per ticket sold**.
- Additionally, **6% SST is applied on the platform's fee**.
- Example:
  - Ticket Price: RM50
  - Platform Fee: RM3 (6%)
  - SST on Fee: RM0.18
  - **Total Fee Collected**: RM3.18

### 4. Organizer Verification & Trust
- Need for verified and trustworthy event organizers.
- Require document submission (SSM, NGO registration, etc.).
- Admin verification workflow before organizer status granted.

---

## 👥 User Tiers & Workflows

### 1. Public Users (Attendees)
**Registration & Access:**
- Simple email/password or social login.
- Basic profile creation.
- No document verification needed.

**Core Features:**
- Browse available events.
- Purchase tickets.
- Receive QR code e-tickets.
- Track event attendance.
- Manage goody bag collections.
- View purchase history.

### 2. Event Organizers
**Registration Process:**
1. Register as public user.
2. Access organizer application.
3. Submit required documents:
   - SSM registration for companies.
   - Society registration for NGOs.
   - Other relevant certifications.
4. Wait for admin verification.
5. Receive organizer status upon approval.

**Core Features:**
- Organization profile management.
- Event creation and publishing.
- Ticket sales management.
- Revenue tracking.
- Attendance scanning via QR codes.
- Goody bag distribution management.
- Analytics and reporting.

### 3. Platform Administrators
**Core Responsibilities:**
- Review organizer applications.
- Verify submitted documents.
- Manage user roles.
- Monitor platform activities.
- Handle revenue distribution.

---

## 🛠 Tech Stack

- **Frontend**: Next.js (App Router)
- **Styling**: Tailwind CSS, Shadcn UI
- **Auth**: Clerk
- **Validation**: Zod
- **State Management**: Zustand
- **Database**: TURSO (edge SQLite)
- **ORM**: Drizzle ORM
- **Hosting**: Vercel (or other edge-first host)

---

## 🌍 Platform Architecture

- **Multitenant Support**:
  - Per-tenant subdomain (e.g. `runforhope.fuiyoo.com`)
  - Logical row-based tenant separation using tenant ID in TURSO
  - Role-based access for tenant admins, organizers, and attendees

---

## ✨ Core Features

### Public User Features
- Event discovery & search
- Ticket purchasing
- QR code e-tickets
- Attendance tracking
- Collection management
- Purchase history

### Organizer Dashboard
- Organization profile
- Document submission
- Event creation wizard
- Ticket management
- QR code scanning
- Revenue tracking
- Distribution management

### Admin Panel
- Organizer verification
- Document review
- User management
- Event moderation
- Revenue analytics

---

## 🎫 Supported Event Types

| Event Type    | Custom Fields |
|---------------|---------------|
| Fun Run       | Category, T-shirt Size, Emergency Contact |
| Concert       | Zone Selection, Seat Type, Price per Zone |
| Seminar       | Preferred Language, Meal Preference       |
| Custom Event  | Dynamic Form Builder (Zod + Zustand)      |

---

## 💰 Monetization

- **Fee Structure**:
  - 6% per ticket or RM2.50 minimum (whichever is higher)
  - 6% SST on the platform's fee
- **Revenue Collection**:
  - Automatically calculated and deducted during checkout
  - Monthly payout to organizers, minus platform fee
- **Optional Premium Add-ons**:
  - Featured event listing
  - Custom branded subdomains
  - Sponsor placement modules

---

## 📈 Success Metrics

- Number of verified organizers
- Organizer verification turnaround time
- Events created per month
- Tickets sold per event type
- Organizer retention rate
- Data accuracy metrics
- Platform uptime
- User satisfaction scores

---

## 🗃 Data Models

### User Profiles
```ts
interface BaseUser {
  id: string;
  email: string;
  name: string;
  role: 'public' | 'organizer' | 'admin';
  createdAt: Date;
}

interface OrganizerProfile extends BaseUser {
  organizationName: string;
  documentType: 'SSM' | 'NGO' | 'Other';
  documentNumber: string;
  documentUrl: string;
  verificationStatus: 'pending' | 'approved' | 'rejected';
  verifiedAt?: Date;
}
```

### Event Registration
```ts
// Example: Fun Run Registration
interface FunRunRegistration {
  id: string;
  userId: string;
  eventId: string;
  category: '3KM' | '5KM' | '10KM';
  tShirtSize: 'XS' | 'S' | 'M' | 'L' | 'XL';
  emergencyContact: string;
  ticketNumber: string;
  qrCode: string;
  attendanceStatus: 'pending' | 'attended';
  collectionStatus: 'pending' | 'collected';
  paymentStatus: 'paid' | 'pending' | 'failed';
  tenantId: string;
}
```