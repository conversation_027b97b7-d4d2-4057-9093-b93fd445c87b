/**
 * Consolidated URL utility functions for the Fuiyoo application
 *
 * This file contains all URL-related utility functions to ensure consistent
 * behavior across different environments (local, staging, production).
 *
 * These functions handle:
 * - Environment detection
 * - Base URL determination
 * - Authentication callback URLs
 * - Cross-environment redirect prevention
 * - Event registration URL detection
 */

/**
 * Get the base URL for the current environment
 * This is the primary function for determining the base URL and should be used
 * in all places where the application needs to know its own URL.
 *
 * @returns The base URL for the current environment with trailing slash
 */
export function getBaseUrl(): string {
  // For client-side, always use the current origin to avoid cross-origin issues
  if (typeof window !== 'undefined') {
    const origin = window.location.origin;
    console.log('Client-side getBaseUrl using window.location.origin:', origin);
    return origin.endsWith('/') ? origin : `${origin}/`;
  }

  // For server-side, first check for explicit environment variables
  let url = process?.env?.NEXT_PUBLIC_SITE_URL ?? '';

  // If no SITE_URL is set, check for Netlify-specific environment variables
  if (!url && process.env.NETLIFY) {
    if (process.env.CONTEXT === 'production' || process.env.BRANCH === 'main' || process.env.BRANCH === 'master') {
      url = 'https://fuiyoo.netlify.app';
      console.log('Using production URL from Netlify environment variables');
    } else if (process.env.CONTEXT === 'deploy-preview' || process.env.CONTEXT === 'branch-deploy' || process.env.BRANCH === 'staging') {
      url = 'https://staging--fuiyoo.netlify.app';
      console.log('Using staging URL from Netlify environment variables');
    }
  }

  // If still no URL, determine based on NODE_ENV
  if (!url) {
    if (process.env.NODE_ENV === 'development') {
      // Use NEXT_PUBLIC_DEV_URL if available, otherwise default to localhost with port
      url = process?.env?.NEXT_PUBLIC_DEV_URL ?? 'http://localhost:3000';
      console.log('Using development URL from NODE_ENV');

      // Check if we're running on a different port (from request headers in middleware)
      if (process?.env?.NEXT_PUBLIC_DYNAMIC_PORT) {
        url = url.replace(/:\d+/, `:${process.env.NEXT_PUBLIC_DYNAMIC_PORT}`);
      }
    } else if (process.env.NODE_ENV === 'production') {
      // In production builds, check if we can determine if this is staging or production
      if (process.env.NETLIFY_CONTEXT === 'staging' || process.env.BRANCH === 'staging') {
        url = 'https://staging--fuiyoo.netlify.app';
        console.log('Using staging URL from production build on staging branch');
      } else {
        url = 'https://fuiyoo.netlify.app'; // Production fallback
        console.log('Using production URL fallback');
      }
    }
  }

  console.log('Server-side getBaseUrl using env vars:', url, 'Environment:', process.env.NODE_ENV);

  // Make sure to include `https://` when not localhost.
  url = url.includes('localhost') ? `http://${url.replace('http://', '')}` : `https://${url.replace('https://', '')}`;

  // Make sure to include a trailing `/`.
  url = url.endsWith('/') ? url : `${url}/`;

  return url;
}

/**
 * Get the absolute URL for a path
 *
 * @param path The path to get the absolute URL for
 * @returns The absolute URL
 */
export function getAbsoluteUrl(path: string): string {
  const baseUrl = getBaseUrl();
  const normalizedPath = path.startsWith('/') ? path.substring(1) : path;
  return `${baseUrl}${normalizedPath}`;
}

/**
 * Get the callback URL for authentication
 * This is the URL that Supabase will redirect to after authentication
 *
 * @returns The callback URL for the current environment
 */
export function getAuthCallbackUrl(): string {
  // For client-side, always use the current origin to avoid cross-origin issues
  if (typeof window !== 'undefined') {
    // In production, always use the production URL to avoid localhost redirects
    if (window.location.hostname !== 'localhost' && process.env.NODE_ENV === 'production') {
      const productionUrl = 'https://fuiyoo.netlify.app/auth/callback';
      console.log('Client-side getAuthCallbackUrl using production URL:', productionUrl);
      return productionUrl;
    }

    const callbackUrl = `${window.location.origin}/auth/callback`;
    console.log('Client-side getAuthCallbackUrl using window.location.origin:', callbackUrl);
    return callbackUrl;
  }

  // For server-side, first check if we have an explicit auth redirect URL configured
  const configuredRedirectUrl = process?.env?.NEXT_PUBLIC_AUTH_REDIRECT_URL;

  if (configuredRedirectUrl) {
    console.log('Using configured auth redirect URL from env:', configuredRedirectUrl);
    return configuredRedirectUrl;
  }

  // If no explicit redirect URL is set, construct it from the base URL
  const baseUrl = getBaseUrl();
  const callbackUrl = `${baseUrl}auth/callback`;
  console.log('Server-side getAuthCallbackUrl:', callbackUrl);
  return callbackUrl;
}

/**
 * Check if a URL is an event registration URL
 *
 * @param url The URL to check
 * @returns True if the URL is an event registration URL, false otherwise
 */
export function isEventRegistrationUrl(url: string): boolean {
  if (!url) return false;

  try {
    // Create a URL object from the string
    const urlObj = new URL(
      url.startsWith('http')
        ? url
        : `${getBaseUrl()}${url.startsWith('/') ? url : `/${url}`}`
    );

    // Check if the pathname matches the event registration pattern
    return urlObj.pathname.match(/^\/events\/[^/]+\/register(\/.*)?$/) !== null;
  } catch (error) {
    console.error('Error checking if URL is an event registration URL:', error);
    return false;
  }
}

/**
 * Get the event ID from an event URL
 *
 * @param url The URL to extract the event ID from
 * @returns The event ID or null if not found
 */
export function getEventIdFromUrl(url: string): string | null {
  if (!url) return null;

  try {
    // Create a URL object from the string
    const urlObj = new URL(
      url.startsWith('http')
        ? url
        : `${getBaseUrl()}${url.startsWith('/') ? url : `/${url}`}`
    );

    // Extract the event ID from the pathname
    const match = urlObj.pathname.match(/^\/events\/([^/]+)(\/.*)?$/);
    return match && match[1] ? match[1] : null;
  } catch (error) {
    console.error('Error extracting event ID from URL:', error);
    return null;
  }
}

/**
 * Detect and fix cross-environment redirects
 * This ensures that redirects stay within the same environment (local, staging, production)
 *
 * @param redirectUrl The URL to check and potentially fix
 * @param currentUrl The current URL (used to determine the current environment)
 * @returns A fixed URL that stays within the current environment
 */
export function fixCrossEnvironmentRedirect(redirectUrl: string, currentUrl: string): string {
  console.log('fixCrossEnvironmentRedirect input:', { redirectUrl, currentUrl });

  // If the redirect URL is not absolute, it's already relative to the current environment
  if (!redirectUrl.startsWith('http')) {
    return redirectUrl;
  }

  try {
    // Parse the URLs
    const redirectUrlObj = new URL(redirectUrl);
    const currentUrlObj = new URL(currentUrl);

    console.log('Parsed URLs:', {
      redirectOrigin: redirectUrlObj.origin,
      currentOrigin: currentUrlObj.origin
    });

    // Check if the origins match (same environment)
    if (redirectUrlObj.origin === currentUrlObj.origin) {
      console.log('Origins match, no fix needed');
      return redirectUrl; // Already in the same environment
    }

    // Always force the redirect to stay in the current environment
    // This is a more aggressive approach that ensures redirects never cross environments
    console.warn(`Cross-environment redirect detected: from ${currentUrlObj.origin} to ${redirectUrlObj.origin}`);
    console.warn('Forcing redirect to stay in current environment');

    // Extract the path and query from the redirect URL
    const pathWithSearch = redirectUrlObj.pathname + redirectUrlObj.search;

    // Create a new URL using the current origin
    const fixedUrl = new URL(pathWithSearch, currentUrlObj.origin).toString();
    console.log('Fixed URL:', fixedUrl);
    return fixedUrl;
  } catch (error) {
    console.error('Error fixing cross-environment redirect:', error);
    return redirectUrl; // Return the original URL if there's an error
  }
}

/**
 * Detect the current environment (development, staging, production)
 * This is useful for environment-specific logic
 *
 * @returns The current environment as a string
 */
export function detectEnvironment(): 'development' | 'staging' | 'production' {
  // Client-side detection
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'development';
    } else if (hostname.includes('staging') || hostname.includes('preview')) {
      return 'staging';
    } else {
      return 'production';
    }
  }

  // Server-side detection
  if (process.env.NODE_ENV === 'development') {
    return 'development';
  } else if (
    process.env.NETLIFY_CONTEXT === 'staging' ||
    process.env.BRANCH === 'staging' ||
    process.env.NEXT_PUBLIC_SITE_URL?.includes('staging')
  ) {
    return 'staging';
  } else {
    return 'production';
  }
}

/**
 * Get the environment-specific URL for a given path
 * This ensures URLs are always correct for the current environment
 *
 * @param path The path to get the URL for
 * @param env Optional environment to override detection
 * @returns The full URL for the specified path in the current environment
 */
export function getEnvironmentUrl(
  path: string,
  env?: 'development' | 'staging' | 'production'
): string {
  const environment = env || detectEnvironment();
  let baseUrl: string;

  switch (environment) {
    case 'development':
      baseUrl = 'http://localhost:3000';
      break;
    case 'staging':
      baseUrl = 'https://staging--fuiyoo.netlify.app';
      break;
    case 'production':
      baseUrl = 'https://fuiyoo.netlify.app';
      break;
  }

  // Normalize the path
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;

  return `${baseUrl}${normalizedPath}`;
}

// For backward compatibility, export aliases of the functions
export const getURL = getBaseUrl;
export const getEnvironmentURL = getBaseUrl;
export const getAuthCallbackURL = getAuthCallbackUrl;
