export enum MalaysiaState {
  Johor = 'Johor',
  Kedah = 'Kedah',
  Kelantan = 'Kelantan',
  Melaka = 'Melaka',
  NegeriSembilan = 'Negeri Sembilan',
  Pa<PERSON> = 'Pa<PERSON>',
  Perak = 'Perak',
  <PERSON>lis = 'Perlis',
  PulauPinang = 'Pulau Pinang',
  Sabah = 'Sabah',
  Sarawak = 'Sarawak',
  Selangor = 'Selangor',
  Terengganu = 'Terengganu',
  WilayahPersekutuanKualaLumpur = 'Wilayah Persekutuan Kuala Lumpur',
  WilayahPersekutuanLabuan = 'Wilayah Persekutuan Labuan',
  WilayahPersekutuanPutrajaya = 'Wilayah Persekutuan Putrajaya'
}

export enum EventCategory {
  Sports = 'Sports',
  Wellness = 'Wellness',
  Finance = 'Finance',
  Charity = 'Charity',
  Entertainment = 'Entertainment',
  Education = 'Education',
  Technology = 'Technology',
  Business = 'Business',
  Arts = 'Arts',
  Other = 'Other'
}

export interface Event {
  id: string;
  slug: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  state: MalaysiaState;
  category: EventCategory;
  organizer: string;
  coverImage: string;
  posterImage?: string;
  galleryImages: string[];
  price?: string;
  ticketUrl?: string;
  contactEmail?: string;
  contactPhone?: string;
  featured?: boolean;
} 