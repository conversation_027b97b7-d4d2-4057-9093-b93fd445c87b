'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import Link from 'next/link'
import { resetPassword } from '@/lib/supabase/auth'

/**
 * Reset Password Form Component
 *
 * This component allows users to request a password reset link
 * by entering their email address.
 */
export default function ResetPasswordForm() {
  const [email, setEmail] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)

  /**
   * Handle form submission
   * @param e Form event
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Basic email validation
    if (!email || !email.includes('@')) {
      setError('Please enter a valid email address')
      setLoading(false)
      return
    }

    try {
      // Use the auth utility function to reset the password
      await resetPassword(email)
      setSuccess(true)
    } catch (err: any) {
      setError(err.message || 'An error occurred while resetting your password')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="w-full max-w-md mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Check your email</h1>
          <p className="text-muted-foreground mt-2">
            We&apos;ve sent a password reset link to <strong>{email}</strong>.
            Please check your email and click the link to reset your password.
          </p>
        </div>

        <Alert className="mt-4">
          <AlertDescription>
            If you don&apos;t see the email in your inbox, please check your spam folder.
            The link will expire in 24 hours.
          </AlertDescription>
        </Alert>

        <div className="flex flex-col space-y-2 mt-6">
          <Button
            className="w-full"
            onClick={() => setSuccess(false)}
          >
            Back to Reset Password
          </Button>

          <Button
            variant="outline"
            className="w-full"
            onClick={() => window.location.href = '/sign-in'}
          >
            Return to Sign In
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Reset Password</h1>
        <p className="text-muted-foreground mt-2">
          Enter your email address and we&apos;ll send you a link to reset your password
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="text-foreground">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={loading}
            aria-describedby="email-description"
            className="bg-background"
          />
          <p id="email-description" className="text-xs text-muted-foreground">
            Enter the email address associated with your account
          </p>
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={loading}
          aria-label={loading ? "Sending reset link..." : "Send password reset link"}
        >
          {loading ? 'Sending reset link...' : 'Send Reset Link'}
        </Button>
      </form>

      <div className="text-center text-sm mt-6">
        Remember your password?{' '}
        <Link href="/sign-in" className="text-primary hover:underline">
          Sign in
        </Link>
      </div>
    </div>
  )
}
