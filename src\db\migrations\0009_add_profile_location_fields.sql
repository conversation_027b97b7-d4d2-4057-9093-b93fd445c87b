-- Add location-related fields to users table
ALTER TABLE users
ADD COLUMN IF NOT EXISTS nationality TEXT,
ADD COLUMN IF NOT EXISTS country TEXT,
ADD COLUMN IF NOT EXISTS state_province TEXT;

-- Add indices for faster lookups
CREATE INDEX IF NOT EXISTS users_nationality_idx ON users(nationality);
CREATE INDEX IF NOT EXISTS users_country_idx ON users(country);
CREATE INDEX IF NOT EXISTS users_state_province_idx ON users(state_province);

-- Add comments for documentation
COMMENT ON COLUMN users.nationality IS 'User''s nationality based on ISO 3166-1';
COMMENT ON COLUMN users.country IS 'User''s country of residence based on ISO 3166-1';
COMMENT ON COLUMN users.state_province IS 'User''s state or province of residence'; 