export default function AnalyticsPage() {
  return (
    <div className="container mx-auto px-4">
      <h1 className="text-2xl font-semibold mb-6">Analytics Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Events Overview */}
        <div className="p-6 bg-card rounded-lg shadow border border-border">
          <h2 className="text-xl font-semibold mb-4">Events Overview</h2>
          <div className="space-y-2">
            <p className="text-muted-foreground">Total Events: <span className="font-semibold text-foreground">0</span></p>
            <p className="text-muted-foreground">Active Events: <span className="font-semibold text-foreground">0</span></p>
            <p className="text-muted-foreground">Completed Events: <span className="font-semibold text-foreground">0</span></p>
          </div>
        </div>

        {/* User Statistics */}
        <div className="p-6 bg-card rounded-lg shadow border border-border">
          <h2 className="text-xl font-semibold mb-4">User Statistics</h2>
          <div className="space-y-2">
            <p className="text-muted-foreground">Total Users: <span className="font-semibold text-foreground">0</span></p>
            <p className="text-muted-foreground">Event Organizers: <span className="font-semibold text-foreground">0</span></p>
            <p className="text-muted-foreground">Active Today: <span className="font-semibold text-foreground">0</span></p>
          </div>
        </div>

        {/* Revenue Overview */}
        <div className="p-6 bg-card rounded-lg shadow border border-border">
          <h2 className="text-xl font-semibold mb-4">Revenue Overview</h2>
          <div className="space-y-2">
            <p className="text-muted-foreground">Total Revenue: <span className="font-semibold text-foreground">$0</span></p>
            <p className="text-muted-foreground">This Month: <span className="font-semibold text-foreground">$0</span></p>
            <p className="text-muted-foreground">Last Month: <span className="font-semibold text-foreground">$0</span></p>
          </div>
        </div>
      </div>
    </div>
  );
}