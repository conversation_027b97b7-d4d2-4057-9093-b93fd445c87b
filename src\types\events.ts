export enum EventStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export interface Event {
  id: string;
  title: string;
  description?: string;
  organizerId: string;
  organizerName: string;
  status: EventStatus;
  startDate: string;
  endDate: string;
  venue: string;
  capacity: number;
  ticketsSold: number;
  ticketPrice?: number;
  createdAt: string;
  updatedAt?: string;
  publishedAt?: string;
  categories?: string[];
  tags?: string[];
  images?: string[];
}

export interface EventTicket {
  id: string;
  eventId: string;
  type: string;
  price: number;
  quantity: number;
  quantitySold: number;
  description?: string;
  saleStartDate?: string;
  saleEndDate?: string;
}

export interface EventAnalytics {
  eventId: string;
  views: number;
  uniqueViews: number;
  ticketsSold: number;
  revenue: number;
  refunds: number;
  attendees: number;
  ratings: number;
  averageRating: number;
} 