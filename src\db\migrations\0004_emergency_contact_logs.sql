-- Create emergency_contact_access_logs table
CREATE TABLE IF NOT EXISTS emergency_contact_access_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL,
  event_id TEXT NOT NULL,
  registration_id TEXT NOT NULL,
  accessed_by TEXT NOT NULL,
  accessed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  reason TEXT NOT NULL,
  ip_address TEXT,
  user_agent TEXT,
  CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_accessor FOREIGN KEY (accessed_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS emergency_access_user_id_idx ON emergency_contact_access_logs(user_id);
CREATE INDEX IF NOT EXISTS emergency_access_event_id_idx ON emergency_contact_access_logs(event_id);
CREATE INDEX IF NOT EXISTS emergency_access_accessor_idx ON emergency_contact_access_logs(accessed_by);
CREATE INDEX IF NOT EXISTS emergency_access_accessed_at_idx ON emergency_contact_access_logs(accessed_at);

-- Add comment for documentation
COMMENT ON TABLE emergency_contact_access_logs IS 'Logs access to emergency contact information for GDPR compliance';

-- Create a trigger function to notify users when their emergency contacts are accessed
CREATE OR REPLACE FUNCTION notify_emergency_contact_access()
RETURNS TRIGGER AS $$
BEGIN
  -- Here we could integrate with a notification system
  -- This is a placeholder for future notification functionality
  -- For now, we just log the access
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on the logs table
CREATE TRIGGER emergency_contact_access_trigger
AFTER INSERT ON emergency_contact_access_logs
FOR EACH ROW
EXECUTE FUNCTION notify_emergency_contact_access(); 