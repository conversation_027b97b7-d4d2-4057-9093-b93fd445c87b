-- Description: Create organization applications table
-- Created at: 2023-11-15

-- Create organization_applications table
CREATE TABLE IF NOT EXISTS public.organization_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id),
    status VARCHAR(255) NOT NULL DEFAULT 'draft',
    data JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES public.users(id),
    rejection_reason TEXT
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_organization_applications_user_id ON public.organization_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_organization_applications_status ON public.organization_applications(status);

-- Add comment
COMMENT ON TABLE public.organization_applications IS 'Stores applications from users who want to become event organizers';

-- Update the RLS policy
ALTER TABLE public.organization_applications ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own applications
CREATE POLICY user_select_own_applications ON public.organization_applications
    FOR SELECT
    USING (auth.uid() = user_id);

-- Policy for users to insert their own applications
CREATE POLICY user_insert_own_applications ON public.organization_applications
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own applications with status 'draft'
CREATE POLICY user_update_own_draft_applications ON public.organization_applications
    FOR UPDATE
    USING (auth.uid() = user_id AND status = 'draft')
    WITH CHECK (auth.uid() = user_id AND status = 'draft');

-- Policy for admins to view all applications
CREATE POLICY admin_select_all_applications ON public.organization_applications
    FOR SELECT
    USING (auth.jwt() ? 'role' AND (auth.jwt()->>'role' = 'admin' OR auth.jwt()->>'role' = 'super_admin'));

-- Policy for admins to update any application
CREATE POLICY admin_update_all_applications ON public.organization_applications
    FOR UPDATE
    USING (auth.jwt() ? 'role' AND (auth.jwt()->>'role' = 'admin' OR auth.jwt()->>'role' = 'super_admin')); 