/**
 * Templates for different event types to pre-populate category structures
 */

/**
 * Running event category template
 */
export const runningEventCategoryTemplate = {
  properties: {
    distance: 0,
    bibPrefix: '',
    bibStartNumber: '1001',
    bibRequireGeneration: true,
    registrationOpen: true,
    registrationCount: 0,
  }
};

/**
 * Conference event category template
 */
export const conferenceEventCategoryTemplate = {
  properties: {
    venue: '',
    sessionDuration: 60,
    maxAttendees: 100,
    requiresRegistration: true,
  }
};

/**
 * Workshop event category template
 */
export const workshopEventCategoryTemplate = {
  properties: {
    venue: '',
    instructor: '',
    duration: 120, // minutes
    skillLevel: 'Beginner',
    materials: '',
    maxAttendees: 30,
  }
};

/**
 * Seminar event category template
 */
export const seminarEventCategoryTemplate = {
  properties: {
    topic: '',
    speaker: '',
    duration: 90, // minutes
    targetAudience: '',
    maxAttendees: 50,
  }
};

/**
 * Webinar event category template
 */
export const webinarEventCategoryTemplate = {
  properties: {
    platform: '',
    presenter: '',
    duration: 60, // minutes
    requiresRegistration: true,
    maxAttendees: 500,
  }
};

/**
 * Event type templates mapping
 */
export const EVENT_TYPE_TEMPLATES = {
  'running': {
    baseFields: {
      hasCategories: true,
      requiresLocation: true,
      requiresDate: true,
    },
    categoryTemplate: runningEventCategoryTemplate
  },
  'conference': {
    baseFields: {
      hasCategories: true,
      requiresLocation: true,
      requiresDate: true,
    },
    categoryTemplate: conferenceEventCategoryTemplate
  },
  'workshop': {
    baseFields: {
      hasCategories: true,
      requiresLocation: true,
      requiresDate: true,
    },
    categoryTemplate: workshopEventCategoryTemplate
  },
  'seminar': {
    baseFields: {
      hasCategories: true,
      requiresLocation: true,
      requiresDate: true,
    },
    categoryTemplate: seminarEventCategoryTemplate
  },
  'webinar': {
    baseFields: {
      hasCategories: false,
      requiresLocation: false,
      requiresDate: true,
    },
    categoryTemplate: webinarEventCategoryTemplate
  }
};

/**
 * Get a category template for a specific event type
 */
export function getCategoryTemplateForEventType(eventType: string) {
  const template = EVENT_TYPE_TEMPLATES[eventType as keyof typeof EVENT_TYPE_TEMPLATES];
  if (!template) {
    // Return a default empty template if event type not found
    return {
      properties: {}
    };
  }
  return template.categoryTemplate;
}
