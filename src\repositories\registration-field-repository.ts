import { BaseRepository } from '@/lib/db/base-repository';
import { z } from 'zod';
import { RegistrationField, registrationFieldSchema, FieldType } from '@/types/event-types';

/**
 * Registration Field database schema for the repository
 */
const RegistrationFieldDBSchema = z.object({
  id: z.string().uuid(),
  event_id: z.string().uuid(),
  field_id: z.string(),
  field_type: z.string(),
  label: z.string(),
  description: z.string().nullable(),
  is_required: z.boolean(),
  is_public: z.boolean(),
  validation_rules: z.any().nullable(),
  default_value: z.any().nullable(),
  options: z.any().nullable(),
  order_index: z.number().int(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

/**
 * Type for the RegistrationField database model
 */
type RegistrationFieldDB = z.infer<typeof RegistrationFieldDBSchema>;

/**
 * Repository for registration field operations
 */
export class RegistrationFieldRepository extends BaseRepository<RegistrationFieldDB> {
  constructor() {
    super('registration_fields', RegistrationFieldDBSchema);
  }

  /**
   * Transform the database registration field to the application registration field
   */
  private toRegistrationField(dbField: RegistrationFieldDB): RegistrationField {
    return {
      id: dbField.id,
      eventId: dbField.event_id,
      fieldId: dbField.field_id,
      fieldType: dbField.field_type as FieldType,
      label: dbField.label,
      description: dbField.description || "",
      isRequired: dbField.is_required,
      isPublic: dbField.is_public,
      validationRules: dbField.validation_rules || null,
      defaultValue: dbField.default_value || null,
      options: dbField.options || null,
      orderIndex: dbField.order_index,
      createdAt: new Date(dbField.created_at),
      updatedAt: new Date(dbField.updated_at),
    };
  }

  /**
   * Transform the application registration field to the database registration field
   */
  private toRegistrationFieldDB(field: Partial<RegistrationField>): Partial<RegistrationFieldDB> {
    const result: Partial<RegistrationFieldDB> = {};

    if (field.eventId !== undefined) result.event_id = field.eventId;
    if (field.fieldId !== undefined) result.field_id = field.fieldId;
    if (field.fieldType !== undefined) result.field_type = field.fieldType;
    if (field.label !== undefined) result.label = field.label;
    if (field.description !== undefined) result.description = field.description || null;
    if (field.isRequired !== undefined) result.is_required = field.isRequired;
    if (field.isPublic !== undefined) result.is_public = field.isPublic;
    if (field.validationRules !== undefined) result.validation_rules = field.validationRules || null;
    if (field.defaultValue !== undefined) result.default_value = field.defaultValue || null;
    if (field.options !== undefined) result.options = field.options || null;
    if (field.orderIndex !== undefined) result.order_index = field.orderIndex;

    return result;
  }

  /**
   * Get all fields for an event
   */
  async getFieldsByEventId(eventId: string): Promise<RegistrationField[]> {
    const result = await this.find({ event_id: eventId });

    if (!result.success) {
      throw new Error(`Failed to get registration fields: ${result.message}`);
    }

    return (result.data || [])
      .map(this.toRegistrationField)
      .sort((a, b) => a.orderIndex - b.orderIndex);
  }

  /**
   * Get field by ID
   */
  async getFieldById(id: string): Promise<RegistrationField | null> {
    const result = await this.findById(id);

    if (!result.success) {
      if (result.message?.includes('not found')) {
        return null;
      }
      throw new Error(`Failed to get registration field: ${result.message}`);
    }

    return this.toRegistrationField(result.data!);
  }

  /**
   * Get field by event ID and field ID
   */
  async getFieldByEventAndFieldId(eventId: string, fieldId: string): Promise<RegistrationField | null> {
    const result = await this.find({ event_id: eventId, field_id: fieldId });

    if (!result.success) {
      throw new Error(`Failed to get registration field: ${result.message}`);
    }

    if (!result.data || result.data.length === 0) {
      return null;
    }

    if (result.data && result.data.length > 0 && result.data[0]) {
      return this.toRegistrationField(result.data[0]);
    }
    return null;
  }

  /**
   * Create a new field
   */
  async createField(field: Omit<RegistrationField, 'id' | 'createdAt' | 'updatedAt'>): Promise<RegistrationField> {
    // Validate the field
    registrationFieldSchema.parse(field);

    const result = await this.create({
      id: crypto.randomUUID(),
      event_id: field.eventId,
      field_id: field.fieldId,
      field_type: field.fieldType,
      label: field.label,
      description: field.description || null,
      is_required: field.isRequired,
      is_public: field.isPublic,
      validation_rules: field.validationRules || null,
      default_value: field.defaultValue || null,
      options: field.options || null,
      order_index: field.orderIndex,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    if (!result.success) {
      throw new Error(`Failed to create registration field: ${result.message}`);
    }

    return this.toRegistrationField(result.data!);
  }

  /**
   * Update a field
   */
  async updateField(id: string, field: Partial<Omit<RegistrationField, 'id' | 'createdAt' | 'updatedAt'>>): Promise<RegistrationField> {
    // Prepare the data to update
    const dbField = this.toRegistrationFieldDB(field);

    // Always update the updated_at timestamp
    dbField.updated_at = new Date().toISOString();

    const result = await this.update(id, dbField);

    if (!result.success) {
      throw new Error(`Failed to update registration field: ${result.message}`);
    }

    return this.toRegistrationField(result.data!);
  }

  /**
   * Delete a field
   */
  async deleteField(id: string): Promise<void> {
    const result = await this.delete(id);

    if (!result.success) {
      throw new Error(`Failed to delete registration field: ${result.message}`);
    }
  }

  /**
   * Create or update multiple fields for an event
   */
  async createOrUpdateBulkFields(eventId: string, fields: Omit<RegistrationField, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<RegistrationField[]> {
    const results: RegistrationField[] = [];

    // Process each field
    for (const field of fields) {
      // Check if the field already exists
      const existingField = await this.getFieldByEventAndFieldId(eventId, field.fieldId);

      if (existingField) {
        // Update existing field
        const updated = await this.updateField(existingField.id, {
          ...field,
          eventId // Ensure eventId is set
        });
        results.push(updated);
      } else {
        // Create new field
        const created = await this.createField({
          ...field,
          eventId // Ensure eventId is set
        });
        results.push(created);
      }
    }

    return results;
  }

  /**
   * Delete all fields for an event
   */
  async deleteAllFieldsForEvent(eventId: string): Promise<void> {
    const fields = await this.getFieldsByEventId(eventId);

    for (const field of fields) {
      await this.deleteField(field.id);
    }
  }
}