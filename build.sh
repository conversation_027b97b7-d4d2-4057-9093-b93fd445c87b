#!/bin/bash

# This script provides a unified way to build the Next.js application
# with different options (standard webpack or Turbopack)

# Parse command line options
USE_TURBO=false
FORCE_BUILD=false
SKIP_STATIC_GEN=false
SKIP_PREFLIGHT=false

while [[ "$#" -gt 0 ]]; do
  case $1 in
    --turbo) USE_TURBO=true ;;
    --force) FORCE_BUILD=true ;;
    --skip-static) SKIP_STATIC_GEN=true ;;
    --skip-preflight) SKIP_PREFLIGHT=true ;;
    --clean) rm -rf .next ;;
    *) echo "Unknown parameter: $1"; exit 1 ;;
  esac
  shift
done

# Set common environment variables
export NODE_OPTIONS="--max-old-space-size=4096 --enable-source-maps"
export NEXT_TELEMETRY_DISABLED=1

# Apply conditional environment variables and build flags
BUILD_CMD="next build"

if [ "$USE_TURBO" = true ]; then
  export NEXT_TURBO=1
  BUILD_CMD="${BUILD_CMD} --turbo"
  echo "Building Next.js with Turbopack..."
else
  echo "Building Next.js with standard webpack..."
fi

if [ "$FORCE_BUILD" = true ]; then
  BUILD_CMD="${BUILD_CMD} --force"
fi

if [ "$SKIP_STATIC_GEN" = true ]; then
  BUILD_CMD="${BUILD_CMD} --experimental-skip-static-generation"
fi

if [ "$SKIP_PREFLIGHT" = true ]; then
  export NEXT_SKIP_PREFLIGHT_CHECK=true
fi

# Execute the build command
echo "Executing: $BUILD_CMD"
pnpm $BUILD_CMD