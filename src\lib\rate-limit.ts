// Simple rate limiter for API routes
// Implements a token bucket algorithm

type RateLimitOptions = {
  // Maximum number of requests allowed in the time window
  limit: number;

  // Time window in seconds
  window: number;

  // Optional identifier function to get a unique key for the request
  // If not provided, IP address will be used
  identifier?: (req: Request) => string | Promise<string>;
};

// Store for rate limit data
const rateLimitStore = new Map<string, { tokens: number; lastRefill: number }>();

// Clean up the store periodically
setInterval(() => {
  const now = Date.now();
  // Use Array.from to create an array of entries for compatibility
  Array.from(rateLimitStore.entries()).forEach(([key, data]) => {
    // Remove entries older than 1 hour
    if (now - data.lastRefill > 3600 * 1000) {
      rateLimitStore.delete(key);
    }
  });
}, 60 * 1000); // Run every minute

/**
 * Rate limiter middleware function
 */
export async function rateLimit(
  req: Request,
  options: RateLimitOptions
): Promise<{ success: boolean; remaining: number; resetAt: Date }> {
  // Get client identifier
  let id: string;
  if (options.identifier) {
    id = await Promise.resolve(options.identifier(req));
  } else {
    // Default to IP-based limiting
    const forwardedFor = req.headers.get('x-forwarded-for') || '';
    const firstIp = forwardedFor.split(',')[0];
    const ip = firstIp ? firstIp.trim() : '127.0.0.1';
    id = `${ip}:${req.method}:${new URL(req.url).pathname}`;
  }

  const now = Date.now();
  const windowMs = options.window * 1000;

  // Get or create bucket
  let bucket = rateLimitStore.get(id);
  if (!bucket) {
    bucket = { tokens: options.limit, lastRefill: now };
    rateLimitStore.set(id, bucket);
  }

  // Refill tokens based on time elapsed
  const timeElapsed = now - bucket.lastRefill;
  const tokensToAdd = Math.floor(timeElapsed / windowMs * options.limit);

  if (tokensToAdd > 0) {
    bucket.tokens = Math.min(options.limit, bucket.tokens + tokensToAdd);
    bucket.lastRefill = now;
  }

  // Check if we have tokens available
  if (bucket.tokens > 0) {
    bucket.tokens -= 1;
    return {
      success: true,
      remaining: bucket.tokens,
      resetAt: new Date(bucket.lastRefill + windowMs)
    };
  }

  // Calculate time until reset
  const resetAt = new Date(bucket.lastRefill + windowMs);

  return {
    success: false,
    remaining: 0,
    resetAt
  };
}

/**
 * Apply rate limiting to a request
 */
export async function applyRateLimit(
  req: Request,
  options: RateLimitOptions
): Promise<Response | null> {
  const result = await rateLimit(req, options);

  if (!result.success) {
    return new Response(
      JSON.stringify({
        error: 'Too Many Requests',
        message: `Rate limit exceeded. Try again after ${result.resetAt.toISOString()}`,
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': options.limit.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.ceil(result.resetAt.getTime() / 1000).toString(),
          'Retry-After': Math.ceil((result.resetAt.getTime() - Date.now()) / 1000).toString(),
        },
      }
    );
  }

  // Add rate limit headers
  const responseHeaders = new Headers();
  responseHeaders.set('X-RateLimit-Limit', options.limit.toString());
  responseHeaders.set('X-RateLimit-Remaining', result.remaining.toString());
  responseHeaders.set('X-RateLimit-Reset', Math.ceil(result.resetAt.getTime() / 1000).toString());

  return null;
}