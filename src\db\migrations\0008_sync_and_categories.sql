-- Add last_clerk_sync column to users table
ALTER TABLE users 
  ADD COLUMN IF NOT EXISTS last_clerk_sync TIMESTAMP WITH TIME ZONE;

-- Create event_categories table if not exists
CREATE TABLE IF NOT EXISTS event_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add default categories
INSERT INTO event_categories (name, description) VALUES
  ('Sports', 'Sports and athletic activities'),
  ('Education', 'Educational and learning events'),
  ('Technology', 'Technology and digital events'),
  ('Arts', 'Arts and cultural events'),
  ('Business', 'Business and professional events'),
  ('Social', 'Social and networking events'),
  ('Health', 'Health and wellness events'),
  ('Environment', 'Environmental and sustainability events')
ON CONFLICT (name) DO NOTHING;

-- Add event_categories column to users table if not exists
ALTER TABLE users 
  ADD COLUMN IF NOT EXISTS event_categories JSONB DEFAULT '[]'::jsonb; 