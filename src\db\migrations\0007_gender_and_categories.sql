-- Create event_categories table
CREATE TABLE IF NOT EXISTS event_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add default categories
INSERT INTO event_categories (name, description) VALUES
  ('Sports', 'Sports and athletic activities'),
  ('Education', 'Educational and learning events'),
  ('Technology', 'Technology and digital events'),
  ('Arts', 'Arts and cultural events'),
  ('Business', 'Business and professional events'),
  ('Social', 'Social and networking events'),
  ('Health', 'Health and wellness events'),
  ('Environment', 'Environmental and sustainability events')
ON CONFLICT (name) DO NOTHING;

-- Add check constraint to gender column
ALTER TABLE users
  ADD CONSTRAINT gender_check
  CHECK (gender IN ('male', 'female'));

-- Last sync timestamp column for external services
ALTER TABLE users
  ADD COLUMN IF NOT EXISTS last_sync TIMESTAMP WITH TIME ZONE;