-- Fix emergency_contact_settings column in events table
-- Migration: 0018_fix_emergency_contact_settings.sql

-- Start transaction for atomic migration
BEGIN;

-- Drop and recreate the emergency_contact_settings column with proper defaults
ALTER TABLE events 
  DROP COLUMN IF EXISTS emergency_contact_settings,
  ADD COLUMN emergency_contact_settings JSONB DEFAULT '{
    "required": false,
    "fields": ["name", "phone", "relationship"],
    "allowSameForMultipleRegistrations": true
  }'::jsonb;

-- Add comment for documentation
COMMENT ON COLUMN events.emergency_contact_settings IS 'JSON configuration for emergency contact requirements for this event';

-- Force schema cache reload
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('0018_fix_emergency_contact_settings', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit transaction
COMMIT; 