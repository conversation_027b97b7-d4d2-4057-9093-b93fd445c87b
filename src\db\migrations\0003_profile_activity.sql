-- Create profile_activity table for activity tracking and versioning
CREATE TABLE IF NOT EXISTS profile_activity (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL,
  activity_type TEXT NOT NULL,
  activity_description TEXT NOT NULL,
  previous_value JSONB,
  new_value JSONB,
  field_name TEXT,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Add indices for faster lookups
CREATE INDEX IF NOT EXISTS profile_activity_user_id_idx ON profile_activity(user_id);
CREATE INDEX IF NOT EXISTS profile_activity_type_idx ON profile_activity(activity_type);
CREATE INDEX IF NOT EXISTS profile_activity_created_at_idx ON profile_activity(created_at);

-- <PERSON>reate helper function to record profile changes
CREATE OR REPLACE FUNCTION log_profile_change()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO profile_activity (
    user_id, 
    activity_type, 
    activity_description,
    previous_value, 
    new_value
  ) VALUES (
    NEW.id,
    'profile_update',
    'Profile information updated',
    row_to_json(OLD)::jsonb,
    row_to_json(NEW)::jsonb
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on users table to record changes
DROP TRIGGER IF EXISTS profile_update_trigger ON users;
CREATE TRIGGER profile_update_trigger
AFTER UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION log_profile_change();

-- Add comment for documentation
COMMENT ON TABLE profile_activity IS 'Stores user profile activity for change tracking and auditing'; 