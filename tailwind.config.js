/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  // Note: For Tailwind CSS v4, we directly use bracket notation for custom properties in the CSS
  // instead of defining them in the theme config
  theme: {},
  plugins: [], // tailwindcss-animate is no longer needed as a plugin
} 