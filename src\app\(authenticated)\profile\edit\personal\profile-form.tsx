'use client';

import { useCallback, useState, memo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { getStates, type State } from '@/data/countries';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';

interface SelectOption {
  value: string;
  label: string;
  icon?: string;
}

const profileSchema = z.object({
  nationality: z.string().min(1, "Please select a nationality"),
  country: z.string().min(1, "Please select a country"),
  state_province: z.string().optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

interface ProfileFormProps {
  countryOptions: SelectOption[];
}

// Create a memoized component for better performance
const MemoizedSelect = memo(({
  options,
  value,
  onChange,
  placeholder,
  disabled
}: {
  options: SelectOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  disabled?: boolean;
}) => (
  <Select disabled={disabled || false} value={value} onValueChange={onChange}>
    <SelectTrigger className="w-full">
      <SelectValue placeholder={placeholder}>
        {value && options.find(opt => opt.value === value) && (
          <div className="flex items-center">
            {options.find(opt => opt.value === value)?.icon && (
              <span className="mr-2">{options.find(opt => opt.value === value)?.icon}</span>
            )}
            {options.find(opt => opt.value === value)?.label}
          </div>
        )}
      </SelectValue>
    </SelectTrigger>
    <SelectContent>
      {options.map((option) => (
        <SelectItem key={option.value} value={option.value}>
          {option.icon && <span className="mr-2">{option.icon}</span>}
          {option.label}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
));

MemoizedSelect.displayName = 'MemoizedSelect';

export function ProfileForm({ countryOptions }: ProfileFormProps) {
  const [states, setStates] = useState<SelectOption[]>([]);
  const [isLoadingStates, setIsLoadingStates] = useState(false);

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      nationality: "",
      country: "",
      state_province: "",
    }
  });

  const onCountryChange = useCallback(async (value: string) => {
    if (!value) {
      setStates([]);
      form.setValue('state_province', "");
      return;
    }

    setIsLoadingStates(true);
    try {
      const statesList = await getStates(value);
      setStates(statesList.map(s => ({
        value: s.code,
        label: s.name
      })));
    } catch (error) {
      console.error('Failed to load states:', error);
      toast.error('Failed to load states. Please try again.');
    } finally {
      setIsLoadingStates(false);
    }
  }, [form]);

  const onSubmit = async (data: ProfileFormData) => {
    try {
      const response = await fetch('/api/profile', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          nationality: data.nationality,
          country: data.country,
          state_province: data.state_province,
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Update error:', error);
      toast.error('Failed to update profile. Please try again.');
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
          <FormField
            control={form.control}
            name="nationality"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nationality</FormLabel>
                <FormControl>
                  <MemoizedSelect
                    options={countryOptions}
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select nationality..."
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="country"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Country of Residence</FormLabel>
                <FormControl>
                  <MemoizedSelect
                    options={countryOptions}
                    value={field.value}
                    onChange={(value) => {
                      field.onChange(value);
                      onCountryChange(value);
                    }}
                    placeholder="Select country..."
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="state_province"
            render={({ field }) => (
              <FormItem>
                <FormLabel>State/Province</FormLabel>
                <FormControl>
                  <MemoizedSelect
                    options={states}
                    value={field.value || ""}
                    onChange={field.onChange}
                    placeholder={isLoadingStates ? "Loading states..." : "Select state/province..."}
                    disabled={!form.watch('country') || isLoadingStates}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button
          type="submit"
          disabled={form.formState.isSubmitting}
          className="mt-4"
        >
          {form.formState.isSubmitting ? 'Saving...' : 'Save Changes'}
        </Button>
      </form>
    </Form>
  );
}