-- Enable RLS on privacy tables
ALTER TABLE privacy_consents ENABLE ROW LEVEL SECURITY;
ALTER TABLE privacy_consent_versions ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own consents
CREATE POLICY "Users can view their own consents"
  ON privacy_consents
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Policy for users to create their own consents
CREATE POLICY "Users can create their own consents"
  ON privacy_consents
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own consents
CREATE POLICY "Users can update their own consents"
  ON privacy_consents
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policy for users to view consent versions
CREATE POLICY "Users can view consent versions"
  ON privacy_consent_versions
  FOR SELECT
  TO authenticated
  USING (true);

-- Grant permissions to authenticated users
GRANT SELECT ON privacy_consents TO authenticated;
GRANT INSERT ON privacy_consents TO authenticated;
GRANT UPDATE ON privacy_consents TO authenticated;
GRANT SELECT ON privacy_consent_versions TO authenticated; 