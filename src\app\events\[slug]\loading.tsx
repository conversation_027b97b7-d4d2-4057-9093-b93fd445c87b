import { Skeleton } from '@/components/ui/skeleton';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';

export default function EventDetailLoading() {
  return (
    <main className="min-h-screen pb-16">
      {/* Cover banner skeleton */}
      <div className="relative w-full h-[300px] md:h-[400px] bg-gray-200">
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <div className="container max-w-5xl mx-auto">
            <Skeleton className="h-6 w-24 mb-3" />
            <Skeleton className="h-10 w-3/4 mb-4" />
            <div className="flex flex-wrap gap-4">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-5 w-28" />
              <Skeleton className="h-5 w-40" />
              <Skeleton className="h-5 w-32" />
            </div>
          </div>
        </div>
      </div>

      <div className="container max-w-5xl mx-auto px-4 mt-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2 space-y-8">
            {/* Event description skeleton */}
            <div>
              <Skeleton className="h-8 w-48 mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </div>

            {/* Event categories skeleton */}
            <div>
              <Skeleton className="h-8 w-48 mb-4" />
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-6 w-32" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-6 w-32" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Event gallery skeleton */}
            <div>
              <Skeleton className="h-8 w-48 mb-4" />
              <Skeleton className="w-full aspect-video rounded-lg" />
              <div className="flex space-x-2 mt-2 overflow-x-auto pb-2">
                {Array.from({ length: 4 }).map((_, i) => (
                  <Skeleton key={i} className="w-20 h-20 flex-shrink-0 rounded" />
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-6">
            {/* Price & ticket info skeleton */}
            <Card>
              <CardContent className="pt-6">
                <Skeleton className="h-7 w-32 mb-4" />
                <Skeleton className="h-10 w-full mb-4" />
                <div className="flex justify-between mt-4">
                  <Skeleton className="h-9 w-24" />
                  <Skeleton className="h-9 w-24" />
                </div>
              </CardContent>
            </Card>

            {/* Registration info skeleton */}
            <Card>
              <CardContent className="pt-6">
                <Skeleton className="h-6 w-32 mb-4" />
                <div className="flex items-start gap-2 mb-3">
                  <Skeleton className="h-4 w-4 mt-0.5" />
                  <div className="w-full">
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-2/3" />
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <Skeleton className="h-4 w-4 mt-0.5" />
                  <div className="w-full">
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-2/3" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Organizer info skeleton */}
            <Card>
              <CardContent className="pt-6">
                <Skeleton className="h-6 w-32 mb-3" />
                <Skeleton className="h-5 w-48 mb-3" />
                <Skeleton className="h-px w-full my-3" />
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-40" />
                  </div>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-32" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Location info skeleton */}
            <Card>
              <CardContent className="pt-6">
                <Skeleton className="h-6 w-32 mb-3" />
                <Skeleton className="h-5 w-full mb-2" />
                <Skeleton className="h-5 w-3/4 mb-4" />
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </main>
  );
}