'use client';

import { useState, useEffect, useRef } from 'react';
import { useWizard } from '@/components/events/event-wizard/wizard-container';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Upload, X, ImagePlus, Loader2, Info, Plus, MoveVertical } from 'lucide-react';
import Image from 'next/image';
import { createClient } from '@/lib/supabase/client';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ImageFocusAdjuster } from '@/components/events/image-focus-adjuster';

// Define image types
type ImageType = 'poster' | 'cover' | 'gallery';

// Define image object structure
interface ImageObject {
  url: string;
  path: string;
  focusPoint?: {
    y: number;
  };
}

export function ImageUploadStep() {
  const { formData, updateFormData, nextStep } = useWizard();
  const [isUploading, setIsUploading] = useState<ImageType | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [galleryImages, setGalleryImages] = useState<ImageObject[]>(formData.galleryImages || []);
  const [isGalleryUploading, setIsGalleryUploading] = useState(false);
  const [isFocusAdjusterOpen, setIsFocusAdjusterOpen] = useState(false);

  // Drag and drop states
  const [isDraggingCover, setIsDraggingCover] = useState(false);
  const [isDraggingPoster, setIsDraggingPoster] = useState(false);
  const [isDraggingGallery, setIsDraggingGallery] = useState(false);

  // Refs for file inputs
  const coverInputRef = useRef<HTMLInputElement>(null);
  const posterInputRef = useRef<HTMLInputElement>(null);
  const galleryInputRef = useRef<HTMLInputElement>(null);

  // Maximum number of gallery images allowed
  const MAX_GALLERY_IMAGES = 10;

  // Initialize Supabase client
  const supabase = createClient();

  // Check authentication status on component mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        console.log('[DEBUG] ImageUploadStep - Auth session check:', session ? 'Authenticated' : 'Not authenticated');
        if (session?.user) {
          console.log('[DEBUG] ImageUploadStep - User ID:', session.user.id);
        } else {
          console.log('[DEBUG] ImageUploadStep - No session found, but will continue without redirecting');
          // Don't show error - middleware should handle authentication
        }
      } catch (error) {
        console.error('[DEBUG] ImageUploadStep - Error checking auth:', error);
        // Don't redirect or show error - just log it
      }
    };

    checkAuth();
  }, []);

  // Override next button to proceed without validation
  useEffect(() => {
    const nextButton = document.getElementById('wizard-next-button') as HTMLButtonElement;
    if (nextButton) {
      nextButton.onclick = () => nextStep();
    }
  }, [nextStep]);

  // Drag and drop handlers
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>, type: ImageType) => {
    e.preventDefault();
    e.stopPropagation();

    switch (type) {
      case 'cover':
        setIsDraggingCover(true);
        break;
      case 'poster':
        setIsDraggingPoster(true);
        break;
      case 'gallery':
        setIsDraggingGallery(true);
        break;
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>, type: ImageType) => {
    e.preventDefault();
    e.stopPropagation();

    // Only set isDragging to false if we're leaving the dropzone itself, not its children
    if (e.currentTarget === e.target) {
      switch (type) {
        case 'cover':
          setIsDraggingCover(false);
          break;
        case 'poster':
          setIsDraggingPoster(false);
          break;
        case 'gallery':
          setIsDraggingGallery(false);
          break;
      }
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, type: ImageType) => {
    e.preventDefault();
    e.stopPropagation();

    // Reset all dragging states
    setIsDraggingCover(false);
    setIsDraggingPoster(false);
    setIsDraggingGallery(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      if (type === 'gallery') {
        handleGalleryUpload(e.dataTransfer.files);
      } else if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        handleSingleFileUpload(e.dataTransfer.files[0], type);
      }
    }
  };

  // Trigger file input click
  const triggerFileInput = (type: ImageType) => {
    switch (type) {
      case 'cover':
        if (coverInputRef.current) coverInputRef.current.click();
        break;
      case 'poster':
        if (posterInputRef.current) posterInputRef.current.click();
        break;
      case 'gallery':
        if (galleryInputRef.current) galleryInputRef.current.click();
        break;
    }
  };

  // Handle file upload for poster and cover images
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>, type: ImageType) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // For gallery images, handle multiple files
    if (type === 'gallery') {
      await handleGalleryUpload(files);
      return;
    }

    // For poster and cover, handle single file
    const file = files[0];
    await handleSingleFileUpload(file, type);
  };

  // Handle single file upload (poster or cover)
  const handleSingleFileUpload = async (file: File | undefined, type: ImageType) => {
    if (!file) {
      toast({
        title: "Upload Failed",
        description: "No file selected",
        variant: "destructive",
      });
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File Type",
        description: "Please upload an image file (JPEG, PNG, GIF)",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast({
        title: "File Too Large",
        description: "Image must be less than 5MB",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUploading(type);
      setUploadProgress(10);
      setError(null);

      // Create a unique file path
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      let filePath = `events/drafts/${formData.id || 'new'}/${type}/${fileName}`;
      let publicUrl = '';

      // Use the API route for upload instead of direct Supabase upload
      setUploadProgress(20);
      console.log('Preparing to upload via API route');

      // Create form data for the API
      const apiFormData = new FormData();
      if (file) {
        apiFormData.append('file', file);
      }
      apiFormData.append('type', type);

      if (formData.id) {
        apiFormData.append('eventId', formData.id);
      }

      try {
        setUploadProgress(30);
        console.log('Sending file to API route');

        // Upload via API route
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: apiFormData
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API upload error:', errorData);
          throw new Error(errorData.error || 'Failed to upload image');
        }

        const result = await response.json();
        console.log('API upload successful:', result);

        // Set file path and URL from API response
        filePath = result.path;
        publicUrl = result.url;

        setUploadProgress(70);
      } catch (uploadError) {
        console.error('API upload exception:', uploadError);
        throw uploadError;
      }

      setUploadProgress(70);

      // Public URL is already set from the API response
      console.log('Using public URL from API response:', publicUrl);
      setUploadProgress(100);

      // Update form data with image info
      updateFormData({
        [`${type}Image`]: {
          url: publicUrl,
          path: filePath
        }
      });
    } catch (err) {
      console.error(`Error uploading ${type} image:`, err);
      setError(err instanceof Error ? err.message : 'An error occurred during upload');

      toast({
        title: "Upload Failed",
        description: err instanceof Error ? err.message : 'Failed to upload image',
        variant: "destructive",
      });
    } finally {
      setIsUploading(null);
    }
  };

  // Handle gallery image uploads (multiple files)
  const handleGalleryUpload = async (files: FileList) => {
    // Check if adding these files would exceed the maximum
    if (galleryImages.length + files.length > MAX_GALLERY_IMAGES) {
      toast({
        title: "Too Many Images",
        description: `You can upload a maximum of ${MAX_GALLERY_IMAGES} gallery images`,
        variant: "destructive",
      });
      return;
    }

    setIsGalleryUploading(true);
    setError(null);

    // Check authentication before upload, but don't block the upload if there's an issue
    // The API route will handle authentication properly
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (session?.user) {
        console.log('[DEBUG] Gallery upload - authenticated as user:', session.user.id);
      } else {
        console.log('[DEBUG] Gallery upload - no session found, but will try to upload anyway');
        // The API route will handle authentication
      }
    } catch (authError) {
      console.error('[DEBUG] Error checking authentication for gallery upload:', authError);
      // Continue with upload attempt - the API route will handle authentication
    }

    const newImages: ImageObject[] = [];
    const successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // Skip if file is undefined
      if (!file) {
        errorCount++;
        continue;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        errorCount++;
        continue;
      }

      // Validate file size (5MB max)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        errorCount++;
        continue;
      }

      try {
        setUploadProgress(Math.round((i / files.length) * 100));

        // Create a unique file path for reference
        const fileExt = file.name.split('.').pop() || 'jpg';
        const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;

        console.log(`Starting gallery upload ${i + 1}/${files.length} via API route`);

        try {
          // Create form data for the API
          const apiFormData = new FormData();
          if (file) {
            apiFormData.append('file', file);
          }
          apiFormData.append('type', 'gallery');

          if (formData.id) {
            apiFormData.append('eventId', formData.id);
          }

          // Upload via API route
          const response = await fetch('/api/upload', {
            method: 'POST',
            body: apiFormData
          });

          if (!response.ok) {
            const errorData = await response.json();
            console.error(`API upload error for gallery image ${i + 1}/${files.length}:`, errorData);
            errorCount++;
            continue;
          }

          const result = await response.json();
          console.log(`Gallery upload ${i + 1}/${files.length} successful:`, result);

          // Add to new images array using data from API response
          newImages.push({
            url: result.url,
            path: result.path
          });

        } catch (uploadError) {
          console.error(`Exception uploading gallery image ${i + 1}/${files.length}:`, uploadError);
          errorCount++;
          continue;
        }
      } catch (err) {
        console.error(`Error uploading gallery image ${i + 1}/${files.length}:`, err);
        errorCount++;
      }
    }

    // Update state with new images
    if (newImages.length > 0) {
      const updatedGalleryImages = [...galleryImages, ...newImages];
      setGalleryImages(updatedGalleryImages);

      // Update form data
      updateFormData({
        galleryImages: updatedGalleryImages
      });
    }

    // Only show toast for errors
    if (errorCount > 0) {
      toast({
        title: "Upload Failed",
        description: `Failed to upload ${errorCount} image${errorCount !== 1 ? 's' : ''}`,
        variant: "destructive",
      });
    }

    setIsGalleryUploading(false);
  };

  // Remove an uploaded image (poster or cover)
  const handleRemoveImage = (type: 'poster' | 'cover') => {
    // If there's a path, delete from storage
    if (formData[`${type}Image`]?.path) {
      console.log(`Removing ${type} image from storage:`, formData[`${type}Image`].path);

      supabase
        .storage
        .from('images')
        .remove([formData[`${type}Image`].path])
        .then(({ data, error }) => {
          if (error) {
            console.error(`Error removing ${type} image:`, error);
          } else {
            console.log(`Successfully removed ${type} image:`, data);
          }
        });
    }

    // Update form data to remove the image
    updateFormData({
      [`${type}Image`]: undefined
    });
  };

  // Handle opening the focus adjuster
  const handleOpenFocusAdjuster = () => {
    setIsFocusAdjusterOpen(true);
  };

  // Handle saving focus point
  const handleSaveFocusPoint = (focusPoint: { y: number }) => {
    if (formData.coverImage) {
      // Update the cover image with the new focus point
      updateFormData({
        coverImage: {
          ...formData.coverImage,
          focusPoint
        }
      });

      toast({
        title: "Vertical position saved",
        description: "Your cover image vertical position has been adjusted successfully.",
      });
    }
  };

  // Remove a gallery image
  const handleRemoveGalleryImage = (index: number) => {
    // Get the image to remove
    const imageToRemove = galleryImages[index];

    // Remove from storage
    if (imageToRemove?.path) {
      console.log(`Removing gallery image from storage:`, imageToRemove.path);

      supabase
        .storage
        .from('images')
        .remove([imageToRemove.path])
        .then(({ data, error }) => {
          if (error) {
            console.error(`Error removing gallery image:`, error);
          } else {
            console.log(`Successfully removed gallery image:`, data);
          }
        });
    }

    // Update state
    const updatedGalleryImages = [...galleryImages];
    updatedGalleryImages.splice(index, 1);
    setGalleryImages(updatedGalleryImages);

    // Update form data
    updateFormData({
      galleryImages: updatedGalleryImages
    });
  };

  return (
    <div className="space-y-6">

      <div>
        <h2 className="text-2xl font-semibold mb-2">Event Images</h2>
        <p className="text-gray-500">
          Upload images for your event (optional)
        </p>
      </div>

      {/* Cover Image Upload */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="coverImage" className="text-lg font-medium">
                  Cover Image
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        This image will appear as a banner at the top of your event page.
                        Recommended size: 1200×400 pixels.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {formData.coverImage && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleOpenFocusAdjuster}
                  >
                    <MoveVertical className="h-4 w-4 mr-1" />
                    Adjust Vertical Position
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveImage('cover')}
                    className="text-red-500"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                </div>
              )}
            </div>

            {formData.coverImage ? (
              <div className="relative w-full h-[200px] rounded-md overflow-hidden border">
                <Image
                  src={formData.coverImage.url}
                  alt="Cover Image"
                  fill
                  className="object-cover"
                  style={formData.coverImage.focusPoint ? {
                    objectPosition: `center ${formData.coverImage.focusPoint.y}%`
                  } : {}}
                />
              </div>
            ) : (
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDraggingCover
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-300 hover:border-primary/50'
                  }`}
                onDragEnter={(e) => handleDragEnter(e, 'cover')}
                onDragOver={handleDragOver}
                onDragLeave={(e) => handleDragLeave(e, 'cover')}
                onDrop={(e) => handleDrop(e, 'cover')}
              >
                <input
                  type="file"
                  id="coverImage"
                  accept="image/*"
                  onChange={(e) => handleFileUpload(e, 'cover')}
                  className="hidden"
                  disabled={!!isUploading}
                  ref={coverInputRef}
                />
                <div
                  onClick={() => triggerFileInput('cover')}
                  className="flex flex-col items-center justify-center cursor-pointer"
                >
                  {isUploading === 'cover' ? (
                    <div className="flex flex-col items-center space-y-2">
                      <Loader2 className="w-8 h-8 text-primary animate-spin" />
                      <span className="text-sm text-gray-500">Uploading... {uploadProgress}%</span>
                    </div>
                  ) : (
                    <>
                      <Upload className="w-8 h-8 text-gray-400" />
                      <span className="mt-2 text-sm text-gray-500">
                        Click or drag and drop to upload cover image
                      </span>
                      <span className="mt-1 text-xs text-gray-400">
                        Recommended size: 1200×400 pixels (16:3 ratio)
                      </span>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Poster Image Upload */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="posterImage" className="text-lg font-medium">
                  Poster Image
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        This image will be displayed in the event gallery and listings.
                        Recommended size: 800×1200 pixels.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {formData.posterImage && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleRemoveImage('poster')}
                  className="text-red-500"
                >
                  <X className="h-4 w-4 mr-1" />
                  Remove
                </Button>
              )}
            </div>

            {formData.posterImage ? (
              <div className="relative w-full max-w-[300px] h-[400px] rounded-md overflow-hidden border mx-auto">
                <Image
                  src={formData.posterImage.url}
                  alt="Poster Image"
                  fill
                  className="object-cover"
                />
              </div>
            ) : (
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDraggingPoster
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-300 hover:border-primary/50'
                  }`}
                onDragEnter={(e) => handleDragEnter(e, 'poster')}
                onDragOver={handleDragOver}
                onDragLeave={(e) => handleDragLeave(e, 'poster')}
                onDrop={(e) => handleDrop(e, 'poster')}
              >
                <input
                  type="file"
                  id="posterImage"
                  accept="image/*"
                  onChange={(e) => handleFileUpload(e, 'poster')}
                  className="hidden"
                  disabled={!!isUploading}
                  ref={posterInputRef}
                />
                <div
                  onClick={() => triggerFileInput('poster')}
                  className="flex flex-col items-center justify-center cursor-pointer"
                >
                  {isUploading === 'poster' ? (
                    <div className="flex flex-col items-center space-y-2">
                      <Loader2 className="w-8 h-8 text-primary animate-spin" />
                      <span className="text-sm text-gray-500">Uploading... {uploadProgress}%</span>
                    </div>
                  ) : (
                    <>
                      <ImagePlus className="w-8 h-8 text-gray-400" />
                      <span className="mt-2 text-sm text-gray-500">
                        Click or drag and drop to upload poster image
                      </span>
                      <span className="mt-1 text-xs text-gray-400">
                        Recommended size: 800×1200 pixels (2:3 ratio)
                      </span>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Gallery Images */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Label htmlFor="galleryImages" className="text-lg font-medium">
                  Gallery Images
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Upload multiple images to create a gallery for your event.
                        Maximum {MAX_GALLERY_IMAGES} images.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="text-sm text-gray-500">
                {galleryImages.length} / {MAX_GALLERY_IMAGES} images
              </div>
            </div>

            {/* Gallery Upload Area */}
            {galleryImages.length < MAX_GALLERY_IMAGES && (
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDraggingGallery
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-300 hover:border-primary/50'
                  }`}
                onDragEnter={(e) => handleDragEnter(e, 'gallery')}
                onDragOver={handleDragOver}
                onDragLeave={(e) => handleDragLeave(e, 'gallery')}
                onDrop={(e) => handleDrop(e, 'gallery')}
              >
                <input
                  type="file"
                  id="galleryImages"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleFileUpload(e, 'gallery')}
                  className="hidden"
                  disabled={isGalleryUploading}
                  ref={galleryInputRef}
                />
                <div
                  onClick={() => triggerFileInput('gallery')}
                  className="flex flex-col items-center justify-center cursor-pointer"
                >
                  {isGalleryUploading ? (
                    <div className="flex flex-col items-center space-y-2">
                      <Loader2 className="w-8 h-8 text-primary animate-spin" />
                      <span className="text-sm text-gray-500">Uploading... {uploadProgress}%</span>
                    </div>
                  ) : (
                    <>
                      <Plus className="w-8 h-8 text-gray-400" />
                      <span className="mt-2 text-sm text-gray-500">
                        Click or drag and drop to upload gallery images
                      </span>
                      <span className="mt-1 text-xs text-gray-400">
                        PNG, JPG, GIF up to 5MB each (max {MAX_GALLERY_IMAGES} images total)
                      </span>
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Gallery Preview */}
            {galleryImages.length > 0 && (
              <div className="space-y-4">
                <h4 className="text-sm font-medium">Gallery Preview</h4>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {galleryImages.map((image, index) => (
                    <div key={index} className="relative group">
                      <div className="relative w-full h-32 rounded-md overflow-hidden border">
                        <Image
                          src={image.url}
                          alt={`Gallery Image ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleRemoveGalleryImage(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {error && (
        <div className="bg-red-50 text-red-600 p-3 rounded-md flex items-start">
          <X className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {/* Image Focus Adjuster */}
      {formData.coverImage && (
        <ImageFocusAdjuster
          imageUrl={formData.coverImage.url}
          isOpen={isFocusAdjusterOpen}
          onClose={() => setIsFocusAdjusterOpen(false)}
          onSave={handleSaveFocusPoint}
          initialFocusPoint={formData.coverImage.focusPoint || { y: 50 }}
        />
      )}
    </div>
  );
}
