# Supabase Redirect URLs Configuration

To ensure authentication works correctly across all environments (local, staging, production), you need to configure the redirect URLs in your Supabase project.

## Required Redirect URLs

Add the following URLs to your Supabase project's "URL Configuration" page:

1. **Site URL**: Set this to your production URL
   ```
   https://fuiyoo.netlify.app
   ```

2. **Additional Redirect URLs**: Add the following URLs
   ```
   http://localhost:3000/**
   https://staging--fuiyoo.netlify.app/**
   ```

## How to Configure

1. Log in to your Supabase dashboard
2. Select your project
3. Go to "Authentication" > "URL Configuration"
4. Set the "Site URL" to your production URL
5. Add the additional redirect URLs in the "Additional Redirect URLs" section
6. Save your changes

## Using Wildcards

Supabase supports wildcards in redirect URLs:

- `*` matches any sequence of non-separator characters
- `**` matches any sequence of characters (including separators)

For example:
- `http://localhost:3000/**` matches any path on localhost:3000
- `https://staging--fuiyoo.netlify.app/**` matches any path on the staging site

## Testing

After configuring the redirect URLs, test authentication in each environment:

1. **Local Development**:
   - Sign in at `http://localhost:3000/sign-in`
   - You should be redirected back to `http://localhost:3000/dashboard`

2. **Staging**:
   - Sign in at `https://staging--fuiyoo.netlify.app/sign-in`
   - You should be redirected back to `https://staging--fuiyoo.netlify.app/dashboard`

3. **Production**:
   - Sign in at `https://fuiyoo.netlify.app/sign-in`
   - You should be redirected back to `https://fuiyoo.netlify.app/dashboard`

## Troubleshooting

If you encounter redirect issues:

1. Check the browser console for logs about redirect URLs
2. Verify that the correct environment variables are loaded
3. Make sure the redirect URLs are properly configured in Supabase
4. Clear browser cookies and try again

Remember that changes to the Supabase URL configuration may take a few minutes to propagate.
