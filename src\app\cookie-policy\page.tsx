export default function CookiePolicy() {
  return (
    <div className="container mx-auto py-8 max-w-3xl">
      <h1 className="text-3xl font-bold mb-6">Cookie Policy</h1>
      
      <div className="prose dark:prose-invert">
        <p>This website uses cookies to enhance your browsing experience. By using our website, you agree to our use of cookies.</p>
        
        <h2>What are cookies?</h2>
        <p>Cookies are small text files that are placed on your device when you visit a website. They are widely used to make websites work more efficiently and provide useful information to website owners.</p>
        
        <h2>How we use cookies</h2>
        <ul>
          <li>Essential cookies: Required for the website to function properly</li>
          <li>Analytics cookies: Help us understand how visitors interact with our website</li>
          <li>Preference cookies: Remember your settings and preferences</li>
        </ul>
        
        <h2>Managing cookies</h2>
        <p>You can control and/or delete cookies as you wish. You can delete all cookies that are already on your device and you can set most browsers to prevent them from being placed.</p>
      </div>
    </div>
  )
} 