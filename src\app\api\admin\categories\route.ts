import { createClient } from '@/lib/supabase/pages-client'
import { NextResponse } from "next/server"

export async function GET() {
  const supabase = await createClient()

  // Use a type assertion to avoid TypeScript errors
  const supabaseAny = supabase as any;
  const { data, error } = await supabaseAny
    .from("categories")
    .select("*")
    .order("name")

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json(data)
}

export async function POST(request: Request) {
  const supabase = await createClient()
  const body = await request.json()

  // Use a type assertion to avoid TypeScript errors
  const supabaseAny = supabase as any;
  const { error } = await supabaseAny
    .from("categories")
    .insert([{ name: body.name, description: body.description }])

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json({ message: "Category created successfully" })
}

export async function PUT(request: Request) {
  const supabase = await createClient()
  const body = await request.json()

  // Use a type assertion to avoid TypeScript errors
  const supabaseAny = supabase as any;
  const { error } = await supabaseAny
    .from("categories")
    .update({ name: body.name, description: body.description })
    .eq("id", body.id)

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json({ message: "Category updated successfully" })
}

export async function DELETE(request: Request) {
  const supabase = await createClient()
  const { searchParams } = new URL(request.url)
  const id = searchParams.get("id")

  if (!id) {
    return NextResponse.json({ error: "Category ID is required" }, { status: 400 })
  }

  // Use a type assertion to avoid TypeScript errors
  const supabaseAny = supabase as any;
  const { error } = await supabaseAny
    .from("categories")
    .delete()
    .eq("id", id)

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json({ message: "Category deleted successfully" })
}