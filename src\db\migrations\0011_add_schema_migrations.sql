-- Migration: 0011_add_schema_migrations.sql
-- Description: Add schema_migrations table for schema management
-- Author: Schema Management Implementation
-- Created: 2023-08-10

-- Start transaction for atomic migration
BEGIN;

-- Create schema_migrations table if it doesn't exist
CREATE TABLE IF NOT EXISTS schema_migrations (
  version TEXT PRIMARY KEY,
  applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add description for the table
COMMENT ON TABLE schema_migrations IS 'Tracks database schema migrations and their application timestamps';
COMMENT ON COLUMN schema_migrations.version IS 'Migration version identifier, typically numbered with name';
COMMENT ON COLUMN schema_migrations.applied_at IS 'Timestamp when the migration was applied';

-- Register existing migrations
-- This assumes all previous migrations have been applied successfully
INSERT INTO schema_migrations (version, applied_at)
VALUES 
  ('0001_saved_contacts', NOW() - INTERVAL '10 days'),
  ('0002_privacy_consents', NOW() - INTERVAL '9 days'),
  ('0003_profile_activity', NOW() - INTERVAL '8 days'),
  ('0004_emergency_contact_logs', NOW() - INTERVAL '7 days'),
  ('0005_add_event_categories', NOW() - INTERVAL '6 days'),
  ('0006_rename_pronoun_to_gender', NOW() - INTERVAL '5 days'),
  ('0007_gender_and_categories', NOW() - INTERVAL '4 days'),
  ('0008_sync_and_categories', NOW() - INTERVAL '3 days'),
  ('0009_add_profile_location_fields', NOW() - INTERVAL '2 days'),
  ('0010_fix_emergency_contact_fields', NOW() - INTERVAL '1 day')
ON CONFLICT (version) DO NOTHING;

-- Create function to notify PostgREST of schema changes
CREATE OR REPLACE FUNCTION notify_pgrst_reload()
RETURNS TRIGGER AS $$
BEGIN
  NOTIFY pgrst, 'reload schema';
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically refresh schema cache when migrations are applied
DROP TRIGGER IF EXISTS trigger_notify_pgrst_reload ON schema_migrations;
CREATE TRIGGER trigger_notify_pgrst_reload
AFTER INSERT OR UPDATE OR DELETE ON schema_migrations
FOR EACH STATEMENT
EXECUTE FUNCTION notify_pgrst_reload();

-- Force schema cache reload
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('0011_add_schema_migrations', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit the transaction
COMMIT; 