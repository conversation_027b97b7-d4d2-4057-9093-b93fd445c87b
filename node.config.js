/**
 * Node.js 22 Optimization Configuration
 * This file contains Node.js specific optimizations for the Fuiyoo application
 */

// Node.js 22 performance optimizations
const NODE_22_OPTIMIZATIONS = {
  // Memory management optimizations
  memory: {
    // Reduced from 8GB to 4GB as Node.js 22 has better memory management
    maxOldSpaceSize: 4096,
    // Enable source maps for better debugging
    enableSourceMaps: true,
    // Enable experimental features for better performance
    experimentalLoaderOptimizations: true,
  },
  
  // V8 engine optimizations
  v8: {
    // Enable experimental V8 optimizations
    enableExperimentalOptimizations: true,
    // Use new garbage collection algorithms
    useNewGC: true,
  },
  
  // Network optimizations
  network: {
    // Enable HTTP/2 by default
    enableHttp2: true,
    // Use keep-alive connections
    keepAlive: true,
  }
};

// Environment-specific configurations
const getNodeOptions = (env = 'development') => {
  const baseOptions = [
    `--max-old-space-size=${NODE_22_OPTIMIZATIONS.memory.maxOldSpaceSize}`,
  ];

  if (env === 'development') {
    baseOptions.push('--enable-source-maps');
    // Enable experimental loader optimizations in development
    baseOptions.push('--experimental-loader');
  }

  if (env === 'production') {
    // Production-specific optimizations
    baseOptions.push('--optimize-for-size');
  }

  return baseOptions.join(' ');
};

// Export configurations
module.exports = {
  NODE_22_OPTIMIZATIONS,
  getNodeOptions,
  
  // Recommended NODE_OPTIONS for different environments
  development: getNodeOptions('development'),
  production: getNodeOptions('production'),
  test: getNodeOptions('test'),
};
