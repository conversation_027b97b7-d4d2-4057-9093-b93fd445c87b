/**
 * Formats a date string (YYYY-MM-DD) or Date object into a more readable format
 */
export function formatDate(dateInput: string | Date | null | undefined): string {
  if (!dateInput) {
    return 'Date not available';
  }

  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };

  try {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    return date.toLocaleDateString('en-US', options);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
}