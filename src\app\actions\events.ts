'use server';

import { createClient } from '@/lib/supabase/server';
import { EventTypeRepository } from '@/repositories/event-type-repository';
import { EventCategoryRepository } from '@/repositories/event-category-repository';
import { RegistrationFieldRepository } from '@/repositories/registration-field-repository';
import { FieldMappingRepository } from '@/repositories/field-mapping-repository';
import { EventRepository, Event } from '@/repositories/event-repository';
import crypto from 'crypto';
import {
  EventType,
  EventCategory,
  BaseEventCategory,
  RegistrationField,
  EventField,
  FieldMapping,
  FieldType
} from '@/types/event-types';
import { CreateEventInput } from '@/types/event-schemas';
import { z } from 'zod';
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger";
import { makeSlug } from "@/lib/utils";
import { toDate, toOptionalDate } from "@/lib/utils/date-utils";
import { createEventSchema } from '@/lib/validations/event-schema';

/**
 * Get all event types
 */
export async function getEventTypes(): Promise<{
  success: boolean;
  data?: EventType[];
  error?: string;
}> {
  try {
    const eventTypeRepository = new EventTypeRepository();
    const eventTypes = await eventTypeRepository.getAllEventTypes();

    return {
      success: true,
      data: eventTypes
    };
  } catch (error) {
    console.error('Error fetching event types:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error fetching event types'
    };
  }
}

/**
 * Get event type by ID
 */
export async function getEventTypeById(id: string): Promise<{
  success: boolean;
  data?: EventType;
  error?: string;
}> {
  try {
    const eventTypeRepository = new EventTypeRepository();
    const eventType = await eventTypeRepository.getEventTypeById(id);

    if (!eventType) {
      return {
        success: false,
        error: `Event type with ID ${id} not found`
      };
    }

    return {
      success: true,
      data: eventType
    };
  } catch (error) {
    console.error(`Error fetching event type with ID ${id}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error fetching event type'
    };
  }
}

// Use the imported schema or extend it if needed
const eventFormSchema = createEventSchema;

export async function createEvent(formData: z.infer<typeof eventFormSchema>) {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      logger.error('User authentication failed in createEvent - no auth user found');
      return { success: false, error: "Unauthorized - Please sign in to create an event", data: null };
    }

    // Also check the session to ensure it's valid
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      logger.error('User authentication failed in createEvent - no valid session');
      return { success: false, error: "Your session has expired. Please sign in again.", data: null };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      logger.error(`Error fetching user ID for auth_user_id ${authUser.id}: ${userIdError?.message}`);

      // Check if the user exists by email as a fallback
      if (authUser.email) {
        const { data: userByEmail, error: emailError } = await supabase
          .from('users')
          .select('id')
          .eq('email', authUser.email)
          .maybeSingle();

        if (userByEmail && !emailError) {
          logger.info(`Found user by email instead of auth_user_id: ${authUser.email}`);
          const userId = userByEmail.id;

          // Update the auth_user_id for future requests
          await supabase
            .from('users')
            .update({ auth_user_id: authUser.id })
            .eq('id', userId);

          return await processEventCreation(supabase, formData, userId, authUser);
        }
      }

      // If we still can't find the user, create a new one
      logger.info(`Creating new user record for auth_user_id: ${authUser.id}`);

      // Generate a UUID for the user ID
      const userId = crypto.randomUUID();

      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          id: userId,
          auth_user_id: authUser.id,
          email: authUser.email || '',
          first_name: authUser.user_metadata?.full_name?.split(' ')[0] || 'User',
          last_name: authUser.user_metadata?.full_name?.split(' ').slice(1).join(' ') || '',
          role: 'user'
        })
        .select('id')
        .single();

      if (createError || !newUser) {
        logger.error(`Failed to create user record: ${createError?.message}`);
        return { success: false, error: "Failed to create user record. Please try again or contact support.", data: null };
      }

      return await processEventCreation(supabase, formData, newUser.id, authUser);
    }

    const userId = userData.id;

    return await processEventCreation(supabase, formData, userId, authUser);
  } catch (error) {
    logger.error(`Unexpected error in createEvent: ${error}`);
    return { success: false, error: "Failed to create event", data: null };
  }
}

// Helper function to process event creation after user authentication
async function processEventCreation(
  supabase: any,
  formData: z.infer<typeof eventFormSchema>,
  userId: string,
  authUser: any
) {

  // Log input data for debugging
  logger.info(`Creating event with data: ${JSON.stringify({
    ...formData,
    organizerId: userId,
  }, null, 2)}`);

  // Validate input data
  try {
    eventFormSchema.parse(formData);
  } catch (error) {
    if (error instanceof z.ZodError) {
      logger.error(`Validation error in createEvent: ${JSON.stringify(error.errors)}`);
      return { success: false, error: `Validation error: ${error.errors?.[0]?.message || 'Invalid form data'}`, data: null };
    }
    logger.error(`Unexpected validation error: ${error instanceof Error ? error.message : String(error)}`);
    return { success: false, error: "Invalid form data", data: null };
  }

  // Get event type
  let eventType;
  try {
    const eventTypeRepo = new EventTypeRepository();
    const typeResult = await eventTypeRepo.findById(formData.eventType);
    if (!typeResult.success || !typeResult.data) {
      logger.error(`Event type not found: ${formData.eventType}`);
      return { success: false, error: "Event type not found", data: null };
    }
    eventType = typeResult.data;
    logger.info(`Found event type: ${eventType.name}`);
  } catch (error) {
    logger.error(`Error fetching event type: ${error}`);
    return { success: false, error: "Failed to fetch event type", data: null };
  }

  // Create event
  let event;
  try {
    const eventRepo = new EventRepository();

    const eventData = {
      title: formData.title,
      slug: makeSlug(formData.title),
      description: formData.description || "",
      location: formData.location || "",
      country: formData.country || "",
      state: formData.state || "",
      city: formData.city || "",
      startDate: toDate(formData.startDate),
      endDate: toDate(formData.endDate),
      timezone: formData.timezone || "UTC",
      eventTypeId: formData.eventType,
      organizerId: userId,
      createdBy: userId,
      status: (formData.status === "draft" || formData.status === "published" || formData.status === "cancelled" || formData.status === "completed")
        ? formData.status
        : "draft",
      // Add capacity and registration settings
      totalCapacity: formData.totalCapacity === null ? undefined : formData.totalCapacity,
      registrationCloseDate: toDate(formData.registrationCloseDate),
      allowCategorySpecificClosingDates: formData.allowCategorySpecificClosingDates === null ? false : formData.allowCategorySpecificClosingDates,
      // Add T-shirt options if provided
      tshirtOptions: formData.tshirtOptions ? {
        enabled: formData.tshirtOptions.enabled === null ? false : formData.tshirtOptions.enabled,
        sizes: formData.tshirtOptions.sizes === null ? ["XS", "S", "M", "L", "XL", "XXL", "XXXL"] : formData.tshirtOptions.sizes,
        description: formData.tshirtOptions.description === null ? undefined : formData.tshirtOptions.description,
        sizeChartImage: formData.tshirtOptions.sizeChartImage
      } : null,
      // Add emergency contact settings if provided
      emergencyContactSettings: formData.emergencyContactSettings,
      // Add image data if provided
      posterImage: formData.posterImage,
      coverImage: formData.coverImage,
      // Add gallery images if provided
      galleryImages: formData.galleryImages ? formData.galleryImages.filter(img => img !== null) as { url: string; path: string }[] : null,
    };

    logger.info(`Creating event with data: ${JSON.stringify(eventData)}`);

    if (formData.id) {
      // For updateEvent, it returns the Event directly
      event = await eventRepo.updateEvent(formData.id, eventData);
      logger.info(`Updated event: ${event.id}`);
    } else {
      // For createEvent, it returns a RepositoryResult
      const result = await eventRepo.createEvent(eventData);
      if (result.success && result.data) {
        event = result.data;
        logger.info(`Created new event: ${event.id}`);
      } else {
        logger.error(`Error creating event: ${result.message}`);
        return { success: false, error: result.message, data: null };
      }
    }

    if (!event) {
      logger.error(`Event creation failed without error`);
      return { success: false, error: "Failed to create event", data: null };
    }
  } catch (error) {
    logger.error(`Error creating event: ${error}`);
    return { success: false, error: "Failed to create event", data: null };
  }

  // Create categories if provided, otherwise create default if required by event type
  if (formData.categories && formData.categories.length > 0) {
    try {
      const categoryRepo = new EventCategoryRepository();

      // Get existing categories for this event
      logger.info(`Getting existing categories for event ${event.id}`);
      let existingCategories: EventCategory[] = [];
      try {
        existingCategories = await categoryRepo.getCategoriesByEventId(event.id);
        logger.info(`Found ${existingCategories.length} existing categories`);
      } catch (getError) {
        logger.error(`Error getting existing categories: ${getError}`);
        // Continue even if getting existing categories fails
      }

      const categories = formData.categories.map((category) => {
        // Check if the category is already using the new JSON-based format
        if (category.properties) {
          // Category is already using the new format, just ensure eventId is set
          return {
            name: category.name,
            description: category.description,
            eventId: event.id,
            properties: category.properties
          };
        } else {
          // Convert legacy format to JSON-based format
          const properties: Record<string, any> = {};

          // Add common properties - use !== undefined check to handle 0 values
          // @ts-ignore - capacity might exist in legacy format
          if (category.capacity !== undefined) properties.capacity = typeof category.capacity === 'string' ? parseInt(category.capacity, 10) : category.capacity;
          if (category.price !== undefined) properties.price = typeof category.price === 'string' ? parseFloat(category.price) : category.price;
          if (category.startTime) properties.startTime = category.startTime;

          // Add event type-specific properties
          if (category.earlyBirdPrice !== undefined) properties.earlyBirdPrice = typeof category.earlyBirdPrice === 'string' ? parseFloat(category.earlyBirdPrice) : category.earlyBirdPrice;
          if (category.earlyBirdEndDate) properties.earlyBirdEndDate = category.earlyBirdEndDate;
          if (category.bibPrefix) properties.bibPrefix = category.bibPrefix;
          if (category.bibStartNumber !== undefined) properties.bibStartNumber = category.bibStartNumber;
          if (category.bibRequireGeneration !== undefined) properties.bibRequireGeneration = category.bibRequireGeneration;
          if (category.registrationOpen !== undefined) properties.registrationOpen = category.registrationOpen;
          if (category.registrationCloseDate) properties.registrationCloseDate = category.registrationCloseDate;
          if (category.registrationLimit !== undefined) properties.registrationLimit = typeof category.registrationLimit === 'string' ? parseInt(category.registrationLimit, 10) : category.registrationLimit;

          // Always set registrationCount to 0 for new categories
          properties.registrationCount = 0;

          // If eventType is available in the category, include it
          // @ts-ignore - eventType might exist in legacy format
          if ('eventType' in category && category.eventType) properties.eventType = category.eventType;

          return {
            name: category.name,
            description: category.description,
            eventId: event.id,
            properties
          };
        }
      });

      logger.info(`Processing ${categories.length} categories for event ${event.id}`);

      for (const category of categories) {
        try {
          // Validate the category data before processing
          if (!category.eventId) {
            logger.error(`Missing eventId in category: ${category.name}`);
            continue;
          }

          // Check if this category already exists
          const existingCategory = existingCategories.find(c => c.name === category.name);

          if (existingCategory) {
            // Update existing category
            logger.info(`Updating existing category: ${category.name} (${existingCategory.id})`);

            await categoryRepo.updateCategory(existingCategory.id, {
              description: category.description || "",
              properties: category.properties
            });

            logger.info(`Successfully updated category: ${category.name}`);
          } else {
            // Create new category
            logger.info(`Creating new category: ${category.name}`);

            await categoryRepo.createCategory({
              ...category,
              description: category.description || ""
            });
            logger.info(`Successfully created category: ${category.name}`);
          }
        } catch (categoryError) {
          logger.error(`Error processing category: ${categoryError}`);
          // Continue with other categories even if one fails
        }
      }
    } catch (error) {
      logger.error(`Error processing categories: ${error}`);
      // Don't fail the entire event creation if categories fail
    }
  } else if (eventType.base_fields && eventType.base_fields.hasCategories) {
    try {
      const categoryRepo = new EventCategoryRepository();

      // Check if a default category already exists
      const existingCategories = await categoryRepo.getCategoriesByEventId(event.id);

      if (existingCategories.length === 0) {
        // Only create a default category if no categories exist
        const defaultCategory = {
          name: "Default Category",
          description: "Default category for this event",
          eventId: event.id,
          properties: {
            registrationOpen: true,
            bibRequireGeneration: false,
            registrationCount: 0
          }
        };

        logger.info(`Creating default category for event ${event.id}`);
        await categoryRepo.createCategory(defaultCategory);
      } else {
        logger.info(`Skipping default category creation as ${existingCategories.length} categories already exist`);
      }
    } catch (error) {
      logger.error(`Error handling default category: ${error}`);
      // Don't fail if default category creation fails
    }
  }

  // Create custom fields if provided
  if (formData.customFields && formData.customFields.length > 0) {
    try {
      const fieldRepo = new RegistrationFieldRepository();
      const fields = formData.customFields.map((field, index) => {
        // Create a properly structured RegistrationField object
        return {
          eventId: event.id,
          fieldId: field.id || `field_${index}`,
          fieldType: field.type || FieldType.TEXT,
          label: field.label,
          description: field.description || "",
          isRequired: field.required !== undefined ? field.required : false,
          isPublic: true,
          validationRules: undefined,
          defaultValue: field.defaultValue,
          options: field.options,
          orderIndex: index
        };
      });

      logger.info(`Creating ${fields.length} custom fields for event ${event.id}`);

      await fieldRepo.createOrUpdateBulkFields(event.id, fields);
    } catch (error) {
      logger.error(`Error creating custom fields: ${error}`);
      // Don't fail the entire event creation if custom fields fail
    }
  }

  // Revalidate the events page
  revalidatePath("/dashboard/events");

  // Return success
  return { success: true, error: null, data: event };
}

/**
 * Save an event as draft
 * @param formData Event form data
 * @returns Object indicating success or failure, with event data or error message
 */
export async function saveEventDraft(formData: z.infer<typeof eventFormSchema>) {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      console.warn('[DEBUG] saveEventDraft - Authentication required for saving draft');
      return {
        success: false,
        error: "Authentication required - Please sign in to save your draft",
        data: null
      };
    }

    // Also check the session to ensure it's valid
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      console.warn('[DEBUG] saveEventDraft - No valid session found');
      return {
        success: false,
        error: "Your session has expired. Please sign in again to save your draft.",
        data: null
      };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      console.error(`[DEBUG] saveEventDraft - Error fetching user ID for auth_user_id ${authUser.id}: ${userIdError?.message}`);

      // Check if the user exists by email as a fallback
      if (authUser.email) {
        const { data: userByEmail, error: emailError } = await supabase
          .from('users')
          .select('id')
          .eq('email', authUser.email)
          .maybeSingle();

        if (userByEmail && !emailError) {
          console.log(`[DEBUG] saveEventDraft - Found user by email instead of auth_user_id: ${authUser.email}`);
          const userId = userByEmail.id;

          // Update the auth_user_id for future requests
          await supabase
            .from('users')
            .update({ auth_user_id: authUser.id })
            .eq('id', userId);

          return await processDraftSave(supabase, formData, userId, authUser);
        }
      }

      // If we still can't find the user, create a new one
      console.log(`[DEBUG] saveEventDraft - Creating new user record for auth_user_id: ${authUser.id}`);

      // Generate a UUID for the user ID
      const userId = crypto.randomUUID();

      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          id: userId,
          auth_user_id: authUser.id,
          email: authUser.email || '',
          first_name: authUser.user_metadata?.full_name?.split(' ')[0] || 'User',
          last_name: authUser.user_metadata?.full_name?.split(' ').slice(1).join(' ') || '',
          role: 'user'
        })
        .select('id')
        .single();

      if (createError || !newUser) {
        console.error(`[DEBUG] saveEventDraft - Failed to create user record: ${createError?.message}`);
        return {
          success: false,
          error: "Failed to create user record. Please try again or contact support.",
          data: null
        };
      }

      return await processDraftSave(supabase, formData, newUser.id, authUser);
    }

    const userId = userData.id;
    return await processDraftSave(supabase, formData, userId, authUser);
  } catch (error) {
    console.error(`[DEBUG] saveEventDraft - Unexpected error:`, error);
    return {
      success: false,
      error: "Failed to save draft",
      data: null
    };
  }
}

// Helper function to process draft save after user authentication
async function processDraftSave(
  supabase: any,
  formData: z.infer<typeof eventFormSchema>,
  userId: string,
  authUser: any
) {
  console.log(`[DEBUG] saveEventDraft - Processing draft save for user ${userId}`);

  // Create event with draft status
  const eventData = {
    title: formData.title || "Untitled Event",
    slug: formData.title ? makeSlug(formData.title) : `draft-${Date.now()}`,
    description: formData.description || "",
    location: formData.location || "",
    country: formData.country || "",
    state: formData.state || "",
    city: formData.city || "",
    startDate: toOptionalDate(formData.startDate),
    endDate: toOptionalDate(formData.endDate),
    timezone: formData.timezone || "UTC",
    eventTypeId: formData.eventType || "default",
    organizerId: userId,
    createdBy: userId,
    status: "draft",
    // Add capacity and registration settings if provided
    totalCapacity: formData.totalCapacity === null ? undefined : formData.totalCapacity,
    registrationCloseDate: toOptionalDate(formData.registrationCloseDate),
    allowCategorySpecificClosingDates: formData.allowCategorySpecificClosingDates === null ? false : !!formData.allowCategorySpecificClosingDates,
    // Add T-shirt options if provided
    tshirtOptions: formData.tshirtOptions ? {
      enabled: formData.tshirtOptions.enabled === null ? false : !!formData.tshirtOptions.enabled,
      sizes: formData.tshirtOptions.sizes || [],
      description: formData.tshirtOptions.description || undefined,
      sizeChartImage: formData.tshirtOptions.sizeChartImage
    } : null,
    // Add emergency contact settings if provided
    emergencyContactSettings: formData.emergencyContactSettings,
    // Add image data if provided
    posterImage: formData.posterImage,
    coverImage: formData.coverImage,
    // Add gallery images if provided
    galleryImages: formData.galleryImages ? formData.galleryImages.filter(img => img !== null) as { url: string; path: string }[] : null,
  };

  console.log(`[DEBUG] saveEventDraft - Event data:`, eventData);

  // Create or update the event
  const eventRepo = new EventRepository();
  let event;

  // Ensure status is one of the allowed values
  const validStatus = (eventData.status === "draft" || eventData.status === "published" ||
    eventData.status === "cancelled" || eventData.status === "completed")
    ? eventData.status as "draft" | "published" | "cancelled" | "completed"
    : "draft" as const;

  // Create a new object with the validated status
  const validatedEventData = {
    ...eventData,
    status: validStatus
  } as const;

  if (formData.id) {
    // Update existing draft
    console.log(`[DEBUG] saveEventDraft - Updating existing draft with ID: ${formData.id}`);
    event = await eventRepo.updateEvent(formData.id, validatedEventData);
  } else {
    // Create new draft
    console.log(`[DEBUG] saveEventDraft - Creating new draft event`);
    const result = await eventRepo.createEvent(validatedEventData);
    if (!result.success || !result.data) {
      console.error(`[DEBUG] saveEventDraft - Failed to create draft:`, result.message);
      return {
        success: false,
        error: result.message || "Failed to create draft",
        data: null
      };
    }
    event = result.data;
  }

  // Process categories if provided
  if (formData.categories && formData.categories.length > 0 && event.id) {
    try {
      console.log(`[DEBUG] saveEventDraft - Processing ${formData.categories.length} categories`);
      const categoryRepo = new EventCategoryRepository();

      // Get existing categories
      const existingCategories = await categoryRepo.getCategoriesByEventId(event.id);

      for (const category of formData.categories) {
        // Skip empty categories
        if (!category.name) continue;

        // Check if this category already exists
        const existingCategory = existingCategories.find(c => c.name === category.name);

        if (existingCategory) {
          // Update existing category
          await categoryRepo.updateCategory(existingCategory.id, {
            description: category.description || "",
            properties: category.properties || {}
          });
        } else {
          // Create new category
          await categoryRepo.createCategory({
            name: category.name,
            description: category.description || "",
            eventId: event.id,
            properties: category.properties || {}
          });
        }
      }
    } catch (error) {
      console.error(`[DEBUG] saveEventDraft - Error processing categories:`, error);
      // Don't fail the entire draft save if categories fail
    }
  }

  // Process custom fields if provided
  if (formData.customFields && formData.customFields.length > 0 && event.id) {
    try {
      console.log(`[DEBUG] saveEventDraft - Processing ${formData.customFields.length} custom fields`);
      const fieldRepo = new RegistrationFieldRepository();

      const fields = formData.customFields.map((field, index) => ({
        eventId: event.id,
        fieldId: field.id || `field_${index}`,
        fieldType: field.type || FieldType.TEXT,
        label: field.label || "Untitled Field",
        description: field.description || "",
        isRequired: field.required !== undefined ? field.required : false,
        isPublic: true,
        validationRules: undefined,
        defaultValue: field.defaultValue,
        options: field.options,
        orderIndex: index
      }));

      await fieldRepo.createOrUpdateBulkFields(event.id, fields);
    } catch (error) {
      console.error(`[DEBUG] saveEventDraft - Error processing custom fields:`, error);
      // Don't fail the entire draft save if custom fields fail
    }
  }

  // Return success with event data
  console.log(`[DEBUG] saveEventDraft - Successfully saved draft event with ID: ${event.id}`);
  return {
    success: true,
    error: null,
    data: event
  };
}

/**
 * Get event categories for an event
 */
export async function getEventCategories(eventId: string): Promise<{
  success: boolean;
  data?: EventCategory[];
  error?: string;
}> {
  try {
    console.log(`[DEBUG] getEventCategories - Fetching categories for event ${eventId}`);

    const categoryRepository = new EventCategoryRepository();
    const categories = await categoryRepository.getCategoriesByEventId(eventId);

    console.log(`[DEBUG] getEventCategories - Found ${categories.length} categories for event ${eventId}`);

    return {
      success: true,
      data: categories
    };
  } catch (error) {
    console.error(`[DEBUG] Error fetching categories for event ${eventId}:`, error);

    // Return an empty array instead of an error to prevent cascading failures
    // This is more resilient and prevents the client from having to handle errors
    return {
      success: true,
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error fetching categories'
    };
  }
}

/**
 * Add a new category to an event
 */
export async function addEventCategory(
  eventId: string,
  category: {
    name: string;
    description?: string;
    properties?: Record<string, any>;
    [key: string]: any;
  }
): Promise<{
  success: boolean;
  data?: EventCategory;
  error?: string;
}> {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      return {
        success: false,
        error: 'Unauthorized - You must be logged in to add a category'
      };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    const userId = userData.id;

    // Verify the event exists and user has permission to modify it
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('created_by, organizer_id')
      .eq('id', eventId)
      .single();

    if (eventError || !event) {
      return {
        success: false,
        error: eventError?.message || `Event with ID ${eventId} not found`
      };
    }

    // Safe access with type assertion
    const createdBy = (event as any).created_by as string;
    const organizerId = (event as any).organizer_id as string;

    if (createdBy !== userId && organizerId !== userId) {
      return {
        success: false,
        error: 'Unauthorized - You do not have permission to modify this event'
      };
    }

    const categoryRepository = new EventCategoryRepository();

    // Check if a category with this name already exists for this event
    const existingCategories = await categoryRepository.getCategoriesByEventId(eventId);
    const existingCategory = existingCategories.find(c => c.name === category.name);

    if (existingCategory) {
      // Update the existing category instead of creating a new one
      console.log(`Updating existing category: ${category.name} (${existingCategory.id})`);

      // Prepare the update data
      const updateData: any = {
        description: category.description || ""
      };

      // Add properties if available
      if ('properties' in category) {
        updateData.properties = category.properties;
      } else {
        // Convert legacy format to properties
        updateData.properties = {};
        // We'll handle this in the else branch below
      }

      // Update the category
      const updatedCategory = await categoryRepository.updateCategory(
        existingCategory.id,
        updateData
      );

      return {
        success: true,
        data: updatedCategory
      };
    }

    // If no existing category, create a new one
    // Check if the category is already using the new JSON-based format
    if ('properties' in category) {
      // Already using the new format, just ensure eventId is set
      const newCategory = await categoryRepository.createCategory({
        name: category.name,
        description: category.description || "",
        eventId,
        properties: category.properties || {}
      });
      return {
        success: true,
        data: newCategory
      };
    }

    // Convert legacy format to JSON-based format
    const properties: Record<string, any> = {};

    // Add common properties - use 'in' check to handle 0 values
    // Handle capacity if it exists in the category object
    if ('capacity' in category && category.capacity !== undefined) {
      const capacityValue = (category as any).capacity;
      properties.capacity = typeof capacityValue === 'string' ? parseInt(capacityValue, 10) : Number(capacityValue);
    }
    // Handle price if it exists
    if ('price' in category && (category as any).price !== undefined) {
      const priceValue = (category as any).price;
      properties.price = typeof priceValue === 'string' ? parseFloat(priceValue) : Number(priceValue);
    }

    // Handle time values - ensure they're stored as time strings (HH:MM format)
    if ('startTime' in category && (category as any).startTime) {
      const startTimeValue = (category as any).startTime;
      // Make sure it's just the time part (HH:MM)
      const timeMatch = startTimeValue.match(/(\d{1,2}):(\d{2})/);
      if (timeMatch) {
        properties.startTime = timeMatch[0];
      } else {
        properties.startTime = startTimeValue;
      }
    }

    // Add event type-specific properties
    if ('earlyBirdPrice' in category)
      properties.earlyBirdPrice = typeof category.earlyBirdPrice === 'string' ? parseFloat(category.earlyBirdPrice) : Number(category.earlyBirdPrice);
    if ('earlyBirdEndDate' in category && category.earlyBirdEndDate)
      properties.earlyBirdEndDate = category.earlyBirdEndDate;
    if ('bibPrefix' in category && category.bibPrefix)
      properties.bibPrefix = category.bibPrefix;
    if ('bibStartNumber' in category)
      properties.bibStartNumber = typeof category.bibStartNumber === 'string' ? parseInt(category.bibStartNumber, 10) : Number(category.bibStartNumber);
    if ('bibRequireGeneration' in category)
      properties.bibRequireGeneration = Boolean(category.bibRequireGeneration);
    if ('registrationOpen' in category)
      properties.registrationOpen = Boolean(category.registrationOpen);
    if ('registrationCloseDate' in category && category.registrationCloseDate)
      properties.registrationCloseDate = category.registrationCloseDate;
    if ('registrationLimit' in category)
      properties.registrationLimit = typeof category.registrationLimit === 'string' ? parseInt(category.registrationLimit, 10) : Number(category.registrationLimit);

    // Always set registrationCount to 0 for new categories
    properties.registrationCount = 0;

    // Create a properly typed category object
    const categoryData: Omit<BaseEventCategory, "id" | "createdAt" | "updatedAt"> = {
      name: category.name,
      description: category.description || "",
      eventId,
      properties
    };

    const newCategory = await categoryRepository.createCategory(categoryData);

    return {
      success: true,
      data: newCategory
    };
  } catch (error) {
    console.error(`Error adding category to event ${eventId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error adding category'
    };
  }
}

/**
 * Get registration fields for an event
 */
export async function getEventFields(eventId: string): Promise<{
  success: boolean;
  data?: RegistrationField[];
  error?: string;
}> {
  try {
    console.log(`[DEBUG] getEventFields - Fetching fields for event ${eventId}`);

    const fieldRepository = new RegistrationFieldRepository();
    const fields = await fieldRepository.getFieldsByEventId(eventId);

    console.log(`[DEBUG] getEventFields - Found ${fields.length} fields for event ${eventId}`);

    return {
      success: true,
      data: fields
    };
  } catch (error) {
    console.error(`[DEBUG] Error fetching fields for event ${eventId}:`, error);

    // Return an empty array instead of an error to prevent cascading failures
    // This is more resilient and prevents the client from having to handle errors
    return {
      success: true,
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error fetching fields'
    };
  }
}

/**
 * Add or update registration fields for an event
 */
export async function updateEventFields(
  eventId: string,
  fields: Omit<RegistrationField, 'id' | 'createdAt' | 'updatedAt'>[]
): Promise<{
  success: boolean;
  data?: RegistrationField[];
  error?: string;
}> {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      return {
        success: false,
        error: 'Unauthorized - You must be logged in to update fields'
      };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    const userId = userData.id;

    // Verify the event exists and user has permission to modify it
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('created_by, organizer_id')
      .eq('id', eventId)
      .single();

    if (eventError || !event) {
      return {
        success: false,
        error: eventError?.message || `Event with ID ${eventId} not found`
      };
    }

    // Safe access with type assertion
    const createdBy = (event as any).created_by as string;
    const organizerId = (event as any).organizer_id as string;

    if (createdBy !== userId && organizerId !== userId) {
      return {
        success: false,
        error: 'Unauthorized - You do not have permission to modify this event'
      };
    }

    const fieldRepository = new RegistrationFieldRepository();
    // Make sure each field has the eventId property
    const fieldsWithEventId = fields.map(field => ({
      ...field,
      eventId
    }));
    const updatedFields = await fieldRepository.createOrUpdateBulkFields(eventId, fieldsWithEventId);

    return {
      success: true,
      data: updatedFields
    };
  } catch (error) {
    console.error(`Error updating fields for event ${eventId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error updating fields'
    };
  }
}

/**
 * Delete a registration field
 */
export async function deleteEventField(fieldId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      return {
        success: false,
        error: 'Unauthorized - You must be logged in to delete fields'
      };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    const userId = userData.id;

    const fieldRepository = new RegistrationFieldRepository();
    const field = await fieldRepository.getFieldById(fieldId);

    if (!field) {
      return {
        success: false,
        error: `Field with ID ${fieldId} not found`
      };
    }

    // Verify the user has permission to modify this event
    const { data: event, error: eventError } = await supabase
      .from('events')
      .select('created_by, organizer_id')
      .eq('id', field.eventId)
      .single();

    if (eventError || !event) {
      return {
        success: false,
        error: eventError?.message || `Event with ID ${field.eventId} not found`
      };
    }

    // Safe access with type assertion
    const createdBy = (event as any).created_by as string;
    const organizerId = (event as any).organizer_id as string;

    if (createdBy !== userId && organizerId !== userId) {
      return {
        success: false,
        error: 'Unauthorized - You do not have permission to modify this event'
      };
    }

    await fieldRepository.deleteField(fieldId);

    return {
      success: true
    };
  } catch (error) {
    console.error(`Error deleting field ${fieldId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error deleting field'
    };
  }
}

/**
 * Publish an event
 */
export async function publishEvent(eventId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      logger.error('User authentication failed in publishEvent - no auth user found');
      return {
        success: false,
        error: 'Unauthorized - You must be logged in to publish an event'
      };
    }

    // Also check the session to ensure it's valid
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      logger.error('User authentication failed in publishEvent - no valid session');
      return {
        success: false,
        error: 'Your session has expired. Please sign in again.'
      };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      logger.error(`Error fetching user ID for auth_user_id ${authUser.id}: ${userIdError?.message}`);

      // Check if the user exists by email as a fallback
      if (authUser.email) {
        const { data: userByEmail, error: emailError } = await supabase
          .from('users')
          .select('id')
          .eq('email', authUser.email)
          .maybeSingle();

        if (userByEmail && !emailError) {
          logger.info(`Found user by email instead of auth_user_id: ${authUser.email}`);
          const userId = userByEmail.id;

          // Update the auth_user_id for future requests
          await supabase
            .from('users')
            .update({ auth_user_id: authUser.id })
            .eq('id', userId);

          return await processPublishEvent(supabase, eventId, userId);
        }
      }

      return {
        success: false,
        error: 'User not found - Please try signing out and signing in again'
      };
    }

    const userId = userData.id;

    return await processPublishEvent(supabase, eventId, userId);
  } catch (error) {
    logger.error(`Unexpected error in publishEvent: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error publishing event'
    };
  }
}

// Helper function to process event publishing after user authentication
async function processPublishEvent(
  supabase: any,
  eventId: string,
  userId: string
): Promise<{
  success: boolean;
  error?: string;
}> {

  // Verify the user has permission to publish this event
  const { data: event, error: eventError } = await supabase
    .from('events')
    .select('created_by, organizer_id')
    .eq('id', eventId)
    .single();

  if (eventError || !event) {
    logger.error(`Event with ID ${eventId} not found: ${eventError?.message}`);
    return {
      success: false,
      error: eventError?.message || `Event with ID ${eventId} not found`
    };
  }

  // Safe access with type assertion
  const createdBy = (event as any).created_by as string;
  const organizerId = (event as any).organizer_id as string;

  if (createdBy !== userId && organizerId !== userId) {
    logger.error(`User ${userId} does not have permission to publish event ${eventId}`);
    return {
      success: false,
      error: 'Unauthorized - You do not have permission to publish this event'
    };
  }

  // Update the event status to published
  const { error: updateError } = await supabase
    .from('events')
    .update({ status: 'published' })
    .eq('id', eventId);

  if (updateError) {
    logger.error(`Error updating event status: ${updateError.message}`);
    return {
      success: false,
      error: updateError.message
    };
  }

  // Revalidate the events page
  revalidatePath("/dashboard/events");
  revalidatePath("/events");

  return {
    success: true
  };
}

/**
 * Unpublish an event
 */
export async function unpublishEvent(eventId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      logger.error('User authentication failed in unpublishEvent - no auth user found');
      return {
        success: false,
        error: 'Unauthorized - You must be logged in to unpublish an event'
      };
    }

    // Also check the session to ensure it's valid
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      logger.error('User authentication failed in unpublishEvent - no valid session');
      return {
        success: false,
        error: 'Your session has expired. Please sign in again.'
      };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      logger.error(`Error fetching user ID for auth_user_id ${authUser.id}: ${userIdError?.message}`);

      // Check if the user exists by email as a fallback
      if (authUser.email) {
        const { data: userByEmail, error: emailError } = await supabase
          .from('users')
          .select('id')
          .eq('email', authUser.email)
          .maybeSingle();

        if (userByEmail && !emailError) {
          logger.info(`Found user by email instead of auth_user_id: ${authUser.email}`);
          const userId = userByEmail.id;

          // Update the auth_user_id for future requests
          await supabase
            .from('users')
            .update({ auth_user_id: authUser.id })
            .eq('id', userId);

          return await processUnpublishEvent(supabase, eventId, userId);
        }
      }

      return {
        success: false,
        error: 'User not found - Please try signing out and signing in again'
      };
    }

    const userId = userData.id;

    return await processUnpublishEvent(supabase, eventId, userId);
  } catch (error) {
    logger.error(`Unexpected error in unpublishEvent: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error unpublishing event'
    };
  }
}

// Helper function to process event unpublishing after user authentication
async function processUnpublishEvent(
  supabase: any,
  eventId: string,
  userId: string
): Promise<{
  success: boolean;
  error?: string;
}> {

  // Verify the user has permission to unpublish this event
  const { data: event, error: eventError } = await supabase
    .from('events')
    .select('created_by, organizer_id')
    .eq('id', eventId)
    .single();

  if (eventError || !event) {
    return {
      success: false,
      error: eventError?.message || `Event with ID ${eventId} not found`
    };
  }

  // Safe access with type assertion
  const createdBy = (event as any).created_by as string;
  const organizerId = (event as any).organizer_id as string;

  if (createdBy !== userId && organizerId !== userId) {
    return {
      success: false,
      error: 'Unauthorized - You do not have permission to unpublish this event'
    };
  }

  // Update the event status to draft
  const { error: updateError } = await supabase
    .from('events')
    .update({
      status: 'draft',
      published_at: null,
      updated_at: new Date().toISOString()
    })
    .eq('id', eventId);

  if (updateError) {
    return {
      success: false,
      error: updateError.message
    };
  }

  // Revalidate the events page
  revalidatePath("/dashboard/events");
  revalidatePath(`/events/${eventId}`);

  return {
    success: true
  };
}

export async function getEvent(eventId: string): Promise<{
  success: boolean;
  data?: Event;
  error?: string;
}> {
  console.log(`[SERVER] getEvent - Starting to fetch event with ID: ${eventId}`);

  try {
    // Add retry logic for network errors
    let supabase;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        // First check if the user is authenticated
        supabase = await createClient();
        console.log(`[SERVER] getEvent - Supabase client created for event ID: ${eventId}`);
        break; // If successful, break out of the retry loop
      } catch (clientError) {
        retryCount++;
        console.error(`[SERVER] Error creating Supabase client (attempt ${retryCount}/${maxRetries}):`, clientError);

        if (retryCount >= maxRetries) {
          console.error('[SERVER] Max retries reached for client creation, giving up');
          return {
            success: false,
            error: `Failed to create Supabase client after ${maxRetries} attempts: ${clientError instanceof Error ? clientError.message : String(clientError)}`
          };
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, retryCount), 5000);
        console.log(`[SERVER] Retrying client creation in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // Now try to get the user with retry logic
    let user;
    let userError;
    retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // Make sure supabase is defined before using it
        if (!supabase) {
          throw new Error('Supabase client is undefined');
        }
        const result = await supabase.auth.getUser();
        user = result.data.user;
        userError = result.error;
        break; // If successful, break out of the retry loop
      } catch (authError) {
        retryCount++;
        console.error(`[SERVER] Error getting user (attempt ${retryCount}/${maxRetries}):`, authError);

        if (retryCount >= maxRetries) {
          console.error('[SERVER] Max retries reached for getUser, giving up');
          return {
            success: false,
            error: `Failed to get user after ${maxRetries} attempts: ${authError instanceof Error ? authError.message : String(authError)}`
          };
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, retryCount), 5000);
        console.log(`[SERVER] Retrying getUser in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // Log detailed authentication information for debugging
    console.log(`[DEBUG] getEvent - Auth check for event ${eventId}:`, {
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      authError: userError?.message,
      timestamp: new Date().toISOString()
    });

    if (!user) {
      console.error(`[DEBUG] User not authenticated when fetching event ${eventId}`);

      // Try to get the session as a fallback
      // Make sure supabase is defined before using it
      if (!supabase) {
        throw new Error('Supabase client is undefined');
      }
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        console.error(`[DEBUG] No session found either for event ${eventId}`);
        return {
          success: false,
          error: 'Authentication required'
        };
      } else {
        // We have a session but no user - this is unusual but we'll try to proceed
        console.log(`[DEBUG] Session found but no user for event ${eventId}, trying to proceed with session user`);
        user = session.user;
      }
    }

    // Proceed with fetching the event even if we're not sure about permissions yet
    // This prevents unnecessary authentication errors
    const eventRepository = new EventRepository();

    // Add retry logic for fetching the event
    let event;
    let eventRetryCount = 0;
    const eventMaxRetries = 3;

    while (eventRetryCount < eventMaxRetries) {
      try {
        event = await eventRepository.getEventById(eventId);
        break; // If successful, break out of the retry loop
      } catch (fetchError) {
        eventRetryCount++;
        console.error(`[SERVER] Error fetching event (attempt ${eventRetryCount}/${eventMaxRetries}):`, fetchError);

        if (eventRetryCount >= eventMaxRetries) {
          console.error('[SERVER] Max retries reached for event fetch, giving up');
          return {
            success: false,
            error: `Failed to fetch event after ${eventMaxRetries} attempts: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`
          };
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, eventRetryCount), 5000);
        console.log(`[SERVER] Retrying event fetch in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    if (!event) {
      console.error(`[DEBUG] Event with ID ${eventId} not found`);
      return {
        success: false,
        error: `Event with ID ${eventId} not found`
      };
    }

    // Now check if the user has permission to access this event
    try {
      // Add retry logic for user data lookup
      let userData;
      let userDataError;
      let userLookupRetryCount = 0;
      const userLookupMaxRetries = 3;

      while (userLookupRetryCount < userLookupMaxRetries) {
        try {
          // Make sure supabase is defined before using it
          if (!supabase) {
            throw new Error('Supabase client is undefined');
          }
          const result = await supabase
            .from('users')
            .select('id, role')
            .eq('auth_user_id', user.id)
            .single();

          userData = result.data;
          userDataError = result.error;
          break; // If successful, break out of the retry loop
        } catch (lookupError) {
          userLookupRetryCount++;
          console.error(`[SERVER] Error looking up user data (attempt ${userLookupRetryCount}/${userLookupMaxRetries}):`, lookupError);

          if (userLookupRetryCount >= userLookupMaxRetries) {
            console.error('[SERVER] Max retries reached for user data lookup, continuing with null userData');
            break;
          }

          // Wait before retrying (exponential backoff)
          const delay = Math.min(1000 * Math.pow(2, userLookupRetryCount), 5000);
          console.log(`[SERVER] Retrying user data lookup in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      console.log(`[DEBUG] User data lookup for event ${eventId}:`, {
        found: !!userData,
        error: userDataError?.message,
        userId: userData?.id,
        userRole: userData?.role
      });

      if (!userData) {
        // Try to find user by email as fallback with retry logic
        let userByEmail;
        let emailError;
        let emailLookupRetryCount = 0;
        const emailLookupMaxRetries = 3;

        while (emailLookupRetryCount < emailLookupMaxRetries) {
          try {
            // Make sure supabase is defined before using it
            if (!supabase) {
              throw new Error('Supabase client is undefined');
            }
            // Make sure user.email is defined before using it
            if (!user || !user.email) {
              throw new Error('User email is undefined');
            }
            const result = await supabase
              .from('users')
              .select('id, role')
              .eq('email', user.email)
              .single();

            userByEmail = result.data;
            emailError = result.error;
            break; // If successful, break out of the retry loop
          } catch (lookupError) {
            emailLookupRetryCount++;
            console.error(`[SERVER] Error looking up user by email (attempt ${emailLookupRetryCount}/${emailLookupMaxRetries}):`, lookupError);

            if (emailLookupRetryCount >= emailLookupMaxRetries) {
              console.error('[SERVER] Max retries reached for user lookup by email, continuing with null userByEmail');
              break;
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.min(1000 * Math.pow(2, emailLookupRetryCount), 5000);
            console.log(`[SERVER] Retrying user lookup by email in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (userByEmail) {
          console.log(`[DEBUG] Found user by email instead of auth_user_id for event ${eventId}`);

          // Update the auth_user_id for future queries with retry logic
          let updateRetryCount = 0;
          const updateMaxRetries = 3;
          while (updateRetryCount < updateMaxRetries) {
            try {
              // Make sure supabase is defined before using it
              if (!supabase) {
                throw new Error('Supabase client is undefined');
              }
              // Make sure user.id and userByEmail.id are defined before using them
              if (!user || !user.id || !userByEmail || !userByEmail.id) {
                throw new Error('User ID or userByEmail ID is undefined');
              }
              // Use a different property name to avoid TypeScript errors
              const updateData: Record<string, any> = {};
              updateData.auth_user_id = user.id;

              await supabase
                .from('users')
                .update(updateData)
                .eq('id', userByEmail.id);

              console.log(`[DEBUG] Successfully updated auth_user_id for user ${userByEmail.id}`);
              break; // If successful, break out of the retry loop
            } catch (updateError) {
              updateRetryCount++;
              console.error(`[SERVER] Error updating auth_user_id (attempt ${updateRetryCount}/${updateMaxRetries}):`, updateError);

              if (updateRetryCount >= updateMaxRetries) {
                console.error('[SERVER] Max retries reached for auth_user_id update, continuing anyway');
                break;
              }

              // Wait before retrying (exponential backoff)
              const delay = Math.min(1000 * Math.pow(2, updateRetryCount), 5000);
              console.log(`[SERVER] Retrying auth_user_id update in ${delay}ms...`);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }

          // Use this user data
          userData = userByEmail;
        } else {
          console.error(`[DEBUG] User record not found when fetching event ${eventId}`);
          // Return the event anyway - we'll let the client handle permission issues
          // This prevents unnecessary authentication redirects
          return {
            success: true,
            data: event
          };
        }
      }

      // Check if the user is the organizer or an admin
      if (event.organizerId !== userData.id) {
        // User is not the organizer, check if they're an admin
        const isAdmin = userData.role === 'admin' || userData.role === 'super_admin';

        if (!isAdmin) {
          console.log(`[DEBUG] User ${userData.id} does not have permission to access event ${eventId}`);
          // Return the event anyway but mark it as unauthorized
          // The client can handle this appropriately
          // Create a new object with the event data and add the _unauthorized flag
          const eventWithFlag = {
            ...event
          };
          // Add the _unauthorized flag using a type assertion
          (eventWithFlag as any)._unauthorized = true;

          return {
            success: true,
            data: eventWithFlag
          };
        }
      }
    } catch (permissionError) {
      console.error(`[DEBUG] Error checking permissions for event ${eventId}:`, permissionError);
      // Return the event anyway - we'll let the client handle permission issues
      // This prevents unnecessary authentication redirects
      return {
        success: true,
        data: event
      };
    }

    return {
      success: true,
      data: event
    };
  } catch (error) {
    console.error(`Error fetching event ${eventId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error fetching event'
    };
  }
}

export async function getEventsByOrganizer(organizerId: string): Promise<{
  success: boolean;
  data?: Event[];
  error?: string;
}> {
  try {
    const eventRepository = new EventRepository();
    const events = await eventRepository.getEventsByOrganizerId(organizerId);

    return {
      success: true,
      data: events
    };
  } catch (error) {
    console.error(`Error fetching events for organizer ${organizerId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error fetching events'
    };
  }
}

export async function updateEvent(
  eventId: string,
  data: Partial<Omit<Event, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<{
  success: boolean;
  data?: Event;
  error?: string;
}> {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      return {
        success: false,
        error: 'Unauthorized - You must be logged in to update an event'
      };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      return {
        success: false,
        error: 'User not found'
      };
    }

    const userId = userData.id;

    const eventRepository = new EventRepository();
    const event = await eventRepository.updateEvent(eventId, data);

    return {
      success: true,
      data: event
    };
  } catch (error) {
    console.error(`Error updating event ${eventId}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error updating event'
    };
  }
}

/**
 * Delete an event by ID
 */
export async function deleteEvent(eventId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      logger.error('User authentication failed in deleteEvent - no auth user found');
      return {
        success: false,
        error: 'Unauthorized - You must be logged in to delete an event'
      };
    }

    // Also check the session to ensure it's valid
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      logger.error('User authentication failed in deleteEvent - no valid session');
      return {
        success: false,
        error: 'Your session has expired. Please sign in again.'
      };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      logger.error(`Error fetching user ID for auth_user_id ${authUser.id}: ${userIdError?.message}`);

      // Check if the user exists by email as a fallback
      if (authUser.email) {
        const { data: userByEmail, error: emailError } = await supabase
          .from('users')
          .select('id')
          .eq('email', authUser.email)
          .maybeSingle();

        if (userByEmail && !emailError) {
          logger.info(`Found user by email instead of auth_user_id: ${authUser.email}`);
          const userId = userByEmail.id;

          // Update the auth_user_id for future requests
          await supabase
            .from('users')
            .update({ auth_user_id: authUser.id })
            .eq('id', userId);

          return await processDeleteEvent(supabase, eventId, userId);
        }
      }

      return {
        success: false,
        error: 'User not found - Please try signing out and signing in again'
      };
    }

    const userId = userData.id;

    return await processDeleteEvent(supabase, eventId, userId);
  } catch (error) {
    logger.error(`Unexpected error in deleteEvent: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error deleting event'
    };
  }
}

// Helper function to process event deletion after user authentication
async function processDeleteEvent(
  supabase: any,
  eventId: string,
  userId: string
): Promise<{
  success: boolean;
  error?: string;
}> {

  const eventRepository = new EventRepository();

  // Verify the user is authorized to delete this event
  const eventResult = await eventRepository.findById(eventId);

  if (!eventResult.success || !eventResult.data) {
    return {
      success: false,
      error: `Event with ID ${eventId} not found`
    };
  }

  const event = eventResult.data;

  if (event.organizer_id !== userId && event.created_by !== userId) {
    return {
      success: false,
      error: 'Unauthorized - You do not have permission to delete this event'
    };
  }

  // Delete the event
  await eventRepository.deleteEvent(eventId);

  return {
    success: true
  };
}

/**
 * Duplicate an event by ID
 * Creates a new event with the same details but with "Copy of" prefix in the title
 */
export async function duplicateEvent(eventId: string): Promise<{
  success: boolean;
  data?: Event;
  error?: string;
}> {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      logger.error('User authentication failed in duplicateEvent - no auth user found');
      return {
        success: false,
        error: 'Unauthorized - You must be logged in to duplicate an event'
      };
    }

    // Also check the session to ensure it's valid
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      logger.error('User authentication failed in duplicateEvent - no valid session');
      return {
        success: false,
        error: 'Your session has expired. Please sign in again.'
      };
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      logger.error(`Error fetching user ID for auth_user_id ${authUser.id}: ${userIdError?.message}`);

      // Check if the user exists by email as a fallback
      if (authUser.email) {
        const { data: userByEmail, error: emailError } = await supabase
          .from('users')
          .select('id')
          .eq('email', authUser.email)
          .maybeSingle();

        if (userByEmail && !emailError) {
          logger.info(`Found user by email instead of auth_user_id: ${authUser.email}`);
          const userId = userByEmail.id;

          // Update the auth_user_id for future requests
          await supabase
            .from('users')
            .update({ auth_user_id: authUser.id })
            .eq('id', userId);

          return await processDuplicateEvent(supabase, eventId, userId);
        }
      }

      return {
        success: false,
        error: 'User not found - Please try signing out and signing in again'
      };
    }

    const userId = userData.id;

    return await processDuplicateEvent(supabase, eventId, userId);
  } catch (error) {
    logger.error(`Unexpected error in duplicateEvent: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error duplicating event'
    };
  }
}

// Helper function to process event duplication after user authentication
async function processDuplicateEvent(
  supabase: any,
  eventId: string,
  userId: string
): Promise<{
  success: boolean;
  data?: Event;
  error?: string;
}> {

  // Get the original event
  const eventRepository = new EventRepository();
  const originalEvent = await eventRepository.getEventById(eventId);

  if (!originalEvent) {
    return {
      success: false,
      error: `Event with ID ${eventId} not found`
    };
  }

  // Verify the user is authorized to duplicate this event
  if (originalEvent.organizerId !== userId && originalEvent.createdBy !== userId) {
    return {
      success: false,
      error: 'Unauthorized - You do not have permission to duplicate this event'
    };
  }

  // Create a new event based on the original, but only include the fields we need
  const newEventData = {
    title: `Copy of ${originalEvent.title}`,
    description: originalEvent.description,
    location: originalEvent.location,
    country: originalEvent.country,
    state: originalEvent.state,
    city: originalEvent.city,
    startDate: originalEvent.startDate,
    endDate: originalEvent.endDate,
    timezone: originalEvent.timezone,
    eventTypeId: originalEvent.eventTypeId,
    organizerId: userId,
    createdBy: userId,
    status: 'draft' as const, // Always create as draft
    totalCapacity: originalEvent.totalCapacity,
    registrationCloseDate: originalEvent.registrationCloseDate,
    allowCategorySpecificClosingDates: originalEvent.allowCategorySpecificClosingDates,
    tshirtOptions: originalEvent.tshirtOptions,
    emergencyContactSettings: originalEvent.emergencyContactSettings,
    posterImage: originalEvent.posterImage,
    coverImage: originalEvent.coverImage,
    galleryImages: originalEvent.galleryImages,
    slug: makeSlug(`Copy of ${originalEvent.title}`)
  };

  // Create the new event
  const result = await eventRepository.createEvent(newEventData);

  if (!result.success || !result.data) {
    return {
      success: false,
      error: result.message || 'Failed to duplicate event'
    };
  }

  const newEvent = result.data;

  // Duplicate categories if they exist
  if (originalEvent.categories && originalEvent.categories.length > 0) {
    try {
      const categoryRepo = new EventCategoryRepository();
      const originalCategories = await categoryRepo.getCategoriesByEventId(eventId);

      for (const category of originalCategories) {
        // Create a properly typed category object
        const categoryData: Omit<BaseEventCategory, "id" | "createdAt" | "updatedAt"> = {
          name: category.name,
          description: category.description || "",
          eventId: newEvent.id,
          properties: category.properties || {}
        };

        await categoryRepo.createCategory(categoryData);
      }
    } catch (error) {
      logger.error(`Error duplicating categories: ${error}`);
      // Continue even if category duplication fails
    }
  }

  // Duplicate custom fields if they exist
  try {
    const fieldRepo = new RegistrationFieldRepository();
    const originalFields = await fieldRepo.getFieldsByEventId(eventId);

    if (originalFields.length > 0) {
      const newFields = originalFields.map((field, index) => ({
        ...field,
        id: undefined, // Remove ID to create new fields
        eventId: newEvent.id,
        orderIndex: index,
        createdAt: undefined,
        updatedAt: undefined
      }));

      await fieldRepo.createOrUpdateBulkFields(newEvent.id, newFields);
    }
  } catch (error) {
    logger.error(`Error duplicating custom fields: ${error}`);
    // Continue even if field duplication fails
  }

  // Revalidate the events page
  revalidatePath("/dashboard/events");

  return {
    success: true,
    data: newEvent
  };
}