import { createClient } from '@/lib/supabase/pages-client';
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    const supabase = await createClient();
    
    const { data: categories, error } = await supabase
      .from('event_categories')
      .select('name, description')
      .order('name');

    if (error) {
      console.error("Error fetching categories:", error);
      return new NextResponse("Error fetching categories", { status: 500 });
    }

    return NextResponse.json(categories);
  } catch (error) {
    console.error("Error in categories API:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 