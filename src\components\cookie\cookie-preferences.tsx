'use client'

import { useState, useEffect } from 'react'
import { useCookieConsent } from '@/contexts/cookie-consent-context'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { CookieConsentOptions } from '@/lib/cookies/consent'

export function CookiePreferences() {
  const {
    showPreferences,
    closePreferences,
    cookieOptions,
    updateOptions,
    expirationDate,
  } = useCookieConsent()
  
  const [localOptions, setLocalOptions] = useState<CookieConsentOptions>({
    necessary: true,
    analytics: false,
    marketing: false,
    preferences: false,
  })
  
  // Update local options when cookie options change
  useEffect(() => {
    setLocalOptions(cookieOptions)
  }, [cookieOptions])
  
  // Handle option change
  const handleOptionChange = (option: keyof CookieConsentOptions, value: boolean) => {
    setLocalOptions(prev => ({
      ...prev,
      [option]: value,
    }))
  }
  
  // Save preferences
  const savePreferences = () => {
    updateOptions(localOptions)
    closePreferences()
  }
  
  return (
    <Dialog open={showPreferences} onOpenChange={closePreferences}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Cookie Preferences</DialogTitle>
          <DialogDescription>
            Customize your cookie preferences. These settings will be stored for 12 months.
            {expirationDate && (
              <p className="mt-2 text-xs">
                Your current preferences expire on: {expirationDate}
              </p>
            )}
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4 space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="necessary">Necessary Cookies</Label>
              <p className="text-sm text-muted-foreground">
                Required for the website to function properly. Cannot be disabled.
              </p>
            </div>
            <Switch
              id="necessary"
              checked={true}
              disabled={true}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="analytics">Analytics Cookies</Label>
              <p className="text-sm text-muted-foreground">
                Help us understand how visitors interact with our website.
              </p>
            </div>
            <Switch
              id="analytics"
              checked={localOptions.analytics}
              onCheckedChange={(checked) => handleOptionChange('analytics', checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="marketing">Marketing Cookies</Label>
              <p className="text-sm text-muted-foreground">
                Used to track visitors across websites to display relevant advertisements.
              </p>
            </div>
            <Switch
              id="marketing"
              checked={localOptions.marketing}
              onCheckedChange={(checked) => handleOptionChange('marketing', checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="preferences">Preference Cookies</Label>
              <p className="text-sm text-muted-foreground">
                Enable the website to remember information that changes the way the website behaves or looks.
              </p>
            </div>
            <Switch
              id="preferences"
              checked={localOptions.preferences}
              onCheckedChange={(checked) => handleOptionChange('preferences', checked)}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={closePreferences}>
            Cancel
          </Button>
          <Button onClick={savePreferences}>
            Save Preferences
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
