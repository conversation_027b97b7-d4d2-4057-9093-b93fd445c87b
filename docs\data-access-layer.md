# Data Access Layer with Authorization Checks

## Overview

This document outlines the implementation of a proper data access layer with authorization checks for the Fuiyoo application. The data access layer provides a consistent interface for interacting with the Supabase database while ensuring proper authorization at every level.

## Architecture

The data access layer follows a repository pattern with the following components:

1. **Base Repository**: Provides common CRUD operations with built-in authorization checks
2. **Entity-Specific Repositories**: Extend the base repository with entity-specific operations
3. **Service Layer**: Implements business logic using repositories
4. **Authorization Middleware**: Ensures proper authorization before data access

## Implementation

### Base Repository

```typescript
// src/lib/repositories/base-repository.ts
import { createClient } from '@/lib/supabase/server';
import { createBrowserClient } from '@/lib/supabase/client';
import { PostgrestError } from '@supabase/supabase-js';

export class BaseRepository<T extends { id: string }> {
  protected tableName: string;

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  /**
   * Get the current user ID from Supabase Auth
   */
  protected async getCurrentUserId(): Promise<string> {
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      throw new Error('Unauthorized: User not authenticated');
    }

    // Get the internal user ID from the users table
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', session.user.id)
      .single();

    if (error || !data) {
      throw new Error('User not found in database');
    }

    return data.id;
  }

  /**
   * Check if the user has access to the specified resource
   */
  protected async checkAccess(resourceId: string, action: 'read' | 'write' | 'delete'): Promise<boolean> {
    const userId = await this.getCurrentUserId();
    const supabase = await createClient();

    // Check if the resource belongs to the user or if the user has the required permissions
    const { data, error } = await supabase
      .from(this.tableName)
      .select('id, user_id, organization_id')
      .eq('id', resourceId)
      .single();

    if (error || !data) {
      return false;
    }

    // Direct ownership
    if (data.user_id === userId) {
      return true;
    }

    // Organization membership (if applicable)
    if (data.organization_id) {
      const { data: membership, error: membershipError } = await supabase
        .from('organization_members')
        .select('role')
        .eq('organization_id', data.organization_id)
        .eq('user_id', userId)
        .single();

      if (membershipError || !membership) {
        return false;
      }

      // Check role permissions
      switch (action) {
        case 'read':
          return true; // All members can read
        case 'write':
          return ['admin', 'editor'].includes(membership.role);
        case 'delete':
          return membership.role === 'admin';
        default:
          return false;
      }
    }

    return false;
  }

  /**
   * Get a single item by ID with authorization check
   */
  async getById(id: string): Promise<T | null> {
    const hasAccess = await this.checkAccess(id, 'read');
    if (!hasAccess) {
      throw new Error(`Unauthorized: Cannot read ${this.tableName} with ID ${id}`);
    }

    const supabase = await createClient();
    const { data, error } = await supabase
      .from(this.tableName)
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching ${this.tableName}:`, error);
      return null;
    }

    return data as T;
  }

  /**
   * Get items owned by the current user
   */
  async getByCurrentUser(): Promise<T[]> {
    const userId = await this.getCurrentUserId();
    const supabase = await createClient();

    const { data, error } = await supabase
      .from(this.tableName)
      .select('*')
      .eq('user_id', userId);

    if (error) {
      console.error(`Error fetching ${this.tableName} for user:`, error);
      return [];
    }

    return data as T[];
  }

  /**
   * Create a new item
   */
  async create(item: Omit<T, 'id'>): Promise<T | null> {
    const userId = await this.getCurrentUserId();
    const supabase = await createClient();

    // Ensure the item is associated with the current user
    const itemWithUserId = {
      ...item,
      user_id: userId,
    };

    const { data, error } = await supabase
      .from(this.tableName)
      .insert(itemWithUserId)
      .select()
      .single();

    if (error) {
      console.error(`Error creating ${this.tableName}:`, error);
      return null;
    }

    return data as T;
  }

  /**
   * Update an item with authorization check
   */
  async update(id: string, updates: Partial<T>): Promise<T | null> {
    const hasAccess = await this.checkAccess(id, 'write');
    if (!hasAccess) {
      throw new Error(`Unauthorized: Cannot update ${this.tableName} with ID ${id}`);
    }

    const supabase = await createClient();

    // Prevent changing the user_id
    const { user_id, ...safeUpdates } = updates as any;

    const { data, error } = await supabase
      .from(this.tableName)
      .update(safeUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating ${this.tableName}:`, error);
      return null;
    }

    return data as T;
  }

  /**
   * Delete an item with authorization check
   */
  async delete(id: string): Promise<boolean> {
    const hasAccess = await this.checkAccess(id, 'delete');
    if (!hasAccess) {
      throw new Error(`Unauthorized: Cannot delete ${this.tableName} with ID ${id}`);
    }

    const supabase = await createClient();
    const { error } = await supabase
      .from(this.tableName)
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting ${this.tableName}:`, error);
      return false;
    }

    return true;
  }
}
```

### Entity-Specific Repository Example

```typescript
// src/lib/repositories/event-repository.ts
import { BaseRepository } from './base-repository';
import { Event, EventWithDetails } from '@/lib/types';

export class EventRepository extends BaseRepository<Event> {
  constructor() {
    super('events');
  }

  /**
   * Get published events (public access)
   */
  async getPublishedEvents(limit = 10, offset = 0): Promise<Event[]> {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from(this.tableName)
      .select('*')
      .eq('status', 'published')
      .order('start_date', { ascending: true })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching published events:', error);
      return [];
    }

    return data as Event[];
  }

  /**
   * Get event with related details
   */
  async getEventWithDetails(id: string): Promise<EventWithDetails | null> {
    const hasAccess = await this.checkAccess(id, 'read');
    if (!hasAccess) {
      // For published events, allow public access
      const supabase = await createClient();
      const { data: eventData, error: eventError } = await supabase
        .from(this.tableName)
        .select('*')
        .eq('id', id)
        .eq('status', 'published')
        .single();

      if (eventError || !eventData) {
        throw new Error(`Unauthorized: Cannot read event with ID ${id}`);
      }
    }

    const supabase = await createClient();
    const { data, error } = await supabase
      .from(this.tableName)
      .select(`
        *,
        organization:organization_id(*),
        ticket_types:ticket_types(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching event with details:', error);
      return null;
    }

    return data as EventWithDetails;
  }

  /**
   * Get events for an organization with authorization check
   */
  async getEventsByOrganization(organizationId: string): Promise<Event[]> {
    // Check if user is a member of the organization
    const userId = await this.getCurrentUserId();
    const supabase = await createClient();

    const { data: membership, error: membershipError } = await supabase
      .from('organization_members')
      .select('*')
      .eq('organization_id', organizationId)
      .eq('user_id', userId);

    if (membershipError || !membership || membership.length === 0) {
      throw new Error(`Unauthorized: Not a member of organization ${organizationId}`);
    }

    const { data, error } = await supabase
      .from(this.tableName)
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching events for organization:', error);
      return [];
    }

    return data as Event[];
  }
}
```

### Service Layer Example

```typescript
// src/lib/services/event-service.ts
import { EventRepository } from '@/lib/repositories/event-repository';
import { TicketTypeRepository } from '@/lib/repositories/ticket-type-repository';
import { Event, TicketType, CreateEventInput } from '@/lib/types';
import { createEventSchema } from '@/lib/validations/event-schema';

export class EventService {
  private eventRepository: EventRepository;
  private ticketTypeRepository: TicketTypeRepository;

  constructor() {
    this.eventRepository = new EventRepository();
    this.ticketTypeRepository = new TicketTypeRepository();
  }

  /**
   * Create a new event with ticket types
   */
  async createEvent(input: CreateEventInput): Promise<Event | null> {
    // Validate input
    const validated = createEventSchema.parse(input);

    // Extract ticket types from input
    const { ticketTypes, ...eventData } = validated;

    // Create the event
    const event = await this.eventRepository.create(eventData);
    if (!event) {
      return null;
    }

    // Create ticket types
    if (ticketTypes && ticketTypes.length > 0) {
      for (const ticketType of ticketTypes) {
        await this.ticketTypeRepository.create({
          ...ticketType,
          event_id: event.id,
        });
      }
    }

    return event;
  }

  /**
   * Get event dashboard data for the current user
   */
  async getEventDashboardData(): Promise<{
    draftEvents: Event[];
    publishedEvents: Event[];
    pastEvents: Event[];
  }> {
    const allEvents = await this.eventRepository.getByCurrentUser();
    const now = new Date();

    return {
      draftEvents: allEvents.filter(e => e.status === 'draft'),
      publishedEvents: allEvents.filter(
        e => e.status === 'published' && new Date(e.end_date) >= now
      ),
      pastEvents: allEvents.filter(
        e => e.status === 'published' && new Date(e.end_date) < now
      ),
    };
  }

  /**
   * Publish an event
   */
  async publishEvent(eventId: string): Promise<Event | null> {
    return this.eventRepository.update(eventId, { status: 'published' });
  }
}
```

### Authorization Middleware

```typescript
// src/middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });

  // Get the path from the URL
  const path = req.nextUrl.pathname;

  // Check if the user is authenticated
  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/sign-in',
    '/sign-up',
    '/api/webhooks/supabase',
    '/events',
    '/about',
    '/contact',
    '/terms',
    '/privacy-policy',
  ];

  // Check if the current path is public
  const isPublicRoute = publicRoutes.some(route => {
    if (route.endsWith('*')) {
      return path.startsWith(route.slice(0, -1));
    }
    return path === route || path.startsWith(`${route}/`);
  });

  // If the user is not authenticated and the route is not public, redirect to sign-in
  if (!session && !isPublicRoute) {
    const signInUrl = new URL('/sign-in', req.url);
    signInUrl.searchParams.set('redirect_url', req.url);
    return NextResponse.redirect(signInUrl);
  }

  return res;
}

export const config = {
  matcher: ['/((?!.*\\..*|_next).*)', '/', '/(api|trpc)(.*)'],
};
```

## Row-Level Security (RLS) Policies

In addition to application-level authorization, implement Row-Level Security policies in Supabase:

```sql
-- Example RLS policies for the events table

-- Enable RLS
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- Policy: Users can read published events
CREATE POLICY "Users can read published events"
ON events
FOR SELECT
USING (status = 'published');

-- Policy: Users can CRUD their own events
CREATE POLICY "Users can CRUD their own events"
ON events
USING (user_id = auth.uid());

-- Policy: Organization members can read organization events
CREATE POLICY "Organization members can read organization events"
ON events
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM organization_members
    WHERE organization_members.organization_id = events.organization_id
    AND organization_members.user_id = auth.uid()
  )
);

-- Policy: Organization admins/editors can update organization events
CREATE POLICY "Organization admins/editors can update organization events"
ON events
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM organization_members
    WHERE organization_members.organization_id = events.organization_id
    AND organization_members.user_id = auth.uid()
    AND organization_members.role IN ('admin', 'editor')
  )
);

-- Policy: Only organization admins can delete organization events
CREATE POLICY "Only organization admins can delete organization events"
ON events
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM organization_members
    WHERE organization_members.organization_id = events.organization_id
    AND organization_members.user_id = auth.uid()
    AND organization_members.role = 'admin'
  )
);
```

## Error Handling

Implement consistent error handling for data access operations:

```typescript
// src/lib/utils/error-handler.ts
import { PostgrestError } from '@supabase/supabase-js';

export class DatabaseError extends Error {
  code: string;
  details: string | null;

  constructor(error: PostgrestError) {
    super(error.message);
    this.name = 'DatabaseError';
    this.code = error.code;
    this.details = error.details;
  }
}

export class AuthorizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export function handleDatabaseError(error: PostgrestError): never {
  console.error('Database error:', error);
  throw new DatabaseError(error);
}

export function handleAuthorizationError(resource: string, action: string): never {
  const message = `Unauthorized: Cannot ${action} ${resource}`;
  console.error(message);
  throw new AuthorizationError(message);
}
```

## Usage in Server Actions

```typescript
// src/app/actions/event-actions.ts
'use server';

import { auth } from '@clerk/nextjs';
import { EventService } from '@/lib/services/event-service';
import { CreateEventInput } from '@/lib/types';
import { createEventSchema } from '@/lib/validations/event-schema';
import { revalidatePath } from 'next/cache';

export async function createEvent(formData: FormData) {
  try {
    const session = await auth();
    if (!session?.userId) {
      return { error: 'Unauthorized' };
    }

    // Parse and validate form data
    const rawData = Object.fromEntries(formData.entries());
    const data = createEventSchema.parse(rawData);

    // Create event using service
    const eventService = new EventService();
    const event = await eventService.createEvent({
      ...data,
      user_id: session.userId,
    });

    if (!event) {
      return { error: 'Failed to create event' };
    }

    // Revalidate relevant paths
    revalidatePath('/dashboard/events');

    return { success: true, eventId: event.id };
  } catch (error) {
    console.error('Error creating event:', error);
    if (error.name === 'ZodError') {
      return { error: 'Invalid form data', details: error.errors };
    }
    return { error: 'Failed to create event' };
  }
}
```

## Conclusion

This data access layer implementation provides:

1. **Consistent Authorization**: Every data access operation includes proper authorization checks
2. **Type Safety**: TypeScript types ensure correct data structures
3. **Validation**: Zod schemas validate input data
4. **Error Handling**: Consistent error handling across the application
5. **Separation of Concerns**: Repository pattern separates data access from business logic
6. **Multiple Layers of Security**: Application-level checks and database-level RLS policies

By implementing this architecture, the Fuiyoo application ensures that data is accessed securely and consistently throughout the application.
