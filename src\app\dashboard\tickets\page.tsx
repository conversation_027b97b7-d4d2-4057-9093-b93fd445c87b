import React from 'react';
import { createClient } from '@/lib/supabase/pages-client';
import { redirectToSignIn } from '@/lib/redirect-actions';

export default async function TicketsPage() {
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session || !session.user) {
    await redirectToSignIn();
    // Return early to avoid TypeScript errors
    return <div>Redirecting to sign in...</div>;
  }

  const userId = session.user.id;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-2">Tickets</h1>
      <p className="text-gray-500 mb-6">View and manage your event tickets</p>

      <div className="mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Your Tickets</h2>
            <div className="flex space-x-2">
              <select className="border rounded-md px-3 py-1.5 text-sm">
                <option>All Tickets</option>
                <option>Upcoming</option>
                <option>Past</option>
              </select>
            </div>
          </div>

          {/* Tickets listing */}
          <div className="border rounded-lg overflow-hidden">
            <div className="p-8 text-center text-gray-500">
              <p>You don't have any tickets yet.</p>
              <p className="mt-2">Purchase tickets to events to see them here.</p>
              <button className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition">
                Browse Events
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Recently Viewed Events</h2>
          <p className="text-gray-500 mb-4">
            Events you've recently viewed but haven't purchased tickets for yet.
          </p>

          {/* Recently viewed events */}
          <div className="border rounded-lg p-8 text-center text-gray-500">
            <p>No recently viewed events.</p>
            <p className="mt-2">Events you view will appear here for easy access.</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Ticket Actions</h2>
          <div className="space-y-3">
            <button className="w-full text-left px-4 py-2 border rounded-md hover:bg-gray-50">
              Transfer Ticket
            </button>
            <button className="w-full text-left px-4 py-2 border rounded-md hover:bg-gray-50">
              Request Refund
            </button>
            <button className="w-full text-left px-4 py-2 border rounded-md hover:bg-gray-50">
              View Past Tickets
            </button>
            <button className="w-full text-left px-4 py-2 border rounded-md hover:bg-gray-50">
              Add to Calendar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}