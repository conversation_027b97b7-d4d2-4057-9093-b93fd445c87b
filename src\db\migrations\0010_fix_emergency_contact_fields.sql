-- Fix emergency contact fields in saved_contacts table
-- This migration refreshes the schema cache for the saved_contacts table
-- by altering column types to force Supabase/PostgREST to update its metadata

-- 1. Start a transaction to ensure all operations complete or none do
BEGIN;

-- 2. Add comments to explain each field's purpose to improve documentation
COMMENT ON COLUMN saved_contacts.emergency_contact_name IS 'Emergency contact name for this contact';
COMMENT ON COLUMN saved_contacts.emergency_contact_no IS 'Emergency contact phone number for this contact';
COMMENT ON COLUMN saved_contacts.emergency_contact_relationship IS 'Relationship between this contact and their emergency contact';

-- 3. Alter emergency contact columns to force schema cache refresh
-- Even though these columns are already TEXT, altering them forces the schema cache to update
ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_name TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_no TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_relationship TYPE TEXT;

-- 4. Alter other columns that are having issues to ensure complete refresh
ALTER TABLE saved_contacts ALTER COLUMN first_name TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN last_name TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN relationship TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN email TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN phone TYPE TEXT;

-- 5. Add explicit NOT NULL constraints where appropriate to ensure schema clarity
ALTER TABLE saved_contacts ALTER COLUMN user_id SET NOT NULL;
ALTER TABLE saved_contacts ALTER COLUMN first_name SET NOT NULL;
ALTER TABLE saved_contacts ALTER COLUMN relationship SET NOT NULL;
ALTER TABLE saved_contacts ALTER COLUMN created_at SET NOT NULL;
ALTER TABLE saved_contacts ALTER COLUMN updated_at SET NOT NULL;

-- 6. Add an index for emergency contact lookup for better performance
CREATE INDEX IF NOT EXISTS saved_contacts_emergency_idx ON saved_contacts(emergency_contact_name);

-- 7. Force schema cache reload
-- In Supabase, this notifies the PostgREST service to reload its schema cache
-- This may require admin privileges depending on your Supabase configuration
SELECT pg_notify('pgrst', 'reload schema');

-- 8. Log the migration in the application's migrations table for tracking
INSERT INTO schema_migrations (version, applied_at)
VALUES ('0010_fix_emergency_contact_fields', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- 9. Commit the transaction
COMMIT; 