# Fuiyoo Project Architecture

This document provides an overview of the Fuiyoo project architecture, including the folder structure, key components, and architectural patterns.

## Technology Stack

- **Frontend Framework**: Next.js 15.3.0 with App Router
- **UI Library**: React 19.1.0
- **Styling**: Tailwind CSS 4.1.4
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **State Management**: Zustand
- **Form Handling**: React Hook Form with Zod validation
- **Deployment**: Netlify

## Project Structure

```
fuiyoo/
├── .next/                  # Next.js build output
├── .vscode/                # VS Code configuration
├── docs/                   # Project documentation
├── public/                 # Static assets
├── src/                    # Source code
│   ├── app/                # Next.js App Router
│   │   ├── actions/        # Server actions
│   │   ├── api/            # API routes
│   │   ├── (auth)/         # Authentication routes
│   │   ├── dashboard/      # Dashboard routes
│   │   ├── events/         # Event routes
│   │   ├── layout.tsx      # Root layout
│   │   └── page.tsx        # Home page
│   ├── components/         # React components
│   │   ├── layout/         # Layout components
│   │   ├── ui/             # UI components
│   │   └── forms/          # Form components
│   ├── db/                 # Database migrations
│   │   └── migrations/     # SQL migration files
│   ├── lib/                # Shared libraries
│   │   ├── repositories/   # Data access layer
│   │   ├── services/       # Business logic
│   │   ├── supabase/       # Supabase client
│   │   ├── utils/          # Utility functions
│   │   └── validations/    # Zod schemas
│   ├── middleware.ts       # Next.js middleware
│   └── types/              # TypeScript type definitions
├── .env.example            # Example environment variables
├── .eslintrc.js            # ESLint configuration
├── .gitignore              # Git ignore file
├── next.config.js          # Next.js configuration
├── netlify.toml            # Netlify configuration
├── package.json            # Package manifest
├── pnpm-lock.yaml          # pnpm lock file
├── postcss.config.js       # PostCSS configuration
├── react-compiler.config.js # React Compiler configuration
├── setup.sh                # Development setup script
├── tailwind.config.js      # Tailwind CSS configuration
└── tsconfig.json           # TypeScript configuration
```

## Architectural Patterns

### 1. Repository Pattern

The repository pattern is used for data access, providing a clean separation between the data layer and business logic.

```typescript
// src/lib/repositories/base-repository.ts
export class BaseRepository<T extends { id: string }> {
  protected tableName: string;

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  async getById(id: string): Promise<T | null> {
    // Implementation
  }

  // Other methods
}

// src/lib/repositories/event-repository.ts
export class EventRepository extends BaseRepository<Event> {
  constructor() {
    super('events');
  }

  // Entity-specific methods
}
```

### 2. Service Layer

The service layer encapsulates business logic and uses repositories for data access.

```typescript
// src/lib/services/event-service.ts
export class EventService {
  private eventRepository: EventRepository;

  constructor() {
    this.eventRepository = new EventRepository();
  }

  async createEvent(input: CreateEventInput): Promise<Event | null> {
    // Business logic
  }

  // Other methods
}
```

### 3. Server Actions

Server actions are used for server-side operations, leveraging the service layer.

```typescript
// src/app/actions/event-actions.ts
'use server';

export async function createEvent(formData: FormData) {
  // Implementation using EventService
}
```

### 4. React Server Components

The App Router pattern is used with React Server Components for optimal performance.

```typescript
// src/app/events/page.tsx
export default async function EventsPage() {
  // Server-side data fetching
  const events = await getEvents();

  return (
    <div>
      <h1>Events</h1>
      <EventList events={events} />
    </div>
  );
}
```

### 5. Client Components

Client components are used for interactive UI elements.

```typescript
// src/components/forms/EventForm.tsx
'use client';

export default function EventForm() {
  // Client-side state and interactivity
}
```

## Authentication and Authorization

### Authentication with Supabase Auth

Supabase Auth is used for authentication, providing a secure and integrated authentication system that works seamlessly with our database.

```typescript
// src/middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });

  // Check if the user is authenticated
  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Define public routes
  const publicRoutes = [
    '/',
    '/sign-in*',
    '/sign-up*',
    '/api/webhooks/supabase',
    '/events',
    '/events/:id',
  ];

  // Handle authentication and redirects
  // ...

  return res;
}
```

### Authorization

Authorization is implemented at multiple levels:

1. **Middleware**: Protects routes based on authentication status
2. **Repository Layer**: Checks access permissions for data operations
3. **Row-Level Security**: Implemented in Supabase for database-level protection

## Data Flow

1. **User Interaction**: User interacts with a client component
2. **Form Submission**: Data is validated with Zod and submitted to a server action
3. **Server Action**: Calls the appropriate service method
4. **Service Layer**: Implements business logic and calls repository methods
5. **Repository Layer**: Performs data access operations with authorization checks
6. **Database**: Supabase executes SQL queries with row-level security

## State Management

### Server State

Server components fetch data directly from the database or external APIs.

### Client State

Client-side state is managed using:

1. **React Hooks**: For component-level state
2. **Zustand**: For global state management
3. **React Hook Form**: For form state

## Performance Optimization

### Edge Compatibility

API routes and middleware are designed to be edge-compatible for optimal global performance.

```typescript
// src/app/api/edge-example/route.ts
export const runtime = 'edge';
```

### Static and Dynamic Rendering

Pages are rendered using the appropriate strategy:

1. **Static Rendering**: For content that doesn't change frequently
2. **Dynamic Rendering**: For personalized or frequently changing content

### Image Optimization

Next.js Image component is used for optimal image loading and rendering.

```typescript
import Image from 'next/image';

export default function EventImage({ src }) {
  return (
    <Image
      src={src}
      width={800}
      height={600}
      alt="Event"
      priority
    />
  );
}
```

## Error Handling

Errors are handled consistently throughout the application:

1. **Client-Side**: Toast notifications for user feedback
2. **Server-Side**: Structured error responses from server actions
3. **Repository Layer**: Error logging and consistent error objects

```typescript
// src/lib/utils/error-handler.ts
export class DatabaseError extends Error {
  code: string;
  details: string | null;

  constructor(error: PostgrestError) {
    super(error.message);
    this.name = 'DatabaseError';
    this.code = error.code;
    this.details = error.details;
  }
}
```

## Deployment

The application is deployed on Netlify with the following configuration:

1. **Build Command**: `pnpm build:netlify`
2. **Publish Directory**: `.next`
3. **Environment Variables**: Set in the Netlify dashboard
4. **Edge Functions**: Used for optimal global performance

See [Netlify Deployment Guide](./netlify-deployment.md) for more details.

## Development Workflow

1. **Setup**: Run `./setup.sh` to set up the development environment
2. **Development**: Run `pnpm dev` to start the development server
3. **Type Checking**: Run `pnpm type-check` to check for TypeScript errors
4. **Linting**: Run `pnpm lint` to check for ESLint errors
5. **Building**: Run `pnpm build` to build the application
6. **Validation**: Run `pnpm validate` to run type checking and linting

## Conclusion

The Fuiyoo project follows a clean architecture with clear separation of concerns, leveraging modern Next.js features and best practices for optimal performance, security, and developer experience.
