"use client";

import { useState, useEffect } from "react";
import { Sidebar } from "./sidebar";
import { NavItem } from "./nav-utils";

interface ClientSideDashboardProps {
  children: React.ReactNode;
  navItems: NavItem[];
}

export function ClientSideDashboard({
  children,
  navItems,
}: ClientSideDashboardProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Listen for sidebar toggle events from the global header
  useEffect(() => {
    // Check localStorage on mount to maintain state between navigation
    const savedState = localStorage.getItem('fuiyoo-sidebar-open');
    if (savedState) {
      setSidebarOpen(JSON.parse(savedState));
    }

    // Listen for toggle events
    const handleSidebarToggle = (event: CustomEvent<{ isOpen: boolean }>) => {
      setSidebarOpen(event.detail.isOpen);
    };

    window.addEventListener('sidebar-toggle', handleSidebarToggle as EventListener);

    return () => {
      window.removeEventListener('sidebar-toggle', handleSidebarToggle as EventListener);
    };
  }, []);

  return (
    <>
      <Sidebar
        isOpen={sidebarOpen}
        setIsOpen={setSidebarOpen}
        navItems={navItems}
      />
      <div className="flex flex-col flex-1 bg-[hsl(var(--background))] dark:bg-[hsl(var(--dark-background))] text-[hsl(var(--foreground))] h-screen overflow-hidden">
        {/* Uncomment header if needed for dashboard-specific controls */}
        {/* <DashboardHeader setIsOpen={setSidebarOpen} /> */}

        <main className="flex-1 px-2 md:px-4 relative z-10 w-full overflow-y-auto">
          {children}
        </main>
      </div>
    </>
  );
}