import { z } from "zod";

// Schema for privacy consent validation
export const ConsentSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  consentType: z.enum(['marketing', 'analytics', 'thirdpartysharing', 'datasharing', 'profiledisplay']),
  consentGiven: z.boolean(),
  consentVersion: z.string(),
  consentText: z.string(),
  expiresAt: z.string().datetime().optional().nullable(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
});

export type ConsentFormData = {
  consentType: z.infer<typeof ConsentSchema>['consentType'];
  consentGiven: boolean;
  consentVersion: string;
  consentText: string;
  expiresAt?: string | null;
};

export const ConsentVersionSchema = z.object({
  id: z.string().uuid(),
  version: z.string(),
  name: z.string(),
  description: z.string().optional(),
  consentText: z.string(),
  effectiveDate: z.string().datetime(),
  expiryDate: z.string().datetime().optional().nullable(),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
}); 