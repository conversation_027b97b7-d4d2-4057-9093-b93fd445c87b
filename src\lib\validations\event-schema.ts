import { z } from 'zod';
import { FieldType } from '@/types/event-types';

/**
 * Consolidated event validation schema
 * Single source of truth for event validation
 */

// Basic details validation
export const basicDetailsSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  location: z.string().min(3, 'Location must be at least 3 characters'),
  country: z.string().min(2, 'Please select a country'),
  state: z.string().min(1, 'Please select a state'),
  city: z.string().min(2, 'City is required').optional(),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  timezone: z.string().default('UTC'),
  totalCapacity: z.union([
    z.string().transform(val => val === '' ? undefined : parseInt(val, 10)),
    z.number().int().positive('Capacity must be a positive number'),
    z.undefined()
  ]).optional(),
  registrationCloseDate: z.string().optional(),
  allowCategorySpecificClosingDates: z.boolean().optional().default(false),
});

// Category validation
export const categorySchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Category name is required'),
  description: z.string().optional(),
  price: z.number().nonnegative().optional(),
  startTime: z.string().optional(),
  earlyBirdPrice: z.number().nonnegative().optional(),
  earlyBirdEndDate: z.string().optional(),
  bibPrefix: z.string().optional(),
  // Allow string or number for bibStartNumber to preserve leading zeros
  bibStartNumber: z.union([
    z.string().optional(),
    z.number().int().nonnegative().optional()
  ]),
  bibRequireGeneration: z.boolean().default(true),
  registrationOpen: z.boolean().default(true),
  registrationCloseDate: z.string().optional(),
  registrationLimit: z.number().int().positive().optional(),
  customFields: z.array(z.any()).optional(),
  // New JSON-based properties field
  properties: z.record(z.any()).optional(),
});

// Custom field validation
export const fieldSchema = z.object({
  id: z.string().min(1),
  type: z.nativeEnum(FieldType),
  label: z.string().min(1, 'Label is required'),
  description: z.string().optional(),
  required: z.boolean().optional(),
  placeholder: z.string().optional(),
  options: z.array(z.string().or(z.object({ label: z.string(), value: z.string() }))).optional(),
  minLength: z.number().optional(),
  maxLength: z.number().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  step: z.number().optional(),
  content: z.string().optional(),
  defaultValue: z.any().optional(),
  order: z.number().optional(),
});

// Image validation
export const imageSchema = z.object({
  url: z.string().url('Invalid image URL'),
  path: z.string(),
}).nullable();

// Emergency contact settings validation
export const emergencyContactSchema = z.object({
  required: z.boolean().default(false),
  fields: z.array(z.string()).default(['name', 'phone', 'relationship']),
  allowSameForMultipleRegistrations: z.boolean().default(true),
});

// T-shirt options validation
export const tshirtOptionsSchema = z.object({
  enabled: z.boolean().nullable().default(false),
  sizes: z.array(z.string()).nullable().default(["XS", "S", "M", "L", "XL", "XXL", "XXXL"]),
  description: z.string().nullable().optional(),
  sizeChartImage: z.any().nullable().optional(),
});

// Complete event schema
export const createEventSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().nullable().optional(),
  eventType: z.string(),
  eventTypeId: z.string().optional(),
  location: z.string().nullable().optional(),
  country: z.string().nullable().optional(),
  state: z.string().nullable().optional(),
  city: z.string().nullable().optional(),
  startDate: z.string().nullable().optional(),
  endDate: z.string().nullable().optional(),
  timezone: z.string().nullable().optional(),
  status: z.enum(["draft", "published", "cancelled", "completed"]).default("draft"),
  // New fields
  totalCapacity: z.union([
    z.string().transform(val => val === '' ? undefined : parseInt(val, 10)),
    z.number().int().positive('Capacity must be a positive number'),
    z.undefined(),
    z.null()
  ]).optional(),
  registrationCloseDate: z.string().nullable().optional(),
  allowCategorySpecificClosingDates: z.boolean().nullable().optional().default(false),
  tshirtOptions: tshirtOptionsSchema.nullable().optional(),
  // Existing fields
  categories: z.array(categorySchema).nullable().optional(),
  customFields: z.array(fieldSchema).nullable().optional(),
  emergencyContactSettings: emergencyContactSchema.nullable().optional(),
  posterImage: imageSchema.nullable().optional(),
  coverImage: imageSchema.nullable().optional(),
  galleryImages: z.array(imageSchema).nullable().optional(),
});

// Type for the event form data
export type EventFormData = z.infer<typeof createEventSchema>;
