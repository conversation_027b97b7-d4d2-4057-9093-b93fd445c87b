# Fixing Profile Activity Tracking

This document explains how to fix the profile activity tracking system in the Fuiyoo application.

## The Problem

The profile activity tracking system is not working correctly because:

1. There's a mismatch between the database schema and the code
2. The automatic trigger for logging profile changes is missing
3. The columns expected by the code don't exist in the database
4. There's a mismatch between the user IDs used in the code and the database

## Solution

### Option 1: Using Supabase MCP Tool (Recommended)

1. Open the Supabase dashboard for your project
2. Navigate to the SQL Editor
3. Create a new query
4. Copy and paste the contents of the `fix-profile-activity.sql` file
5. Run the query

```sql
-- Fix profile activity tracking
-- This migration aligns the profile_activity table with the code expectations
-- and sets up automatic activity logging via triggers

-- Step 1: Add missing columns to profile_activity table
ALTER TABLE profile_activity
  ADD COLUMN IF NOT EXISTS activity_type TEXT,
  ADD COLUMN IF NOT EXISTS activity_description TEXT,
  ADD COLUMN IF NOT EXISTS previous_value JSONB,
  ADD COLUMN IF NOT EXISTS new_value JSONB,
  ADD COLUMN IF NOT EXISTS field_name TEXT;

-- Step 2: Make required columns nullable to work with the new schema
ALTER TABLE profile_activity
  ALTER COLUMN profile_id DROP NOT NULL,
  ALTER COLUMN version DROP NOT NULL,
  ALTER COLUMN change_type DROP NOT NULL;

-- Step 3: Update existing columns to match expected structure
-- Map existing columns to new structure where possible
UPDATE profile_activity
SET
  activity_type = change_type,
  activity_description = 'Profile information updated',
  previous_value = changed_fields,
  new_value = changed_fields
WHERE activity_type IS NULL;

-- Step 3: Create helper function to record profile changes
CREATE OR REPLACE FUNCTION log_profile_change()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO profile_activity (
    user_id,
    activity_type,
    activity_description,
    previous_value,
    new_value
  ) VALUES (
    NEW.id,
    'profile_update',
    'Profile information updated',
    row_to_json(OLD)::jsonb,
    row_to_json(NEW)::jsonb
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create trigger on users table to record changes
DROP TRIGGER IF EXISTS profile_update_trigger ON users;
CREATE TRIGGER profile_update_trigger
AFTER UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION log_profile_change();

-- Step 5: Add indices for faster lookups if they don't exist
CREATE INDEX IF NOT EXISTS profile_activity_activity_type_idx ON profile_activity(activity_type);
CREATE INDEX IF NOT EXISTS profile_activity_created_at_idx ON profile_activity(created_at);
```

### Option 2: Using the Admin Migration Page

1. Deploy the application with the included migration files
2. Navigate to `/dashboard/admin/migrations` in your application
3. Click the "Apply Migration" button
4. The system will apply the migration and show a success message

### Option 3: Using the Migration File Directly

1. Run the migration file using the Supabase CLI:

```bash
supabase db execute -f src/db/migrations/0018_fix_profile_activity.sql
```

## Testing the Fix

After applying the fix, you can test if it's working by:

1. Going to your profile page at `/dashboard/profile`
2. Clicking on the "Activity" tab
3. Clicking the "Create Test Activity" button
4. You should see a new activity appear in the list

Additionally, any changes you make to your profile should now be automatically logged and visible in the activity tab.

## Troubleshooting

If you're still not seeing activities after applying the fix:

1. Check the browser console for any errors
2. Verify that the `profile_activity` table has the correct columns
3. Check if the `profile_update_trigger` trigger exists on the `users` table
4. Try manually updating your profile to trigger the logging function
