#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up privacy tables in Supabase
 * 
 * Usage:
 * node scripts/setup-privacy-tables.js
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Missing Supabase URL or service role key');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupPrivacyTables() {
  console.log('Setting up privacy tables in Supabase...');

  try {
    // Create tables
    const createTablesSQL = `
      -- Create extension if it doesn't exist
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
      
      -- Create privacy_consent_versions table
      CREATE TABLE IF NOT EXISTS privacy_consent_versions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        version VARCHAR(50) NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        consent_text TEXT NOT NULL,
        effective_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        expiry_date TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      -- Create privacy_consents table with simplified structure
      CREATE TABLE IF NOT EXISTS privacy_consents (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id TEXT NOT NULL,
        consent_type VARCHAR(50) NOT NULL,
        consent_given BOOLEAN NOT NULL DEFAULT false,
        consent_version VARCHAR(50) NOT NULL,
        consent_text TEXT NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE,
        ip_address VARCHAR(45),
        user_agent TEXT,
        previous_record_id UUID,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
      
      -- Create index for faster lookups
      CREATE INDEX IF NOT EXISTS idx_privacy_consents_user_id ON privacy_consents(user_id);
      CREATE INDEX IF NOT EXISTS idx_privacy_consents_type ON privacy_consents(consent_type);
    `;

    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTablesSQL });
    
    if (createError) {
      throw new Error(`Error creating tables: ${createError.message}`);
    }
    
    console.log('✅ Tables created successfully');

    // Insert initial consent versions
    const insertVersionsSQL = `
      -- Insert initial consent versions if they don't exist
      INSERT INTO privacy_consent_versions (version, name, description, consent_text)
      SELECT '1.0', 'Marketing', 'Receive marketing communications and updates', 'I agree to receive marketing communications, newsletters, and updates about events and services.'
      WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Marketing');
      
      INSERT INTO privacy_consent_versions (version, name, description, consent_text)
      SELECT '1.0', 'Analytics', 'Allow usage data collection for analytics', 'I agree to the collection and processing of my usage data for analytics and service improvement purposes.'
      WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Analytics');
      
      INSERT INTO privacy_consent_versions (version, name, description, consent_text)
      SELECT '1.0', 'Third Party', 'Share data with trusted third parties', 'I agree to share my data with trusted third-party partners for service delivery and improvement.'
      WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Third Party');
      
      INSERT INTO privacy_consent_versions (version, name, description, consent_text)
      SELECT '1.0', 'Data Sharing', 'Share profile data with event organizers', 'I agree to share my profile information with event organizers for event registration and management.'
      WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Data Sharing');
      
      INSERT INTO privacy_consent_versions (version, name, description, consent_text)
      SELECT '1.0', 'Profile Display', 'Display profile in public listings', 'I agree to display my profile information in public event attendee listings.'
      WHERE NOT EXISTS (SELECT 1 FROM privacy_consent_versions WHERE name = 'Profile Display');
    `;

    const { error: insertError } = await supabase.rpc('exec_sql', { sql: insertVersionsSQL });
    
    if (insertError) {
      throw new Error(`Error inserting consent versions: ${insertError.message}`);
    }
    
    console.log('✅ Consent versions inserted successfully');

    // Set up RLS policies
    const setupRLSSQL = `
      -- Enable RLS on privacy tables
      ALTER TABLE privacy_consents ENABLE ROW LEVEL SECURITY;
      ALTER TABLE privacy_consent_versions ENABLE ROW LEVEL SECURITY;
      
      -- Drop existing policies if any
      DROP POLICY IF EXISTS "Users can view their own consents" ON privacy_consents;
      DROP POLICY IF EXISTS "Users can create their own consents" ON privacy_consents;
      DROP POLICY IF EXISTS "Users can update their own consents" ON privacy_consents;
      DROP POLICY IF EXISTS "Users can view consent versions" ON privacy_consent_versions;
      
      -- Create new policies with proper casting
      CREATE POLICY "Users can view their own consents"
        ON privacy_consents
        FOR SELECT
        TO authenticated
        USING (auth.uid()::text = user_id);
      
      CREATE POLICY "Users can create their own consents"
        ON privacy_consents
        FOR INSERT
        TO authenticated
        WITH CHECK (auth.uid()::text = user_id);
      
      CREATE POLICY "Users can update their own consents"
        ON privacy_consents
        FOR UPDATE
        TO authenticated
        USING (auth.uid()::text = user_id);
      
      CREATE POLICY "Users can view consent versions"
        ON privacy_consent_versions
        FOR SELECT
        TO authenticated
        USING (true);
      
      -- Grant permissions to authenticated users
      GRANT SELECT ON privacy_consents TO authenticated;
      GRANT INSERT ON privacy_consents TO authenticated;
      GRANT UPDATE ON privacy_consents TO authenticated;
      GRANT SELECT ON privacy_consent_versions TO authenticated;
    `;

    const { error: rlsError } = await supabase.rpc('exec_sql', { sql: setupRLSSQL });
    
    if (rlsError) {
      throw new Error(`Error setting up RLS policies: ${rlsError.message}`);
    }
    
    console.log('✅ RLS policies created successfully');

    // Verify tables exist
    const { data: tables, error: verifyError } = await supabase.rpc('exec_sql', {
      sql: `SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('privacy_consent_versions', 'privacy_consents')`
    });
    
    if (verifyError) {
      throw new Error(`Error verifying tables: ${verifyError.message}`);
    }
    
    console.log(`✅ Verification complete: Found ${tables?.length || 0} tables`);
    
    // Verify consent versions
    const { data: versions, error: versionsError } = await supabase.rpc('exec_sql', {
      sql: `SELECT * FROM privacy_consent_versions`
    });
    
    if (versionsError) {
      throw new Error(`Error verifying consent versions: ${versionsError.message}`);
    }
    
    console.log(`✅ Found ${versions?.length || 0} consent versions`);
    
    console.log('✅ Privacy tables setup complete!');
    console.log('Restart your development server to see the changes.');
  } catch (error) {
    console.error('❌ Error setting up privacy tables:', error.message);
    process.exit(1);
  }
}

setupPrivacyTables(); 