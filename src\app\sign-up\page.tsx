import { Metadata } from 'next'
import SignUpForm from '@/components/auth/SignUpForm'
import { Suspense } from 'react'

export const metadata: Metadata = {
  title: 'Sign Up | Fuiyoo',
  description: 'Create a new Fuiyoo account',
}

// Make the page static to avoid dynamic server usage errors
export const dynamic = 'force-static'

export default function SignUpPage() {
  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12 bg-background">
      <div className="w-full max-w-md">
        <div className="bg-card text-card-foreground shadow-lg rounded-lg p-8 border border-border">
          <Suspense fallback={<div>Loading...</div>}>
            <SignUpForm />
          </Suspense>
        </div>
      </div>
    </div>
  )
}
