/**
 * Simple logger utility for application logging
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Type for log arguments - can be primitives, objects, or errors
type LogArgument = string | number | boolean | null | undefined | Error | Record<string, unknown> | unknown[];

class Logger {
  private prefix: string;

  constructor(prefix: string = '') {
    this.prefix = prefix ? `[${prefix}] ` : '';
  }

  debug(message: string, ...args: LogArgument[]): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`${this.prefix}${message}`, ...args);
    }
  }

  info(message: string, ...args: LogArgument[]): void {
    console.info(`${this.prefix}${message}`, ...args);
  }

  warn(message: string, ...args: LogArgument[]): void {
    console.warn(`${this.prefix}${message}`, ...args);
  }

  error(message: string, ...args: LogArgument[]): void {
    console.error(`${this.prefix}${message}`, ...args);
  }
}

export const logger = new Logger('App');