# Next.js App Router Documentation

This document contains key information about using the Next.js App Router in our application.

## File-Based Routing

The App Router uses a file-based routing system where:

- Folders define routes
- Files define UI
- Special files like `page.tsx`, `layout.tsx`, `loading.tsx`, etc. have specific roles

### Key Files

- `page.tsx`: Defines a route segment that is publicly accessible
- `layout.tsx`: Defines shared UI for a segment and its children
- `loading.tsx`: Creates a loading UI for a segment
- `error.tsx`: Creates an error UI for a segment
- `not-found.tsx`: Creates a UI for 404 errors

## Server Components

By default, all components in the App Router are React Server Components (RSC).

```tsx
// This is a Server Component by default
export default function Page() {
  return <h1>Hello, Server Component!</h1>
}
```

### Client Components

To make a component a Client Component, add the `'use client'` directive at the top of the file.

```tsx
'use client'

// This is a Client Component
export default function Counter() {
  const [count, setCount] = useState(0)
  
  return (
    <button onClick={() => setCount(count + 1)}>
      Count: {count}
    </button>
  )
}
```

## Data Fetching

### Server Components

In Server Components, you can fetch data directly:

```tsx
export default async function Page() {
  // This request is made on the server
  const data = await fetch('https://api.example.com/data')
  const json = await data.json()
  
  return <div>{json.message}</div>
}
```

### Route Handlers

Create API endpoints using Route Handlers:

```tsx
// app/api/hello/route.ts
export async function GET() {
  return Response.json({ message: 'Hello World' })
}

export async function POST(request: Request) {
  const body = await request.json()
  return Response.json({ received: body })
}
```

## Server Actions

Server Actions allow you to run server-side code from client components:

```tsx
// app/actions.ts
'use server'

export async function submitForm(formData: FormData) {
  const name = formData.get('name')
  // Process the form data on the server
  return { success: true }
}
```

Then use it in a client component:

```tsx
'use client'

import { submitForm } from './actions'

export default function Form() {
  return (
    <form action={submitForm}>
      <input name="name" />
      <button type="submit">Submit</button>
    </form>
  )
}
```

## Metadata

Define metadata for SEO:

```tsx
// app/page.tsx
export const metadata = {
  title: 'Home Page',
  description: 'Welcome to our website',
}

export default function Page() {
  return <h1>Welcome</h1>
}
```

Or generate it dynamically:

```tsx
// app/blog/[slug]/page.tsx
export async function generateMetadata({ params }) {
  const post = await getPost(params.slug)
  
  return {
    title: post.title,
    description: post.excerpt,
  }
}
```

## Dynamic Routes

Create dynamic routes using folder names with brackets:

```
app/blog/[slug]/page.tsx
```

Access the dynamic parameter:

```tsx
export default function Post({ params }: { params: { slug: string } }) {
  return <h1>Post: {params.slug}</h1>
}
```

## Loading UI

Create loading states with `loading.tsx`:

```tsx
// app/dashboard/loading.tsx
export default function Loading() {
  return <div>Loading dashboard...</div>
}
```

## Error Handling

Handle errors with `error.tsx`:

```tsx
'use client'

export default function Error({
  error,
  reset,
}: {
  error: Error
  reset: () => void
}) {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={() => reset()}>Try again</button>
    </div>
  )
}
```

## Not Found Pages

Create custom 404 pages with `not-found.tsx`:

```tsx
// app/not-found.tsx
export default function NotFound() {
  return (
    <div>
      <h2>Not Found</h2>
      <p>Could not find requested resource</p>
    </div>
  )
}
```

## Parallel Routes

Create parallel routes using the `@folder` convention:

```
app/dashboard/@stats/page.tsx
app/dashboard/@team/page.tsx
app/dashboard/layout.tsx
```

```tsx
// app/dashboard/layout.tsx
export default function DashboardLayout({
  children,
  stats,
  team,
}: {
  children: React.ReactNode
  stats: React.ReactNode
  team: React.ReactNode
}) {
  return (
    <div>
      {children}
      <div className="grid grid-cols-2">
        {stats}
        {team}
      </div>
    </div>
  )
}
```

## Intercepting Routes

Create modal-like experiences with intercepting routes using the `(.)folder` or `(..)folder` convention:

```
app/posts/page.tsx
app/posts/[id]/page.tsx
app/posts/@modal/(.)new/page.tsx
```

## References

- [Next.js App Router Documentation](https://nextjs.org/docs/app)
- [Routing Fundamentals](https://nextjs.org/docs/app/building-your-application/routing)
- [Data Fetching](https://nextjs.org/docs/app/building-your-application/data-fetching)
- [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions)
- [Metadata API](https://nextjs.org/docs/app/building-your-application/optimizing/metadata)
