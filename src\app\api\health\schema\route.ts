import { NextResponse } from 'next/server';
import { SchemaManager } from '@/lib/db/schema-manager';
import { createClient } from '@/lib/supabase/pages-client';

/**
 * GET /api/health/schema
 *
 * Schema health check endpoint that validates:
 * - Database connection
 * - Schema consistency
 * - Migration status
 *
 * This endpoint can be used by monitoring systems to detect schema cache issues
 */
export async function GET(request: Request) {
  // Check if this is an admin request
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();
  const userId = session?.user?.id;
  const isDetailedCheck = new URL(request.url).searchParams.has('detailed');

  // Basic health check is public, detailed checks require authentication
  if (isDetailedCheck && !userId) {
    return NextResponse.json(
      { error: 'Unauthorized, authentication required for detailed checks' },
      { status: 401 }
    );
  }

  try {
    // Run schema health check
    const healthCheck = await SchemaManager.checkSchemaHealth();

    // Return appropriate response based on health status
    if (!healthCheck.success) {
      // For public requests, just return a simple status
      if (!userId && !isDetailedCheck) {
        return NextResponse.json(
          { status: 'error', message: 'Schema health check failed' },
          { status: 500 }
        );
      }

      // For admin/detailed requests, return full error details
      return NextResponse.json(healthCheck, { status: 500 });
    }

    // For public requests, return a simple healthy status
    if (!userId && !isDetailedCheck) {
      return NextResponse.json({
        status: 'healthy',
        checkedAt: healthCheck.checkedAt
      });
    }

    // Return full health details for admin/detailed requests
    return NextResponse.json(healthCheck);
  } catch (error) {
    console.error('Error in schema health check:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error during health check',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/health/schema/refresh
 *
 * Admin-only endpoint to force schema cache refresh
 */
export async function POST(request: Request) {
  // Check if this is an admin request
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();
  const userId = session?.user?.id;

  if (!userId) {
    return NextResponse.json(
      { error: 'Unauthorized, authentication required' },
      { status: 401 }
    );
  }

  try {
    // Force schema cache refresh
    const result = await SchemaManager.refreshSchemaCache();

    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error refreshing schema cache:', error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error refreshing schema cache',
      },
      { status: 500 }
    );
  }
}