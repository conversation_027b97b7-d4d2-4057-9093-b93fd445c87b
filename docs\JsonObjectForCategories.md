# JSON-Based Event Category Structure

## Overview

This document outlines a flexible approach to storing event categories using JSON objects. Different event types (running events, conferences, exhibitions, etc.) require different category structures. Rather than creating a rigid database schema that tries to accommodate all possible event types, we propose using JSON objects to store category-specific properties.

## Current Limitations

The current approach with a fixed schema in the `event_categories` table has several limitations:

1. **Rigid Structure**: All event categories must conform to the same schema, regardless of event type
2. **Schema Evolution Challenges**: Adding new fields requires database migrations
3. **Unused Fields**: Many fields may be irrelevant for certain event types (e.g., bib numbers for conferences)
4. **Limited Customization**: Difficult to support event-specific category attributes

## Implemented Solution

### Database Schema

We have implemented a pure JSON-based approach with the `event_categories` table using a JSONB column for storing all category-specific properties:

```sql
CREATE TABLE event_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID NOT NULL REFERENCES events(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  -- JSON-based properties field
  properties JSONB NOT NULL DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

This approach:
- Maintains referential integrity with the `events` table
- Keeps core fields (name, description) queryable
- Provides flexibility for different event types through the `properties` JSONB field
- Allows for efficient indexing and querying of common properties

### TypeScript Type System

We've defined base types with event-specific extensions:

```typescript
// Base category type
interface BaseEventCategory {
  id: string;
  eventId: string;
  name: string;
  description?: string;
  properties: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Running event category properties
interface RunningEventCategoryProperties {
  distance?: number;
  price?: number;
  startTime?: string;
  earlyBirdPrice?: number;
  earlyBirdEndDate?: string;
  bibPrefix?: string;
  bibStartNumber?: string;
  bibRequireGeneration?: boolean;
  registrationOpen?: boolean;
  registrationCloseDate?: string;
  registrationLimit?: number;
  registrationCount?: number;
}

// Conference event category properties
interface ConferenceEventCategoryProperties {
  venue?: string;
  speaker?: string;
  sessionDuration?: number;
  price?: number;
  startTime?: string;
  endTime?: string;
  registrationLimit?: number;
  registrationCount?: number;
}

// Running event category
interface RunningEventCategory extends BaseEventCategory {
  properties: RunningEventCategoryProperties;
}

// Conference event category
interface ConferenceEventCategory extends BaseEventCategory {
  properties: ConferenceEventCategoryProperties;
}

// Generic event category (backward compatibility)
interface EventCategory extends BaseEventCategory {
  // Legacy fields for backward compatibility
  price?: number;
  startTime?: Date;
  earlyBirdPrice?: number;
  earlyBirdEndDate?: Date;
  bibPrefix?: string;
  bibStartNumber?: number | string;
  bibRequireGeneration: boolean;
  registrationOpen: boolean;
  registrationCloseDate?: Date;
  registrationLimit?: number;
  registrationCount: number;
  customFields: EventField[];
}
```

### Repository Layer Implementation

The repository layer will handle the conversion between application types and database records:

```typescript
class EventCategoryRepository {
  // Create a category with type-specific properties
  async createCategory(eventId: string, category: BaseEventCategory & { properties?: Record<string, any> }): Promise<EventCategory> {
    const { data, error } = await supabase
      .from('event_categories')
      .insert({
        event_id: eventId,
        name: category.name,
        description: category.description || null,
        properties: category.properties || {}
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to create category: ${error.message}`);
    return this.mapToEventCategory(data);
  }

  // Get categories for an event
  async getCategoriesByEventId(eventId: string): Promise<EventCategory[]> {
    const { data, error } = await supabase
      .from('event_categories')
      .select('*')
      .eq('event_id', eventId);

    if (error) throw new Error(`Failed to get categories: ${error.message}`);
    return data.map(this.mapToEventCategory);
  }

  // Update a category
  async updateCategory(id: string, category: Partial<BaseEventCategory> & { properties?: Record<string, any> }): Promise<EventCategory> {
    const updates: any = {};
    if (category.name !== undefined) updates.name = category.name;
    if (category.description !== undefined) updates.description = category.description;

    // If properties are provided, merge them with existing properties
    if (category.properties) {
      const { data: existing } = await supabase
        .from('event_categories')
        .select('properties')
        .eq('id', id)
        .single();

      if (existing) {
        updates.properties = { ...existing.properties, ...category.properties };
      } else {
        updates.properties = category.properties;
      }
    }

    updates.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from('event_categories')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw new Error(`Failed to update category: ${error.message}`);
    return this.mapToEventCategory(data);
  }

  // Helper to map DB record to EventCategory
  private mapToEventCategory(record: any): EventCategory {
    return {
      id: record.id,
      name: record.name,
      description: record.description,
      properties: record.properties || {},
    };
  }
}
```

### UI Implementation

The UI will dynamically render form fields based on the event type:

```tsx
// Component to render category form based on event type
function CategoryForm({ eventType, category, onChange }) {
  // Base fields for all categories
  const baseFields = (
    <>
      <TextField
        name="name"
        label="Category Name"
        value={category.name}
        onChange={(e) => onChange({ ...category, name: e.target.value })}
        required
      />
      <TextField
        name="description"
        label="Description"
        value={category.description}
        onChange={(e) => onChange({ ...category, description: e.target.value })}
        multiline
      />
    </>
  );

  // Event-specific fields
  const renderEventTypeFields = () => {
    const properties = category.properties || {};
    const updateProperty = (key, value) => {
      onChange({
        ...category,
        properties: {
          ...properties,
          [key]: value
        }
      });
    };

    switch(eventType) {
      case 'running':
        return (
          <>
            <TextField
              name="price"
              label="Price"
              value={properties.price || ''}
              onChange={(e) => updateProperty('price', parseFloat(e.target.value))}
              type="number"
            />
            <TextField
              name="capacity"
              label="Capacity"
              value={properties.capacity || ''}
              onChange={(e) => updateProperty('capacity', parseInt(e.target.value))}
              type="number"
            />
            <TextField
              name="distance"
              label="Distance (km)"
              value={properties.distance || ''}
              onChange={(e) => updateProperty('distance', parseFloat(e.target.value))}
              type="number"
            />
            <TextField
              name="bibPrefix"
              label="Bib Prefix"
              value={properties.bibPrefix || ''}
              onChange={(e) => updateProperty('bibPrefix', e.target.value)}
            />
            <TextField
              name="bibStartNumber"
              label="Starting Bib Number"
              value={properties.bibStartNumber || ''}
              onChange={(e) => updateProperty('bibStartNumber', e.target.value)}
            />
            <Switch
              name="bibRequireGeneration"
              label="Generate Bibs Automatically"
              checked={properties.bibRequireGeneration || false}
              onChange={(e) => updateProperty('bibRequireGeneration', e.target.checked)}
            />
          </>
        );
      case 'conference':
        return (
          <>
            <TextField
              name="price"
              label="Price"
              value={properties.price || ''}
              onChange={(e) => updateProperty('price', parseFloat(e.target.value))}
              type="number"
            />
            <TextField
              name="venue"
              label="Venue"
              value={properties.venue || ''}
              onChange={(e) => updateProperty('venue', e.target.value)}
            />
            <TextField
              name="speaker"
              label="Speaker"
              value={properties.speaker || ''}
              onChange={(e) => updateProperty('speaker', e.target.value)}
            />
            <TextField
              name="sessionDuration"
              label="Duration (minutes)"
              value={properties.sessionDuration || ''}
              onChange={(e) => updateProperty('sessionDuration', parseInt(e.target.value))}
              type="number"
            />
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div className="category-form">
      {baseFields}
      {renderEventTypeFields()}
    </div>
  );
}
```

## Event Type Templates

We'll define templates for different event types to pre-populate category structures:

```typescript
const EVENT_TYPE_TEMPLATES = {
  'running': {
    baseFields: {
      hasCategories: true,
      requiresLocation: true,
      requiresDate: true,
    },
    categoryTemplate: {
      properties: {
        distance: 0,
        bibPrefix: '',
        bibStartNumber: '1001',
        bibRequireGeneration: true,
        registrationOpen: true,
        registrationCount: 0,
      }
    }
  },
  'conference': {
    baseFields: {
      hasCategories: true,
      requiresLocation: true,
      requiresDate: true,
    },
    categoryTemplate: {
      properties: {
        venue: '',
        sessionDuration: 60,
        maxAttendees: 100,
        requiresRegistration: true,
      }
    }
  },
  // More event types...
};
```

## Migration Strategy

To migrate from the current structure to the new JSON-based approach:

1. **Add JSON Column**: Add the `properties` JSONB column to the existing `event_categories` table
2. **Data Migration**: Move existing specialized columns into the properties JSON object
3. **Gradual Transition**: Update application code to use the new structure while maintaining backward compatibility
4. **Schema Cleanup**: Once fully migrated, remove the specialized columns

```sql
-- Step 1: Add properties column
ALTER TABLE event_categories ADD COLUMN properties JSONB DEFAULT '{}'::jsonb;

-- Step 2: Migrate existing data
UPDATE event_categories
SET properties = jsonb_build_object(
  'price', price,
  'capacity', capacity,
  'bibPrefix', bib_prefix,
  'bibStartNumber', bib_start_number,
  'bibRequireGeneration', bib_require_generation,
  'registrationOpen', registration_open,
  'registrationLimit', registration_limit,
  'registrationCount', registration_count,
  'startTime', start_time,
  'earlyBirdPrice', early_bird_price,
  'earlyBirdEndDate', early_bird_end_date,
  'registrationCloseDate', registration_close_date
);

-- Step 3: After application is updated, remove old columns
-- (This would be done later after confirming everything works)
```

## Querying JSON Properties

Supabase and PostgreSQL provide powerful operators for querying JSON data:

```typescript
// Get all running categories with distance > 10km
const { data, error } = await supabase
  .from('event_categories')
  .select('*')
  .eq('event_id', eventId)
  .gte('properties->distance', 10);

// Get categories with specific bib prefix
const { data, error } = await supabase
  .from('event_categories')
  .select('*')
  .eq('event_id', eventId)
  .eq('properties->>bibPrefix', 'FM-');
```

In SQL:

```sql
-- Get all running categories with distance > 10km
SELECT * FROM event_categories
WHERE event_id = 'event-uuid'
AND (properties->>'distance')::numeric > 10;

-- Get categories with specific bib prefix
SELECT * FROM event_categories
WHERE event_id = 'event-uuid'
AND properties->>'bibPrefix' = 'FM-';
```

## Validation with Zod

We use Zod schemas for validation at the application level:

```typescript
// Base schema for all categories
export const baseEventCategorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  description: z.string().optional(),
  properties: z.record(z.any()).default({}),
});

// Running event properties schema
export const runningEventCategoryPropertiesSchema = z.object({
  distance: z.number().optional(),
  price: z.number().nonnegative().optional(),
  startTime: z.string().optional(),
  earlyBirdPrice: z.number().nonnegative().optional(),
  earlyBirdEndDate: z.string().optional(),
  bibPrefix: z.string().optional(),
  bibStartNumber: z.string().optional(),
  bibRequireGeneration: z.boolean().optional().default(true),
  registrationOpen: z.boolean().optional().default(true),
  registrationCloseDate: z.string().optional(),
  registrationLimit: z.number().int().positive().optional(),
  registrationCount: z.number().int().nonnegative().optional().default(0),
});

// Conference event properties schema
export const conferenceEventCategoryPropertiesSchema = z.object({
  venue: z.string().optional(),
  speaker: z.string().optional(),
  sessionDuration: z.number().int().positive().optional(),
  price: z.number().nonnegative().optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
  registrationLimit: z.number().int().positive().optional(),
  registrationCount: z.number().int().nonnegative().optional().default(0),
});

// Legacy event category schema
export const eventCategorySchema = baseEventCategorySchema.extend({
  // Legacy fields for backward compatibility
  price: z.number().nonnegative().optional(),
  startTime: z.date().optional(),
  earlyBirdPrice: z.number().nonnegative().optional(),
  earlyBirdEndDate: z.date().optional(),
  bibPrefix: z.string().optional(),
  bibStartNumber: z.union([
    z.string().optional(),
    z.number().int().nonnegative().optional()
  ]),
  bibRequireGeneration: z.boolean().optional().default(true),
  registrationOpen: z.boolean().optional().default(true),
  registrationCloseDate: z.date().optional(),
  registrationLimit: z.number().int().positive().optional(),
  customFields: z.array(z.any()).optional(),
});
```

## Benefits

1. **Flexibility**: Easily adapt to different event types without schema changes
2. **Future-Proof**: Add new category properties without migrations
3. **Type Safety**: Maintain TypeScript type checking with interfaces
4. **Performance**: Keep core fields queryable while allowing flexible properties
5. **Simplified Code**: Cleaner repository layer with less mapping code

## Considerations

1. **Query Performance**: Filtering on JSON properties is less efficient than on columns
2. **Indexing**: Consider creating indexes on frequently queried JSON properties
3. **Validation**: Implement proper validation for JSON properties at the application level
4. **Schema Documentation**: Maintain clear documentation of expected properties for each event type

## Implementation Plan

1. Create a migration to add the `properties` JSONB column to the `event_categories` table
2. Update the TypeScript types and repository layer to support the new structure
3. Modify UI components to handle dynamic category properties
4. Implement validation for the JSON properties
5. Migrate existing data to the new structure
6. Update the application code to use the new structure
7. Once everything is working, remove the old columns

## References

- [Supabase JSON Documentation](https://supabase.com/docs/guides/database/json)
- [PostgreSQL JSON Functions and Operators](https://www.postgresql.org/docs/current/functions-json.html)
- [pg_jsonschema Extension](https://supabase.com/docs/guides/database/extensions/pg_jsonschema)
