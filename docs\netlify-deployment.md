# Netlify Deployment Guide for Fuiyoo

This guide covers the deployment of the Fuiyoo application on Netlify, including configuration, environment setup, and best practices.

## Overview

Fuiyoo is deployed on Netlify using the Next.js plugin for optimal performance and edge compatibility. The deployment process is configured through the `netlify.toml` file and environment variables set in the Netlify dashboard.

## Deployment Configuration

### netlify.toml

The `netlify.toml` file in the project root contains all the necessary configuration for Netlify deployment:

```toml
[build]
  command = "pnpm build:netlify"
  publish = ".next"
  ignore = "git diff --quiet $CACHED_COMMIT_REF $COMMIT_REF ./src"

[build.environment]
  NETLIFY_NEXT_PLUGIN_SKIP = "false"
  NETLIFY_USE_PNPM = "true"
  NODE_VERSION = "20"
  NPM_FLAGS = "--version" # Prevent Netlify npm install
  NEXT_TELEMETRY_DISABLED = "1"
  NEXT_FORCE_EDGE_IMAGES = "true"
```

### Key Configuration Elements

1. **Build Command**: `pnpm build:netlify` - This runs the Next.js build process optimized for Netlify.
2. **Publish Directory**: `.next` - This is where Next.js outputs the built application.
3. **Environment Variables**: Configuration for Node.js version, pnpm usage, and Next.js settings.
4. **Headers**: Cache control and security headers for optimal performance and security.
5. **Functions**: Configuration for Netlify Functions, including included files and external modules.
6. **Plugins**: The Next.js plugin for Netlify that handles SSR and edge functions.

## Environment Variables

The following environment variables need to be set in the Netlify dashboard:

### Required Variables

- `NEXT_PUBLIC_SUPABASE_URL`: The URL of your Supabase project
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: The anonymous key for Supabase client access
- `SUPABASE_SERVICE_ROLE_KEY`: The service role key for admin Supabase operations
- `NEXT_PUBLIC_SITE_URL`: The public URL of your site for auth redirects
- `NEXT_PUBLIC_SIGN_IN_URL`: The URL for the sign-in page (e.g., `/sign-in`)
- `NEXT_PUBLIC_SIGN_UP_URL`: The URL for the sign-up page (e.g., `/sign-up`)
- `NEXT_PUBLIC_AFTER_SIGN_IN_URL`: The URL to redirect to after sign-in (e.g., `/dashboard`)
- `NEXT_PUBLIC_AFTER_SIGN_UP_URL`: The URL to redirect to after sign-up (e.g., `/onboarding`)

### Optional Variables

- `ANALYZE`: Set to `true` to enable bundle analysis during build
- `NEXT_TURBO`: Set to `1` to enable Turbopack (experimental)
- `NEXT_SKIP_PREFLIGHT_CHECK`: Set to `true` to skip preflight checks

## Deployment Process

### Automatic Deployments

Netlify is configured to automatically deploy:

1. When code is pushed to the `main` branch
2. When pull requests are created (deploy previews)

### Manual Deployments

To trigger a manual deployment:

1. Go to the Netlify dashboard
2. Select the Fuiyoo site
3. Click "Trigger deploy" and select "Deploy site"

## Performance Optimization

### Edge Functions

Fuiyoo uses Netlify Edge Functions for optimal global performance. API routes with the `export const runtime = 'edge'` directive will be deployed as Edge Functions.

Example:

```typescript
// src/app/api/edge-example/route.ts
import { NextResponse } from 'next/server';

export const runtime = 'edge';

export async function GET() {
  return NextResponse.json({ message: 'This is an Edge Function' });
}
```

### Caching Strategy

The `netlify.toml` file includes cache headers for static assets:

```toml
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/_next/image/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

### Image Optimization

Next.js Image component is configured to use Netlify's image optimization:

```toml
[build.environment]
  NEXT_FORCE_EDGE_IMAGES = "true"
```

## Monitoring and Debugging

### Build Logs

To view build logs:

1. Go to the Netlify dashboard
2. Select the Fuiyoo site
3. Click "Deploys"
4. Select a deploy
5. Click "View deploy log"

### Function Logs

To view function logs:

1. Go to the Netlify dashboard
2. Select the Fuiyoo site
3. Click "Functions"
4. Select a function to view its logs

### Analytics

Enable Netlify Analytics for insights into:

- Page views
- Unique visitors
- Bandwidth usage
- Top pages
- Error rates

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check the build logs for errors
   - Ensure all environment variables are set correctly
   - Verify that the build command is correct

2. **Missing Environment Variables**:
   - Check the environment variables in the Netlify dashboard
   - Ensure they match the required variables listed above

3. **Function Timeouts**:
   - Check for long-running operations in API routes
   - Consider moving to background tasks for operations that take longer than 10 seconds

4. **Edge Function Compatibility**:
   - Ensure that Edge Functions only use compatible APIs
   - Avoid Node.js-specific APIs in Edge Functions

## Continuous Integration

### GitHub Integration

Netlify is integrated with GitHub for continuous deployment:

1. Code pushed to the main branch triggers a production deployment
2. Pull requests trigger deploy previews
3. Comments on pull requests include links to deploy previews

### Branch Deploys

Configure branch deploys for staging environments:

1. Go to the Netlify dashboard
2. Select the Fuiyoo site
3. Click "Site settings" > "Build & deploy" > "Continuous deployment"
4. Under "Branch deploys", add branches to deploy (e.g., `staging`, `develop`)

## Conclusion

By following this guide, you can ensure that Fuiyoo is deployed optimally on Netlify, taking advantage of edge functions, caching, and continuous deployment for the best possible performance and developer experience.
