# Event Capacity Changes

## Overview

This document outlines the changes made to the event capacity management system in the Fuiyoo platform. The key change is moving capacity management from the category level to the event level, while maintaining backward compatibility with existing data.

## Changes Made

### 1. Event-Level Capacity

- Added `totalCapacity` field to the event model to represent the maximum number of participants across all categories
- Added UI for setting total capacity in the Basic Details step of the event creation wizard
- Removed capacity field from individual categories

### 2. Registration Closing Dates

- Added `registrationCloseDate` field to the event model for a global registration closing date
- Added `allowCategorySpecificClosingDates` toggle to enable/disable per-category closing dates
- Updated category form to respect the global setting

### 3. T-Shirt Options

- Added a new step in the event wizard for configuring T-shirt options
- Added `tshirtOptions` field to the event model with the following properties:
  - `enabled`: Boolean to toggle T-shirt functionality
  - `sizes`: Array of available sizes
  - `description`: Optional description of the T-shirts
  - `sizeChartImage`: Optional image for size chart

### 4. Category Management Improvements

- Added duplicate category functionality
- Added confirmation dialog for category deletion
- Updated category form to use JSON-based properties approach
- Maintained backward compatibility with legacy fields

## Database Schema

The event table now includes:

```sql
total_capacity INTEGER,
registration_close_date TIMESTAMP WITH TIME ZONE,
allow_category_specific_closing_dates BOOLEAN DEFAULT FALSE,
tshirt_options JSONB
```

The event_categories table maintains the capacity field for backward compatibility but it's no longer used for new events.

## Implementation Details

### JSON-Based Properties

Categories now use a JSON-based properties approach:

```typescript
properties: {
  price: number,
  startTime: string,
  earlyBirdPrice: number,
  earlyBirdEndDate: string,
  bibPrefix: string,
  bibStartNumber: string,
  bibRequireGeneration: boolean,
  registrationOpen: boolean,
  registrationCloseDate: string,
  registrationLimit: number,
  registrationCount: number,
  // Event type specific properties
  // ...
}
```

This approach allows for more flexibility in adding new properties without requiring database schema changes.

## Migration Strategy

- Legacy fields have been removed from the codebase
- A database migration script migrates data from legacy fields to the properties field
- All new categories use the JSON-based properties approach exclusively

## Future Considerations

- Complete migration of all legacy data to the new format
- Add more sophisticated capacity management features (waitlists, etc.)
- Enhance T-shirt management with inventory tracking
