-- Add city column to events table if it doesn't exist
-- Migration: 0017_add_city_to_events.sql

-- Start transaction for atomic migration
BEGIN;

-- Add city column to events table if it doesn't exist
ALTER TABLE events ADD COLUMN IF NOT EXISTS city TEXT;

-- Force schema cache reload
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('0017_add_city_to_events', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit transaction
COMMIT; 