# Authentication Documentation

## Overview

This project uses Supa<PERSON> Auth for authentication. It supports:

- Email/password authentication
- Google OAuth authentication
- Password reset
- Email verification

## Authentication Flow

1. **Sign Up**:
   - User enters email and password
   - Supa<PERSON> sends a confirmation email
   - User clicks the confirmation link
   - User is redirected to the dashboard

2. **Sign In**:
   - User enters email and password
   - User is authenticated and redirected to the dashboard
   - Or user signs in with Google OAuth
   - User is authenticated and redirected to the dashboard

3. **Password Reset**:
   - User clicks "Forgot password?" on the sign-in page
   - User enters their email address
   - Supabase sends a password reset link to the user's email
   - User clicks the link in the email
   - User is redirected to the update password page
   - User enters a new password
   - User is redirected to the dashboard

4. **Sign Out**:
   - User clicks sign out
   - User is signed out and redirected to the home page

## Implementation Details

### Middleware

The middleware in `src/middleware.ts` handles authentication for protected routes:

- It uses `updateSession` to refresh the session
- It checks if the user is authenticated
- It redirects unauthenticated users to the sign-in page
- It preserves the original URL for redirection after authentication

### Auth Context

The auth context in `src/contexts/auth-context.tsx` provides authentication state to the application:

- It listens for auth state changes
- It fetches user data from the database
- It provides user data to components

### Sign-In/Sign-Up Components

The sign-in and sign-up components handle user authentication:

- They validate user inputs
- They handle form submission
- They display error messages
- They redirect users after authentication

### Auth Callback

The auth callback in `src/app/auth/callback/route.ts` handles authentication callbacks from Supabase:

- It exchanges the auth code for a session
- It verifies OTP tokens
- It redirects users after authentication

### Password Reset

The password reset functionality is implemented in two parts:

1. **Request Password Reset**:
   - The `src/app/reset-password/reset-password-form.tsx` component allows users to request a password reset
   - It uses the `resetPassword` function from `src/lib/supabase/auth.ts`
   - It validates the email address and displays appropriate error messages
   - It shows a success message when the reset email is sent

2. **Update Password**:
   - The `src/app/update-password/update-password-form.tsx` component allows users to set a new password
   - It uses the `updatePassword` function from `src/lib/supabase/auth.ts`
   - It validates the new password using the `PasswordStrengthIndicator` component
   - It shows a success message when the password is updated
   - It redirects the user to the dashboard after a successful password update

## Security Considerations

- **CSRF Protection**: Supabase Auth uses secure cookies with SameSite attributes
- **XSS Protection**: Content Security Policy headers are added to responses
- **Session Management**: Sessions are refreshed in middleware
- **Password Security**: Passwords are validated for strength
- **Rate Limiting**: Supabase Auth has built-in rate limiting

## Environment Variables

The following environment variables are required:

- `NEXT_PUBLIC_SUPABASE_URL`: The URL of your Supabase project
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: The anonymous key of your Supabase project
- `SUPABASE_SERVICE_ROLE_KEY`: The service role key of your Supabase project (for admin operations)

## Deployment Considerations

When deploying to Netlify:

- Add the environment variables to your Netlify site
- Configure redirects in `netlify.toml`
- Enable prerendering for static pages

## Best Practices

1. **Use `getUser()` instead of `getSession()`**: The `getUser()` method is more secure as it validates the JWT.

2. **Handle cookies properly**: Use the Supabase SSR helpers to handle cookies correctly.

3. **Protect sensitive routes**: Use middleware to protect routes that require authentication.

4. **Validate user input**: Always validate user input on both client and server.

5. **Use HTTPS**: Always use HTTPS in production to protect user data.

6. **Implement proper error handling**: Handle authentication errors gracefully.

7. **Use environment variables**: Store sensitive information in environment variables.

8. **Add security headers**: Add security headers to protect against common attacks.

9. **Implement proper redirects**: Redirect users to their intended destination after authentication.

10. **Use the singleton pattern**: Use the singleton pattern for Supabase clients to avoid creating multiple clients.

## Troubleshooting

### Common Issues

1. **Session not persisting**: Make sure cookies are being set correctly in the middleware.

2. **Redirect loops**:
   - Check for redirect loops in the middleware and auth callback.
   - Use the `no_redirect=true` parameter to break redirect loops.
   - See `docs/auth-redirect-loop-fix.md` for detailed information on handling redirect loops.

3. **CORS errors**: Make sure the Supabase URL is configured correctly.

4. **Missing environment variables**: Check that all required environment variables are set.

5. **Authentication errors**: Check the Supabase logs for authentication errors.

6. **OAuth state errors**:
   - If you see `bad_oauth_state` errors, try clearing cookies and local storage.
   - Visit `/sign-in?reset_auth=true` to force a clean authentication state.

### Debugging

1. **Check the logs**: Check the browser console and server logs for errors.

2. **Use the Supabase dashboard**: Use the Supabase dashboard to check authentication status.

3. **Check the cookies**:
   - Use the browser developer tools to check the cookies.
   - Look for Supabase cookies like `sb-access-token`, `sb-refresh-token`, etc.
   - If cookies are missing or invalid, try clearing them and signing in again.

4. **Check the network requests**:
   - Use the browser developer tools to check the network requests.
   - Look for 401 or 403 errors in the network tab.
   - Check if the session refresh requests are succeeding.

5. **Check the environment variables**: Make sure all required environment variables are set.

6. **Reset authentication state**:
   - Visit the `/auth/reset` page to use the dedicated authentication reset tool.
   - Alternatively, use the `/api/auth/reset-state` endpoint to clear all Supabase cookies.
   - You can also visit `/sign-in?reset_auth=true` to trigger a reset and sign in again.
   - For programmatic access, make a POST request to `/api/auth/reset-state`.
   - For redirection after reset, use `/api/auth/reset-state?redirect_to=/your-path`.

## References

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Next.js Authentication Documentation](https://nextjs.org/docs/authentication)
- [Netlify Deployment Documentation](https://docs.netlify.com/configure-builds/environment-variables/)
