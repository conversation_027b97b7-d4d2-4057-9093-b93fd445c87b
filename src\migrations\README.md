# Database Migrations

This directory contains SQL migration scripts for the Fuiyoo application.

## Running Migrations

You can run these migrations using the Supabase CLI or directly in the Supabase dashboard SQL editor.

### Using Supabase CLI

1. Install the Supabase CLI if you haven't already:
   ```bash
   npm install -g supabase
   ```

2. Login to your Supabase account:
   ```bash
   supabase login
   ```

3. Run the migration script:
   ```bash
   supabase db execute --project-ref YOUR_PROJECT_REF --file ./src/migrations/add_event_images.sql
   ```

### Using Supabase Dashboard

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Create a new query
4. Copy and paste the contents of the migration file
5. Run the query

## Migration Files

- `add_event_images.sql` - Adds poster_image and cover_image columns to the events table for storing image metadata
