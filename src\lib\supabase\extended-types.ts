import type { Database as OriginalDatabase } from './types';
import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Result type for the exec_sql RPC function
 */
export interface SqlExecResult {
  data: Record<string, any>[] | null;
  error: { message: string } | null;
}

/**
 * Extended database interface to add custom tables & functions
 */
export interface ExtendedDatabase extends OriginalDatabase {
  public: OriginalDatabase['public'] & {
    Tables: OriginalDatabase['public']['Tables'] & {
      schema_migrations: {
        Row: {
          version: string;
          applied_at: string;
        };
        Insert: {
          version: string;
          applied_at?: string;
        };
        Update: {
          version?: string;
          applied_at?: string;
        };
      };
      information_schema: {
        columns: {
          Row: {
            column_name: string;
            data_type: string;
            is_nullable: string;
            table_name: string;
            ordinal_position: number;
          };
        };
        tables: {
          Row: {
            table_name: string;
            table_schema: string;
          };
        };
      };
      saved_contacts: OriginalDatabase['public']['Tables']['saved_contacts']; // Reuse existing table definition
    };
    Functions: OriginalDatabase['public']['Functions'] & {
      pg_notify: {
        Args: {
          channel: string;
          payload: string;
        };
        Returns: void;
      };
      exec_sql: {
        Args: {
          sql: string;
        };
        Returns: Record<string, any>[];
      };
    };
  };
}

/**
 * Extended Supabase client type that includes custom RPC methods
 */
export type ExtendedSupabaseClient = SupabaseClient & {
  rpc: (procedure: string, params?: { [key: string]: any }) => Promise<{
    data: any;
    error: { message: string } | null;
  }>;
};

/**
 * Custom Supabase client type with our extended database
 */
export type CustomSupabaseClient = SupabaseClient<ExtendedDatabase>;

/**
 * Helper function to cast Supabase object as any to bypass type issues
 * Use this only when you need to access tables or functions not properly typed
 */
export function asAny<T>(obj: T): any {
  return obj as any;
} 