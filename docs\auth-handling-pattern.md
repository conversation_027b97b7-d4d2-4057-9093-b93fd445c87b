# Authentication Handling <PERSON>tern

This document outlines the recommended pattern for handling authentication in server components, especially in dashboard pages where race conditions can occur between middleware authentication checks and page-level authentication checks.

## Problem

We encountered a race condition in the `/dashboard/organizations` page where:

1. The middleware correctly verified the user was authenticated
2. The page component still tried to redirect to sign-in
3. This created a redirect loop or inconsistent behavior

The issue occurs because:
- Middleware runs first and verifies authentication
- The page component then runs its own authentication check
- If the session isn't immediately available in the page component, it incorrectly redirects

## Solution Pattern

### 1. Check for Auth Cookies First

Before making any database calls, check if the auth cookies exist. This is a quick way to determine if the user is likely authenticated:

```typescript
import { cookies } from 'next/headers';

export default async function DashboardPage() {
  // Check if we have Supabase auth cookies
  const cookieStore = await cookies();
  const hasAuthCookies = cookieStore.getAll().some(cookie =>
    cookie.name.includes('auth-token') || cookie.name.includes('supabase-auth')
  );

  console.log('[DEBUG] Page - Has auth cookies:', hasAuthCookies);

  if (!hasAuthCookies) {
    console.log('[DEBUG] Page - No auth cookies found, redirecting to sign-in');
    // If no auth cookies, redirect to sign-in
    redirect('/sign-in');
  }

  // Rest of the code...
}
```

### 2. Use Cached Auth User Check

Use the cached `getAuthUser()` function from `@/lib/auth-utils` as the primary authentication check. This function is cached and should be consistent with what the middleware has already verified.

```typescript
import { getAuthUser } from '@/lib/auth-utils';

// First check if we have a user from the auth context
const authUser = await getAuthUser();

// Create Supabase client
const supabase = await createClient();

// If no auth user from cache, try to get from session
let userId: string;

if (!authUser) {
  console.log('[DEBUG] Page - No auth user found, checking session');
  // Double-check with session as fallback
  const { data: { session }, error } = await supabase.auth.getSession();

  // Handle session check...
} else {
  userId = authUser.id;
  console.log('[DEBUG] Page - Got user ID from auth context:', userId);
}
```

### 3. Show Loading State Instead of Redirecting

If we have auth cookies but no session, show a loading state instead of redirecting. This prevents redirect loops:

```typescript
if (!session || !session.user) {
  console.log('[DEBUG] Page - No session found, but has auth cookies. Using fallback UI');
  // We have auth cookies but no session - this is likely a race condition
  // Instead of redirecting, show a loading state
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-2">Page Title</h1>
      <p className="text-gray-500 mb-6">Loading your content...</p>
      <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
    </div>
  );
}

userId = session.user.id;
console.log('[DEBUG] Page - Got user ID from session:', userId);
```

### 4. Add Comprehensive Error Handling

Wrap the entire page component in a try-catch block to handle any errors gracefully:

```typescript
try {
  // Authentication and page rendering logic
} catch (error) {
  console.error('[DEBUG] Page - Error rendering page:', error);

  // Return a fallback UI
  return (
    <div className="container mx-auto px-4 py-8">
      <h1>Page Title</h1>
      <p>Error message for users</p>
    </div>
  );
}
```

### 5. Add Debug Logging

Include detailed debug logging to help diagnose issues:

```typescript
console.log('[DEBUG] Page - Initializing');
console.log('[DEBUG] Page - User authenticated:', userId);
```

## Complete Example

```typescript
import { getAuthUser } from '@/lib/auth-utils';
import { createClient } from '@/lib/supabase/pages-client';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

export default async function DashboardPage() {
  console.log('[DEBUG] Page - Initializing');

  try {
    // Check if we have Supabase auth cookies
    const cookieStore = await cookies();
    const hasAuthCookies = cookieStore.getAll().some(cookie =>
      cookie.name.includes('auth-token') || cookie.name.includes('supabase-auth')
    );

    console.log('[DEBUG] Page - Has auth cookies:', hasAuthCookies);

    if (!hasAuthCookies) {
      console.log('[DEBUG] Page - No auth cookies found, redirecting to sign-in');
      // If no auth cookies, redirect to sign-in
      redirect('/sign-in');
    }

    // First check if we have a user from the auth context
    const authUser = await getAuthUser();

    // Create Supabase client
    const supabase = await createClient();

    // If no auth user from cache, try to get from session
    let userId: string;

    if (!authUser) {
      console.log('[DEBUG] Page - No auth user found, checking session');
      // Double-check with session as fallback
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('[DEBUG] Page - Error getting session:', error);
      }

      if (!session || !session.user) {
        console.log('[DEBUG] Page - No session found, but has auth cookies. Using fallback UI');
        // We have auth cookies but no session - this is likely a race condition
        // Instead of redirecting, show a loading state
        return (
          <div className="container mx-auto px-4 py-8">
            <h1 className="text-3xl font-bold mb-2">Page Title</h1>
            <p className="text-gray-500 mb-6">Loading your content...</p>
            <div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>
          </div>
        );
      }

      userId = session.user.id;
      console.log('[DEBUG] Page - Got user ID from session:', userId);
    } else {
      userId = authUser.id;
      console.log('[DEBUG] Page - Got user ID from auth context:', userId);
    }

    // Page-specific logic here

    return (
      <div>
        {/* Page content */}
      </div>
    );
  } catch (error) {
    console.error('[DEBUG] Page - Error rendering page:', error);

    // Return a fallback UI
    return (
      <div>
        <h1>Error</h1>
        <p>We encountered an error. Please try refreshing the page.</p>
      </div>
    );
  }
}
```

## Benefits

1. **Resilience**: The pattern handles various edge cases and race conditions
2. **Debugging**: Comprehensive logging helps diagnose issues
3. **Fallback UI**: Users see a helpful message instead of a broken page
4. **No Redirect Loops**: Shows loading state instead of redirecting when cookies exist but session isn't ready
5. **Consistency**: Follows the same pattern across all dashboard pages

## Implementation

This pattern has been implemented in:
- `/dashboard/organizations`

And should be applied to other dashboard pages that experience similar issues.
