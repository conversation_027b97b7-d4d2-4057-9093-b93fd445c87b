'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { ArrowLeft, Construction, Map, Compass } from 'lucide-react'

export default function NotFound() {
  const [position, setPosition] = useState({ x: 0, y: 0 })
  
  // Interactive cursor effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setPosition({ x: e.clientX, y: e.clientY })
    }
    
    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  return (
    <div className="h-screen w-full bg-gradient-to-b from-gray-900 via-slate-800 to-gray-900 flex items-center justify-center overflow-hidden relative">
      {/* Road pattern */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-full h-24 bg-gray-800 flex items-center justify-center">
          <div className="w-full flex justify-center">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="h-4 w-16 bg-yellow-400 mx-12"
                initial={{ opacity: 0 }}
                animate={{ 
                  opacity: [0, 1, 0],
                  x: [100, -100]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.1,
                  ease: "linear"
                }}
              />
            ))}
          </div>
        </div>
      </div>
      
      {/* Road blocks */}
      <motion.div 
        className="absolute"
        animate={{ rotate: [0, 5, -5, 0] }}
        transition={{ duration: 5, repeat: Infinity }}
      >
        <div className="relative">
          <motion.div
            className="absolute -top-32 -left-32"
            initial={{ y: -100, x: -100 }}
            animate={{ y: [-20, 0, -20], x: [-20, 0, -20] }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
          >
            <Construction size={64} className="text-orange-500" />
          </motion.div>
          
          <motion.div
            className="absolute -top-32 -right-32"
            initial={{ y: -100, x: 100 }}
            animate={{ y: [0, -20, 0], x: [0, -20, 0] }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
          >
            <Map size={64} className="text-blue-500" />
          </motion.div>
        </div>
        
        <motion.div
          className="relative z-10 bg-slate-700 rounded-lg p-12 backdrop-blur-sm bg-opacity-80 shadow-xl border border-slate-600"
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 1, delay: 0.2 }}
        >
          <div className="flex flex-col items-center text-center">
            <motion.div
              animate={{ scale: [1, 1.1, 1], rotate: [0, 3, -3, 0] }}
              transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
            >
              <h1 className="text-9xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-red-500">404</h1>
            </motion.div>
            
            <h2 className="text-3xl mt-4 font-bold text-white">Road Block Ahead!</h2>
            <p className="text-slate-300 mt-4 max-w-md">
              Looks like you've wandered off the main route! Our digital road crew is working on this path.
            </p>
            
            <div className="flex items-center justify-center mt-8">
              <motion.div 
                className="relative"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link 
                  href="/" 
                  className="flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-full font-medium group"
                >
                  <ArrowLeft className="mr-2 h-4 w-4 group-hover:animate-pulse" />
                  <span>Back to safety</span>
                </Link>
                
                <motion.div
                  className="absolute -right-6 -top-6"
                  animate={{ 
                    rotate: 360,
                    y: [0, 5, 0],
                  }}
                  transition={{ 
                    rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                    y: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                  }}
                >
                  <Compass className="h-10 w-10 text-blue-400" />
                </motion.div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </motion.div>
      
      {/* Mouse follower */}
      <motion.div 
        className="hidden md:block fixed w-12 h-12 rounded-full pointer-events-none z-50 mix-blend-difference"
        style={{ 
          left: position.x - 24, 
          top: position.y - 24,
          backgroundColor: "rgba(255, 255, 255, 0.1)" 
        }}
        animate={{
          scale: [1, 1.2, 1]
        }}
        transition={{
          duration: 1,
          repeat: Infinity
        }}
      />
    </div>
  )
} 