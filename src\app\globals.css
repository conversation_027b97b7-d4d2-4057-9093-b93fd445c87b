@import "tailwindcss";

@theme {
  /* Base colors */
  --background: 0 0% 100%;
  --foreground: 224 71.4% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 224 71.4% 4.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 224 71.4% 4.1%;

  /* Primary color (indigo-500) and variants */
  --primary: 238 75% 58%;
  --primary-foreground: 0 0% 100%;
  --primary-hover: 238 75% 53%;
  --primary-active: 238 75% 48%;
  --primary-focus: 238 75% 58%;
  --primary-subtle: 238 75% 96%;
  --primary-50: 238 75% 58% / 0.05;
  --primary-100: 238 75% 58% / 0.1;
  --primary-200: 238 75% 58% / 0.2;
  --primary-300: 238 75% 58% / 0.3;

  /* Secondary color (teal) */
  --secondary: 174 75% 45%;
  --secondary-foreground: 0 0% 100%;
  --secondary-hover: 174 75% 40%;
  --secondary-active: 174 75% 35%;

  /* Accent color (vibrant pink) */
  --accent: 330 85% 60%;
  --accent-foreground: 0 0% 100%;
  --accent-hover: 330 85% 55%;
  --accent-active: 330 85% 50%;

  /* Interactive state variables */
  --focus-ring: 238 75% 58% / 0.35;
  --selected-background: 238 75% 58% / 0.15;
  --hover-overlay: 0 0% 0% / 0.05;
  --active-overlay: 0 0% 0% / 0.1;

  /* Accessibility enhancement colors */
  --focus-visible: 238 75% 58% / 0.7;
  --outline-color: 238 75% 58% / 0.5;
  --glow-color: 238 75% 58% / 0.25;

  /* Neutral colors */
  --muted: 220 14.3% 95.9%;
  --muted-foreground: 220 8.9% 46.1%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 238 75% 58% / 0.3;
  --radius: 0.5rem;

  /* Status colors */
  --success: 142 70% 45%;
  --warning: 38 92% 50%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 20% 98%;

  /* Chart colors (vibrant and cheerful) */
  --chart-1: 238 75% 58%; /* Indigo (Primary) */
  --chart-2: 174 75% 45%; /* Teal */
  --chart-3: 330 85% 60%; /* Pink */
  --chart-4: 38 92% 50%;  /* Amber */
  --chart-5: 142 70% 45%; /* Green */

  /* Dark mode base colors */
  --dark-background: 224 71.4% 4.1%;
  --dark-foreground: 210 20% 98%;
  --dark-card: 224 71.4% 4.1%;
  --dark-card-foreground: 210 20% 98%;
  --dark-popover: 224 71.4% 4.1%;
  --dark-popover-foreground: 210 20% 98%;

  /* Dark mode primary color (indigo-500) and variants */
  --dark-primary: 238 75% 65%;
  --dark-primary-foreground: 0 0% 100%;
  --dark-primary-hover: 238 75% 70%;
  --dark-primary-active: 238 75% 75%;
  --dark-primary-focus: 238 75% 65%;
  --dark-primary-subtle: 238 30% 20%;
  --dark-primary-50: 238 75% 65% / 0.05;
  --dark-primary-100: 238 75% 65% / 0.1;
  --dark-primary-200: 238 75% 65% / 0.2;
  --dark-primary-300: 238 75% 65% / 0.3;

  /* Dark mode secondary color (teal) */
  --dark-secondary: 174 75% 40%;
  --dark-secondary-foreground: 0 0% 100%;

  /* Dark mode accent color (vibrant pink) */
  --dark-accent: 330 85% 65%;
  --dark-accent-foreground: 0 0% 100%;

  /* Dark mode interactive state variables */
  --dark-focus-ring: 238 75% 65% / 0.4;
  --dark-selected-background: 238 75% 65% / 0.25;
  --dark-hover-overlay: 255 255 255 / 0.05;
  --dark-active-overlay: 255 255 255 / 0.1;

  /* Dark mode accessibility enhancement colors */
  --dark-focus-visible: 238 75% 65% / 0.8;
  --dark-outline-color: 238 75% 65% / 0.6;
  --dark-glow-color: 238 75% 65% / 0.3;

  /* Dark mode neutral colors */
  --dark-muted: 215 27.9% 16.9%;
  --dark-muted-foreground: 217.9 10.6% 64.9%;
  --dark-border: 215 27.9% 16.9%;
  --dark-input: 215 27.9% 16.9%;
  --dark-ring: 238 75% 65% / 0.4;

  /* Container config */
  --container-padding: 2rem;
  --container-max-width-2xl: 1400px;

  /* Border radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
}

@layer base {
  .dark {
    --background: var(--dark-background);
    --foreground: var(--dark-foreground);
    --card: var(--dark-card);
    --card-foreground: var(--dark-card-foreground);
    --popover: var(--dark-popover);
    --popover-foreground: var(--dark-popover-foreground);
    --primary: var(--dark-primary);
    --primary-foreground: var(--dark-primary-foreground);
    --primary-hover: var(--dark-primary-hover);
    --primary-active: var(--dark-primary-active);
    --primary-focus: var(--dark-primary-focus);
    --primary-subtle: var(--dark-primary-subtle);
    --primary-50: var(--dark-primary-50);
    --primary-100: var(--dark-primary-100);
    --primary-200: var(--dark-primary-200);
    --primary-300: var(--dark-primary-300);
    --focus-ring: var(--dark-focus-ring);
    --selected-background: var(--dark-selected-background);
    --hover-overlay: var(--dark-hover-overlay);
    --active-overlay: var(--dark-active-overlay);
    --focus-visible: var(--dark-focus-visible);
    --outline-color: var(--dark-outline-color);
    --glow-color: var(--dark-glow-color);
    --secondary: var(--dark-secondary);
    --secondary-foreground: var(--dark-secondary-foreground);
    --muted: var(--dark-muted);
    --muted-foreground: var(--dark-muted-foreground);
    --accent: var(--dark-accent);
    --accent-foreground: var(--dark-accent-foreground);
    --destructive: var(--dark-destructive);
    --destructive-foreground: var(--dark-destructive-foreground);
    --border: var(--dark-border);
    --input: var(--dark-input);
    --ring: var(--dark-ring);
  }
}

@layer base {
  * {
    @apply border-[hsl(var(--border))];
  }
  body {
    @apply bg-[hsl(var(--background))] text-[hsl(var(--foreground))];
  }

  /* Heading styles with primary color */
  h1, h2, h3, h4, h5, h6 {
    color: hsl(var(--primary));
    font-weight: 600;
  }

  /* Logo styling */
  .logo {
    color: hsl(var(--primary));
    fill: hsl(var(--primary));
  }

  .logo-text {
    color: hsl(var(--primary));
  }

  .logo svg * {
    fill: hsl(var(--primary));
  }

  /* Main Fuiyoo logo specific styling */
  .site-logo,
  .site-title,
  .brand-name,
  a:has(.brand-name),
  .app-title,
  header .logo,
  .brand-logo,
  .fuiyoo-logo {
    color: hsl(var(--primary)) !important;
    fill: hsl(var(--primary)) !important;
  }

  .navbar-brand {
    color: hsl(var(--primary));
  }

  /* Header and navigation styling */
  header a,
  nav a {
    color: hsl(var(--foreground));
    transition: color 0.2s ease;
  }

  header a:hover,
  nav a:hover,
  header a.active,
  nav a.active {
    color: hsl(var(--primary));
  }

  .nav-link.active {
    color: hsl(var(--primary));
    font-weight: 500;
  }

  /* Global focus-visible style */
  *:focus-visible {
    outline: 2px solid hsl(var(--focus-visible));
    outline-offset: 2px;
  }

  /* Button state styling */
  button,
  [role="button"],
  .btn {
    &:hover:not(:disabled) {
      background-color: hsl(var(--primary-hover));
    }
    &:active:not(:disabled) {
      background-color: hsl(var(--primary-active));
    }
    &:focus-visible {
      box-shadow: 0 0 0 2px hsl(var(--focus-ring));
    }
  }

  /* Input elements */
  input:focus,
  textarea:focus,
  select:focus {
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 2px hsl(var(--focus-ring));
  }
}

/* Container utility override */
@utility container {
  margin-inline: auto;
  padding-inline: var(--container-padding, 1rem);
  max-width: 100%;
}

@media (min-width: 1400px) {
  .container {
    max-width: var(--container-max-width-2xl, 1400px);
  }
}

/* Keyframes and animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--state-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--state-accordion-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

.animate-gradient-x {
  animation: gradient-x 8s ease infinite;
  background-size: 200% 200%;
}

/* Dashboard styles */
/* Hide the global header when inside dashboard layout */
.dashboard-layout ~ header.global-header,
.dashboard-layout header.global-header,
div.dashboard-layout ~ header.global-header {
  display: none !important;
}

/* Reset padding in dashboard layout since global header is hidden */
.dashboard-layout ~ main,
.dashboard-layout main,
main .dashboard-layout,
div[class*="dashboard-layout"] ~ main,
main:has(.dashboard-layout) {
  padding-top: 0 !important;
}

/* Dashboard header styling */
.dashboard-header h1,
.dashboard-header h2,
.dashboard-header h3,
.page-title {
  color: hsl(var(--primary));
}

/* Enhanced selected state styling */
.selected,
[aria-selected="true"],
.active,
[aria-current="true"],
.is-selected {
  background-color: hsl(var(--selected-background)) !important;
  border-color: hsl(var(--primary)) !important;
  box-shadow: 0 0 0 2px hsl(var(--outline-color)), 0 0 10px 2px hsl(var(--glow-color)) !important;
}

/* Event type selection enhancement */
[role="option"],
.selection-card,
.selectable-item,
.event-type-card {
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

[role="option"]:hover,
.selection-card:hover,
.selectable-item:hover,
.event-type-card:hover {
  background-color: hsl(var(--primary-50));
  transform: translateY(-2px);
  z-index: 5;
}

[role="option"][aria-selected="true"],
.selection-card.selected,
.selectable-item.selected,
.event-type-card.selected {
  background-color: hsl(var(--selected-background)) !important;
  border: 2px solid hsl(var(--primary)) !important;
  box-shadow: 0 0 8px 2px hsl(var(--glow-color)) !important;
  transform: translateY(-3px);
  z-index: 10;
  position: relative;
}

[role="option"][aria-selected="true"] *,
.selection-card.selected *,
.selectable-item.selected *,
.event-type-card.selected * {
  color: hsl(var(--primary)) !important;
}

/* React Select Styles */
.react-select__control {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--input));
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  min-height: 40px !important;
}

.react-select__control:hover {
  border-color: hsl(var(--primary-200));
}

.react-select__control:focus-within {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--focus-ring));
}

.react-select__menu {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--input));
  margin-top: 0.25rem;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  z-index: 50 !important;
}

.react-select__option {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  cursor: pointer !important;
}

.react-select__option:hover {
  background-color: hsl(var(--selected-background));
}

.react-select__option--is-focused {
  background-color: hsl(var(--selected-background)) !important;
  color: hsl(var(--foreground)) !important;
}

.react-select__option--is-selected {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.react-select__single-value {
  color: hsl(var(--foreground)) !important;
}

.react-select__input-container {
  color: hsl(var(--foreground)) !important;
}

.react-select__placeholder {
  color: hsl(var(--muted-foreground)) !important;
}

.react-select__indicators {
  color: hsl(var(--muted-foreground)) !important;
}

.react-select__indicator-separator {
  background-color: hsl(var(--border)) !important;
}

/* Ensure content doesn't overlap with footer */
.main-content {
  min-height: calc(100vh - 4rem);
  padding-bottom: 8rem !important;
}

/* Hide scrollbars but maintain scroll functionality */
.scrollbar-hidden {
  -ms-overflow-style: none;  /* Internet Explorer and Edge */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hidden::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* React Day Picker styles */
.rdp {
  --rdp-cell-size: 40px;
  --rdp-accent-color: hsl(var(--primary));
  --rdp-background-color: hsl(var(--primary-100));
  --rdp-accent-color-dark: hsl(var(--primary));
  --rdp-background-color-dark: hsl(var(--primary-200));
  --rdp-outline: 2px solid hsl(var(--outline-color));
  --rdp-outline-selected: 2px solid hsl(var(--primary));
  margin: 0;
}

.rdp-months {
  justify-content: space-between;
}

.rdp-month {
  background-color: transparent;
}

.rdp-day_selected,
.rdp-day_selected:focus-visible,
.rdp-day_selected:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-day:hover:not(.rdp-day_selected) {
  background-color: hsl(var(--selected-background));
}

.rdp-day:focus-visible:not(.rdp-day_selected) {
  outline: 2px solid hsl(var(--focus-visible));
  outline-offset: 2px;
}

.rdp-day_today {
  background-color: hsl(var(--primary-100));
  font-weight: bold;
}

/* Form element states */
.form-input:focus,
.form-select:focus,
.form-textarea:focus,
.form-checkbox:focus,
.form-radio:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsl(var(--focus-ring));
  outline: none;
}

.form-checkbox:checked,
.form-radio:checked {
  background-color: hsl(var(--primary));
  border-color: hsl(var(--primary));
}

/* Interactive element states */
.interactive-element:hover {
  background-color: hsl(var(--hover-overlay));
}

.interactive-element:active {
  background-color: hsl(var(--active-overlay));
}

.interactive-element.selected {
  background-color: hsl(var(--selected-background));
}