-- Migration: 0004_auth_migration.sql
-- Description: Migrate from <PERSON> to Supabase Auth
-- This migration adds the necessary fields to link users with Supabase Auth

-- Add auth_user_id column to users table to link with Supabase Auth
ALTER TABLE users ADD COLUMN IF NOT EXISTS auth_user_id UUID;

-- Add email_verified column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE;

-- Create index on auth_user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_auth_user_id ON users(auth_user_id);

-- Add foreign key constraint to link with auth.users table
ALTER TABLE users 
  ADD CONSTRAINT fk_users_auth_user_id 
  FOREIGN KEY (auth_user_id) 
  REFERENCES auth.users(id) 
  ON DELETE SET NULL;

-- Create RLS policies for users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own data
CREATE POLICY "Users can view their own data" 
  ON users FOR SELECT 
  USING (auth_user_id = auth.uid());

-- Policy: Users can update their own data
CREATE POLICY "Users can update their own data" 
  ON users FOR UPDATE 
  USING (auth_user_id = auth.uid());

-- Policy: Admins can view all users
CREATE POLICY "Admins can view all users" 
  ON users FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_user_id = auth.uid()
      AND users.role IN ('admin', 'super_admin')
    )
  );

-- Policy: Admins can update all users
CREATE POLICY "Admins can update all users" 
  ON users FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_user_id = auth.uid()
      AND users.role IN ('admin', 'super_admin')
    )
  );

-- Policy: Event organizers can view users registered for their events
CREATE POLICY "Event organizers can view users registered for their events" 
  ON users FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM events e
      JOIN registrations r ON e.id = r.event_id
      JOIN users organizer ON e.user_id = organizer.id
      WHERE r.user_id = users.id
      AND organizer.auth_user_id = auth.uid()
      AND organizer.role = 'event_organizer'
    )
  );

-- Create RLS policies for events table
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can view published events
CREATE POLICY "Anyone can view published events" 
  ON events FOR SELECT 
  USING (is_published = true);

-- Policy: Users can view their own events
CREATE POLICY "Users can view their own events" 
  ON events FOR SELECT 
  USING (
    user_id IN (
      SELECT id FROM users WHERE auth_user_id = auth.uid()
    )
  );

-- Policy: Users can update their own events
CREATE POLICY "Users can update their own events" 
  ON events FOR UPDATE 
  USING (
    user_id IN (
      SELECT id FROM users WHERE auth_user_id = auth.uid()
    )
  );

-- Policy: Admins can view all events
CREATE POLICY "Admins can view all events" 
  ON events FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_user_id = auth.uid()
      AND users.role IN ('admin', 'super_admin')
    )
  );

-- Create RLS policies for registrations table
ALTER TABLE registrations ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own registrations
CREATE POLICY "Users can view their own registrations" 
  ON registrations FOR SELECT 
  USING (
    user_id IN (
      SELECT id FROM users WHERE auth_user_id = auth.uid()
    )
  );

-- Policy: Users can create their own registrations
CREATE POLICY "Users can create their own registrations" 
  ON registrations FOR INSERT 
  WITH CHECK (
    user_id IN (
      SELECT id FROM users WHERE auth_user_id = auth.uid()
    )
  );

-- Policy: Event organizers can view registrations for their events
CREATE POLICY "Event organizers can view registrations for their events" 
  ON registrations FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM events e
      JOIN users organizer ON e.user_id = organizer.id
      WHERE e.id = registrations.event_id
      AND organizer.auth_user_id = auth.uid()
      AND organizer.role = 'event_organizer'
    )
  );

-- Policy: Admins can view all registrations
CREATE POLICY "Admins can view all registrations" 
  ON registrations FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_user_id = auth.uid()
      AND users.role IN ('admin', 'super_admin')
    )
  );

-- Create a function to sync user roles with RLS policies
CREATE OR REPLACE FUNCTION sync_user_role()
RETURNS TRIGGER AS $$
BEGIN
  -- No additional logic needed as roles are stored directly in the users table
  -- This function can be extended later if needed
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to sync user roles when updated
CREATE TRIGGER trigger_sync_user_role
AFTER UPDATE OF role ON users
FOR EACH ROW
EXECUTE FUNCTION sync_user_role();

-- Create a function to handle user registration
CREATE OR REPLACE FUNCTION handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO users (auth_user_id, email, role, created_at)
  VALUES (
    NEW.id, 
    NEW.email,
    'user',
    NOW()
  )
  ON CONFLICT (auth_user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger for new user registration
CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION handle_new_user();
