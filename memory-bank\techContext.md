# Technical Context

## Core Technologies

### Frontend Framework
- **Next.js 15.3.0**: Using the App Router pattern for client and server components
- **React 19**: For UI component architecture
- **TypeScript**: For type safety and developer experience

### Styling
- **Tailwind CSS**: For utility-first styling
- **shadcn/ui**: UI component library built on Radix UI primitives
- **Radix UI**: Accessible UI primitives for React components

### Data Management
- **Mock Data**: Currently using mockEvents.ts for development
- **Supabase**: PostgreSQL database with edge capabilities
- **Server Actions**: 'use server' functions for database operations

### Authentication
- **Clerk**: For user authentication and management

### Deployment
- **Edge-first**: Configured for optimal global performance
- **Standalone Output**: Using `output: 'standalone'` in Next.js config

## Development Environment
- **pnpm**: Package manager
- **macOS**: Development on darwin 19.6.0
- **Next.js Dev Server**: Running on dynamic ports (defaulting to 3000)

## Configuration
- **Images**: Configured for Pexels remote patterns
- **Optimizations**: Package imports optimized for @/components/ui
- **TypeScript**: Using strict mode with build error ignoring for development

## Key Features
- **Events Management**: Discovery, filtering, and registration for events
- **Contacts Management**: User can save contact information for faster event registration
- **Admin Dashboard**: For managing users, events, and organization applications
- **Profile Management**: Users can manage their profile and saved contacts

## Key Files
- **mockEvents.ts**: Contains event data for development
- **EventSearch.tsx**: Client component for searching and filtering events
- **next.config.js**: Next.js configuration including image domains and webpack setup
- **contacts-list.tsx**: Component for managing saved contacts
- **app/actions/**: Server action functions for database operations 