
> fuiyoo@0.1.0 lint
> next lint

Building with NODE_ENV: production
Node.js version: v22.16.0
Using Node.js 22 optimizations: 4096MB

./src/app/(authenticated)/profile/edit/personal/page.tsx
6:11  Warning: 'SelectOption' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/(authenticated)/profile/edit/personal/profile-form.tsx
8:26  Warning: 'State' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/(legal)/cookies/page.tsx
18:51  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:54  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:57  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:60  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:66  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:70  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
92:34  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
150:55  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
150:68  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities

./src/app/(legal)/privacy-policy/page.tsx
18:30  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:34  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:36  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:41  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:46  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:49  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
102:64  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
102:77  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities

./src/app/(legal)/terms/page.tsx
18:54  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
18:60  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
19:88  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
19:97  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
67:55  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
68:37  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/about/page.tsx
41:22  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
41:29  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
45:36  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
82:54  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
161:13  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
161:45  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
162:22  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
163:16  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/actions/activity.ts
64:34  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
65:29  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/actions/contacts.ts
6:10  Warning: 'createAdminClient' is defined but never used.  @typescript-eslint/no-unused-vars
131:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
221:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/actions/data-export.ts
7:28  Warning: 'DataExport' is defined but never used.  @typescript-eslint/no-unused-vars
8:15  Warning: 'ExportFormat' is defined but never used.  @typescript-eslint/no-unused-vars
8:34  Warning: 'ExportType' is defined but never used.  @typescript-eslint/no-unused-vars
10:10  Warning: 'cookies' is defined but never used.  @typescript-eslint/no-unused-vars
66:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
106:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
148:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
167:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
195:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
219:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
224:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
226:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
235:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
249:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
259:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/actions/events.ts
7:10  Warning: 'FieldMappingRepository' is defined but never used.  @typescript-eslint/no-unused-vars
15:3  Warning: 'EventField' is defined but never used.  @typescript-eslint/no-unused-vars
16:3  Warning: 'FieldMapping' is defined but never used.  @typescript-eslint/no-unused-vars
19:10  Warning: 'CreateEventInput' is defined but never used.  @typescript-eslint/no-unused-vars
22:10  Warning: 'redirect' is defined but never used.  @typescript-eslint/no-unused-vars
178:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
181:3  Warning: 'authUser' is defined but never used.  @typescript-eslint/no-unused-vars
181:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
315:44  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
511:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
525:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
569:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
572:3  Warning: 'authUser' is defined but never used.  @typescript-eslint/no-unused-vars
572:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
574:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
612:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
632:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
636:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
652:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
690:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
715:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
732:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
737:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
764:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
765:20  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
817:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
818:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
835:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
838:25  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
880:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
885:42  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
889:45  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
890:39  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
895:49  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
896:43  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
959:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
964:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1039:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1040:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1132:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1133:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1240:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1264:31  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1265:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1382:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1405:31  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1406:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1446:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1458:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1474:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1508:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1514:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1540:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1572:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1619:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1624:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1655:13  Warning: 'emailError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
1668:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1674:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1690:48  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1698:15  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1711:15  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1735:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
1743:29  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1832:11  Warning: 'userId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
1934:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
2057:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/actions/privacy.ts
5:10  Warning: 'ConsentSchema' is defined but never used.  @typescript-eslint/no-unused-vars
5:25  Warning: 'ConsentVersionSchema' is defined but never used.  @typescript-eslint/no-unused-vars
93:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
119:45  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
426:57  Warning: 'userId' is defined but never used.  @typescript-eslint/no-unused-vars
623:13  Warning: 'createTableResult' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/admin/actions.ts
6:10  Warning: 'createClient' is defined but never used.  @typescript-eslint/no-unused-vars
75:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
132:39  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
200:39  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
270:39  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
340:39  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
356:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
414:39  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
430:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/admin/categories/category-client.tsx
36:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
73:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
100:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/admin/migrations/registration-fields/page.tsx
59:81  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/admin/users/components/UserManagement.tsx
43:75  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/admin/users/page.tsx
48:31  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/admin/categories/route.ts
8:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
26:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
43:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
66:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/admin/migrate-users/route.ts
10:28  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
33:37  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
57:45  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
68:55  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
69:34  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
75:53  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
78:44  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/admin/users/route.ts
3:10  Warning: 'UserRole' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/auth/clear-session/route.ts
25:35  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/auth/reset-state/route.ts
64:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/api/auth/session-check/route.ts
26:30  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/auth/sign-out/route.ts
35:30  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/categories/route.ts
4:27  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/countries/route.ts
65:42  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/debug-session/route.ts
12:27  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/events/images/route.ts
19:19  Warning: 'eventData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
210:41  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/events/register/route.ts
99:40  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
142:45  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
149:60  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
152:53  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/events/route.ts
4:10  Warning: 'toDate' is defined but never used.  @typescript-eslint/no-unused-vars
5:16  Warning: 'uuidv4' is defined but never used.  @typescript-eslint/no-unused-vars
230:37  Warning: 'userId' is defined but never used.  @typescript-eslint/no-unused-vars
233:11  Warning: 'currentDate' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/api/export/[id]/route.ts
106:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
278:29  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
282:31  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
282:50  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
283:42  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
299:13  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/health/schema/route.ts
75:28  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/me/route.ts
21:40  Warning: 'authIdError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
53:39  Warning: 'emailError' is assigned a value but never used.  @typescript-eslint/no-unused-vars
62:40  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/migrations/event-images-table/route.ts
6:28  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
16:11  Warning: 'userId' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/api/migrations/registration-fields-table/route.ts
6:28  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
35:37  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/migrations/tshirt-size/route.ts
4:28  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
29:37  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/migrations/users-mapping-table/route.ts
14:11  Warning: 'userId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
27:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
29:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/api/ping/route.ts
3:27  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars
11:28  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/profile/completion/route.ts
27:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
35:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
39:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
44:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
57:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
77:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/api/profile/route.ts
41:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/refresh-schema/route.ts
15:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/scheduler/export-processor/route.ts
50:22  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
113:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
139:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/api/schema-fix/route.ts
69:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
98:15  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
107:19  Warning: 'notifyData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
113:19  Warning: 'testData' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/api/states/route.ts
137:20  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/upload/route.ts
17:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
72:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
74:13  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
88:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
95:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
114:47  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
115:32  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/user/profile/route.ts
3:10  Warning: 'SupabaseClient' is defined but never used.  @typescript-eslint/no-unused-vars
4:10  Warning: 'Database' is defined but never used.  @typescript-eslint/no-unused-vars
87:48  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
90:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
150:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
183:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
241:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/api/user/stats/route.ts
12:27  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/webhooks/supabase/route.ts
1:10  Warning: 'createClient' is defined but never used.  @typescript-eslint/no-unused-vars
70:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
93:37  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
112:40  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
112:55  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
148:40  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
148:55  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
172:40  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
172:55  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/auth/callback/route.ts
250:37  Warning: 'sessionError' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/auth/clear/page.tsx
11:87  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
127:71  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/auth/reset/page.tsx
11:87  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
67:68  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/auth-debug/page.tsx
9:48  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
19:43  Warning: 'error' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/auth-test/page.tsx
10:10  Warning: 'refreshCount' is assigned a value but never used.  @typescript-eslint/no-unused-vars
14:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
20:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
51:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
80:15  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
89:15  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/careers/page.tsx
58:17  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
114:46  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
124:73  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
170:15  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
170:60  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
182:29  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
182:65  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
182:96  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
183:42  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/contact/page.tsx
18:43  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/dashboard/admin/schema/page.tsx
11:40  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
58:9  Warning: 'clearNextCache' is assigned a value but never used.  @typescript-eslint/no-unused-vars
125:52  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/dashboard/events/debug.ts
4:10  Warning: 'logger' is defined but never used.  @typescript-eslint/no-unused-vars
35:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/dashboard/events/new/page.tsx
2:10  Warning: 'redirect' is defined but never used.  @typescript-eslint/no-unused-vars
17:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
36:7  Warning: 'userId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
41:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
45:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
53:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
55:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
62:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
66:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
77:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/dashboard/events/page.tsx
3:10  Warning: 'redirect' is defined but never used.  @typescript-eslint/no-unused-vars
31:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
60:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/dashboard/events/[id]/not-found.tsx
10:22  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
10:43  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
10:62  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/dashboard/events/[id]/page.tsx
8:52  Warning: 'Settings' is defined but never used.  @typescript-eslint/no-unused-vars
43:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
47:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
53:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
57:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
61:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
65:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
76:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
83:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/dashboard/organizations/apply/actions.ts
89:7  Warning: 'applications' is assigned a value but never used.  @typescript-eslint/no-unused-vars
343:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
383:41  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
498:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
555:67  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/organizations/apply/OrganizerApplicationForm.tsx
108:42  Warning: 'name' is defined but never used.  @typescript-eslint/no-unused-vars
138:6  Warning: React Hook useEffect has a missing dependency: 'getValues'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
196:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
204:32  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
597:45  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/dashboard/organizations/ApplyForOrganizerButton.tsx
30:59  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/dashboard/organizations/page.tsx
12:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
21:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
24:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
40:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
49:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
62:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
65:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
72:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
75:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
78:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
112:29  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/dashboard/payments/page.tsx
16:9  Warning: 'userId' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/dashboard/profile/page.tsx
2:10  Warning: 'redirectToSignIn' is defined but never used.  @typescript-eslint/no-unused-vars
2:28  Warning: 'redirectToDashboard' is defined but never used.  @typescript-eslint/no-unused-vars
9:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
18:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
21:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
37:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
46:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
59:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
62:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
66:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
83:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
112:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
161:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/dashboard/profile/profile-client.tsx
26:7  Warning: 'navigationItems' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/dashboard/tickets/page.tsx
15:9  Warning: 'userId' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/events/[slug]/page.tsx
13:10  Warning: 'cn' is defined but never used.  @typescript-eslint/no-unused-vars
24:31  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
24:48  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
35:16  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
42:21  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
44:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
111:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
129:56  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
135:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
138:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
255:52  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/events/[slug]/register/page.tsx
70:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
116:47  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/events/[slug]/register/[categoryId]/page.tsx
40:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
42:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
44:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
153:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
402:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/migrations/data-export-tables.ts
10:15  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
17:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
21:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/app/page.tsx
8:10  Warning: 'TypeAnimation' is defined but never used.  @typescript-eslint/no-unused-vars
12:10  Warning: 'Card' is defined but never used.  @typescript-eslint/no-unused-vars
12:16  Warning: 'CardContent' is defined but never used.  @typescript-eslint/no-unused-vars
13:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
18:40  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
76:7  Warning: 'heroTextVariant' is assigned a value but never used.  @typescript-eslint/no-unused-vars
157:6  Warning: React Hook useEffect has a missing dependency: 'autoPosition'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
163:6  Warning: React Hook useEffect has a missing dependency: 'autoPosition'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
166:9  Warning: 'beamX' is assigned a value but never used.  @typescript-eslint/no-unused-vars
172:54  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/profile/profile-form.tsx
14:9  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
54:19  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
105:19  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/reset-password/reset-password-form.tsx
4:10  Warning: 'createClient' is defined but never used.  @typescript-eslint/no-unused-vars
44:19  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/update-password/update-password-form.tsx
110:19  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/auth/GoogleSignInButton.tsx
46:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
59:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/components/auth/SignInForm.tsx
14:10  Warning: 'getBaseUrl' is defined but never used.  @typescript-eslint/no-unused-vars
22:9  Warning: 'router' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/header.tsx
9:35  Warning: 'setIsOpen' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/events/event-calendar-page.tsx
19:10  Warning: 'selectedEvent' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/events/event-calendar.tsx
30:3  Warning: 'viewMode' is assigned a value but never used.  @typescript-eslint/no-unused-vars
123:9  Warning: 'renderDayContents' is assigned a value but never used.  @typescript-eslint/no-unused-vars
123:41  Warning: '_modifiers' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/events/event-wizard/completion-rate.tsx
8:51  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/events/event-wizard/EventWizardWrapper.tsx
18:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
26:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
35:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
44:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
45:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
61:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
65:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
76:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
89:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
106:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
111:15  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
127:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
152:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
162:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
164:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
167:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
172:17  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
195:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
197:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
200:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
215:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
236:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
247:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
270:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
297:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/components/events/event-wizard/preview-gallery.tsx
5:10  Warning: 'ChevronLeft' is defined but never used.  @typescript-eslint/no-unused-vars
5:23  Warning: 'ChevronRight' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/events/event-wizard/steps/basic-details-step.tsx
14:10  Warning: 'cn' is defined but never used.  @typescript-eslint/no-unused-vars
81:16  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
122:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
145:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
150:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
208:6  Warning: React Hook useEffect has missing dependencies: 'formData.endDate', 'formData.startDate', and 'updateFormData'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/events/event-wizard/steps/categories-step.tsx
12:10  Warning: 'PlusCircle' is defined but never used.  @typescript-eslint/no-unused-vars
12:22  Warning: 'X' is defined but never used.  @typescript-eslint/no-unused-vars
31:3  Warning: 'runningEventCategoryPropertiesSchema' is defined but never used.  @typescript-eslint/no-unused-vars
32:3  Warning: 'conferenceEventCategoryPropertiesSchema' is defined but never used.  @typescript-eslint/no-unused-vars
37:3  Warning: 'EVENT_TYPE_TEMPLATES' is defined but never used.  @typescript-eslint/no-unused-vars
75:10  Warning: 'allowCategorySpecificClosingDates' is assigned a value but never used.  @typescript-eslint/no-unused-vars
75:45  Warning: 'setAllowCategorySpecificClosingDates' is assigned a value but never used.  @typescript-eslint/no-unused-vars
229:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
239:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
250:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
256:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
301:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
305:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
445:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
538:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
546:45  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/events/event-wizard/steps/event-type-step.tsx
4:10  Warning: 'Card' is defined but never used.  @typescript-eslint/no-unused-vars
4:16  Warning: 'CardContent' is defined but never used.  @typescript-eslint/no-unused-vars
4:29  Warning: 'CardDescription' is defined but never used.  @typescript-eslint/no-unused-vars
4:46  Warning: 'CardFooter' is defined but never used.  @typescript-eslint/no-unused-vars
4:58  Warning: 'CardHeader' is defined but never used.  @typescript-eslint/no-unused-vars
4:70  Warning: 'CardTitle' is defined but never used.  @typescript-eslint/no-unused-vars
7:21  Warning: 'WizardStep' is defined but never used.  @typescript-eslint/no-unused-vars
11:37  Warning: 'nextStep' is assigned a value but never used.  @typescript-eslint/no-unused-vars
84:29  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/events/event-wizard/steps/fields-step.tsx
80:37  Warning: 'nextStep' is assigned a value but never used.  @typescript-eslint/no-unused-vars
229:9  Warning: 'handleNumberInputChange' is assigned a value but never used.  @typescript-eslint/no-unused-vars
484:9  Warning: 'getFieldOptions' is assigned a value but never used.  @typescript-eslint/no-unused-vars
570:45  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
585:47  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
597:47  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
613:47  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
625:47  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
638:47  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
693:45  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/events/event-wizard/steps/image-upload-step.tsx
57:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
59:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
61:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
71:6  Warning: React Hook useEffect has a missing dependency: 'supabase.auth'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
218:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
233:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
248:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
263:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
307:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
309:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
318:11  Warning: 'successCount' is assigned a value but never used.  @typescript-eslint/no-unused-vars
348:15  Warning: 'fileName' is assigned a value but never used.  @typescript-eslint/no-unused-vars
350:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
378:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
424:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
434:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
475:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
485:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/components/events/event-wizard/steps/preview-step.tsx
12:13  Warning: 'Tag' is defined but never used.  @typescript-eslint/no-unused-vars
17:8  Warning: 'Link' is defined but never used.  @typescript-eslint/no-unused-vars
22:21  Warning: 'updateFormData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
22:37  Warning: 'submitForm' is assigned a value but never used.  @typescript-eslint/no-unused-vars
91:9  Warning: 'formatPrice' is assigned a value but never used.  @typescript-eslint/no-unused-vars
231:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
232:45  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
356:55  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/events/event-wizard/steps/tshirt-options-step.tsx
194:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
203:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
227:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
267:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
297:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/components/events/image-focus-adjuster.tsx
27:10  Warning: 'imageLoaded' is assigned a value but never used.  @typescript-eslint/no-unused-vars
38:28  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/EventSearch.tsx
42:9  Warning: 'getCategoryDisplay' is assigned a value but never used.  @typescript-eslint/no-unused-vars
47:9  Warning: 'getStateDisplay' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/ImageCarousel.tsx
113:6  Warning: React Hook useEffect has a missing dependency: 'nextImage'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/layout/auth-buttons.tsx
11:9  Warning: 'router' is assigned a value but never used.  @typescript-eslint/no-unused-vars
15:10  Warning: 'loadingTimeout' is assigned a value but never used.  @typescript-eslint/no-unused-vars
45:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
54:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
63:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/components/layout/footer.tsx
8:11  Warning: 'theme' is assigned a value but never used.  @typescript-eslint/no-unused-vars
8:18  Warning: 'setTheme' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/layout/header.tsx
6:10  Warning: 'cn' is defined but never used.  @typescript-eslint/no-unused-vars
11:10  Warning: 'Button' is defined but never used.  @typescript-eslint/no-unused-vars
12:10  Warning: 'UserAvatarMenu' is defined but never used.  @typescript-eslint/no-unused-vars
32:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
41:9  Warning: 'toggleSidebar' is assigned a value but never used.  @typescript-eslint/no-unused-vars
51:9  Warning: 'toggleMobileMenu' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/layout/mobile-menu.tsx
18:29  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
20:9  Warning: 'router' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/layout/user-avatar-menu.tsx
7:10  Warning: 'getUserAvatar' is defined but never used.  @typescript-eslint/no-unused-vars
48:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
61:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
70:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/components/profile/activity-history.tsx
31:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
32:30  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
45:10  Warning: 'totalCount' is assigned a value but never used.  @typescript-eslint/no-unused-vars
53:6  Warning: React Hook useEffect has a missing dependency: 'fetchActivityData'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
106:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
115:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/profile/contacts-list.tsx
6:29  Warning: 'CardFooter' is defined but never used.  @typescript-eslint/no-unused-vars
6:41  Warning: 'CardHeader' is defined but never used.  @typescript-eslint/no-unused-vars
11:10  Warning: 'Badge' is defined but never used.  @typescript-eslint/no-unused-vars
16:30  Warning: 'ContactFormData' is defined but never used.  @typescript-eslint/no-unused-vars
27:3  Warning: 'DialogTrigger' is defined but never used.  @typescript-eslint/no-unused-vars
32:3  Warning: 'FormDescription' is defined but never used.  @typescript-eslint/no-unused-vars
77:12  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
159:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
162:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
164:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
178:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
302:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
307:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
316:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
318:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
338:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
340:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
376:15  Warning: 'success' is assigned a value but never used.  @typescript-eslint/no-unused-vars
721:34  Warning: 'field' is defined but never used.  @typescript-eslint/no-unused-vars
744:34  Warning: 'field' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/profile/data-export.tsx
5:46  Warning: 'CardFooter' is defined but never used.  @typescript-eslint/no-unused-vars
38:3  Warning: 'TableCaption' is defined but never used.  @typescript-eslint/no-unused-vars
73:30  Warning: 'userId' is defined but never used.  @typescript-eslint/no-unused-vars
181:40  Warning: 'exportId' is assigned a value but never used.  @typescript-eslint/no-unused-vars
224:51  Warning: 'format' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/profile/edit-form.tsx
132:10  Warning: 'authUser' is assigned a value but never used.  @typescript-eslint/no-unused-vars
132:44  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
173:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
207:16  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
254:9  Warning: 'addCategory' is assigned a value but never used.  @typescript-eslint/no-unused-vars
269:9  Warning: 'removeCategory' is assigned a value but never used.  @typescript-eslint/no-unused-vars
387:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/profile/personal-info.tsx
159:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
221:6  Warning: React Hook useEffect has a missing dependency: 'fetchProfile'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/profile/privacy-settings.tsx
10:3  Warning: 'getUserConsentByType' is defined but never used.  @typescript-eslint/no-unused-vars
26:10  Warning: 'Info' is defined but never used.  @typescript-eslint/no-unused-vars
26:24  Warning: 'CheckCircle2' is defined but never used.  @typescript-eslint/no-unused-vars
118:6  Warning: React Hook useEffect has a missing dependency: 'getConsentStatuses'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
121:41  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
149:79  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/profile/profile-completion.tsx
31:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
89:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
104:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/components/theme/theme-toggle.tsx
16:21  Warning: 'theme' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/ui/accessible-dropdown-menu.tsx
5:10  Warning: 'Check' is defined but never used.  @typescript-eslint/no-unused-vars
5:17  Warning: 'ChevronRight' is defined but never used.  @typescript-eslint/no-unused-vars
5:31  Warning: 'Circle' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ui/accessible-dropdown.tsx
4:13  Warning: 'DropdownMenuPrimitive' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ui/calendar.tsx
5:10  Warning: 'format' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ui/searchable-select.tsx
12:3  Warning: 'CommandItem' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ui/simple-dropdown.tsx
18:3  Warning: 'align' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/ui/use-toast.tsx
49:24  Warning: The ref value 'toastTimeoutsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'toastTimeoutsRef.current' to a variable inside the effect, and use that variable in the cleanup function.  react-hooks/exhaustive-deps
73:6  Warning: React Hook useCallback has a missing dependency: 'dismiss'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/contexts/auth-context.tsx
82:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
96:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
107:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
112:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
129:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
135:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
147:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
152:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
168:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
174:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
218:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
221:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
251:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
263:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
266:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
273:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
283:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
286:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
292:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
305:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
308:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
314:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
318:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
322:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
323:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
324:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
340:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
350:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
367:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
426:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
433:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
445:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
453:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
468:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
486:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
496:15  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
498:15  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
516:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
532:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
548:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
552:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
562:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
577:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
579:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
599:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
618:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
625:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
634:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
648:6  Warning: React Hook useEffect has missing dependencies: 'isSignedIn', 'loading', 'refreshSession', 'supabase.auth', and 'user'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/contexts/cookie-consent-context.tsx
8:3  Warning: 'saveConsent' is defined but never used.  @typescript-eslint/no-unused-vars

./src/data/countries.ts
56:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
112:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/hooks/use-auth-hooks.ts
3:33  Warning: 'useEffect' is defined but never used.  @typescript-eslint/no-unused-vars
28:15  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
63:19  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
70:6  Warning: React Hook useCallback has an unnecessary dependency: 'router'. Either exclude it or remove the dependency array.  react-hooks/exhaustive-deps
109:15  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
123:19  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
152:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
171:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
179:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
196:15  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
205:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
209:19  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
257:19  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/hooks/use-events.ts
69:66  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
98:66  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/hooks/use-toast.ts
21:7  Warning: 'actionTypes' is assigned a value but only used as a type.  @typescript-eslint/no-unused-vars

./src/lib/api/events.ts
28:78  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
39:74  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
61:46  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
82:58  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/auth.ts
4:10  Warning: 'adminSupabase' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/cookie-utils.ts
73:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
87:9  Warning: 'cookieOptions' is assigned a value but never used.  @typescript-eslint/no-unused-vars
93:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/lib/db/base-repository.ts
26:11  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
38:31  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
39:60  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
44:54  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
81:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
112:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
130:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
143:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
156:29  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
345:17  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
375:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/lib/db/schema-manager.ts
1:10  Warning: 'createClient' is defined but never used.  @typescript-eslint/no-unused-vars
23:15  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars
37:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
79:21  Warning: 'tableData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
203:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/db/seed-data.ts
16:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/lib/env-config.ts
72:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
75:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
77:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
79:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
81:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
84:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/lib/logger.ts
5:6  Warning: 'LogLevel' is defined but never used.  @typescript-eslint/no-unused-vars
19:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
24:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/lib/repositories/base-repository.ts
2:10  Warning: 'PostgrestSingleResponse' is defined but never used.  @typescript-eslint/no-unused-vars
7:54  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
34:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
54:36  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
59:58  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
89:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
90:25  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
115:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
116:25  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
142:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/repositories/contacts-repository.ts
81:27  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/repositories/event-repository.ts
2:10  Warning: 'createClient' is defined but never used.  @typescript-eslint/no-unused-vars
55:27  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
75:43  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
127:27  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
153:27  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
176:27  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
207:41  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
217:27  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
242:27  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
274:27  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/roles.ts
3:20  Warning: 'RoleMetadata' is defined but never used.  @typescript-eslint/no-unused-vars
90:37  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
103:36  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/services/event-service.ts
46:15  Warning: 'ticketTypes' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/lib/supabase/actions.ts
1:10  Warning: 'cache' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/supabase/auth.ts
483:11  Warning: 'currentUrl' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/lib/supabase/client.ts
31:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
33:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/lib/supabase/extended-types.ts
8:24  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
63:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
73:54  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
74:11  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
88:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
89:17  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/supabase/pages-client.ts
38:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
40:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/lib/supabase/server.ts
4:10  Warning: 'CookieOptions' is defined but never used.  @typescript-eslint/no-unused-vars
37:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
52:15  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
112:13  Warning: 'data' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/lib/supabase/validate-env.ts
49:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
98:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
122:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/lib/utils/date-utils.ts
146:12  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars

./src/middleware.ts
6:63  Warning: 'getBaseUrl' is defined but never used.  @typescript-eslint/no-unused-vars
58:13  Warning: 'name' is defined but never used.  @typescript-eslint/no-unused-vars
58:27  Warning: 'value' is defined but never used.  @typescript-eslint/no-unused-vars
58:42  Warning: 'options' is defined but never used.  @typescript-eslint/no-unused-vars
61:16  Warning: 'name' is defined but never used.  @typescript-eslint/no-unused-vars
61:30  Warning: 'options' is defined but never used.  @typescript-eslint/no-unused-vars
111:9  Warning: 'eventRegistrationRoutes' is assigned a value but never used.  @typescript-eslint/no-unused-vars
239:46  Warning: 'authIdError' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/repositories/event-category-repository.ts
9:3  Warning: 'eventCategorySchema' is defined but never used.  @typescript-eslint/no-unused-vars
58:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
83:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
101:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
133:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
145:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
223:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
240:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
273:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
277:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
288:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
292:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
301:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
316:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
365:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
374:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
378:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
404:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
488:20  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/repositories/event-repository.ts
4:10  Warning: 'UserRepository' is defined but never used.  @typescript-eslint/no-unused-vars
31:7  Warning: 'ImageDBSchema' is assigned a value but never used.  @typescript-eslint/no-unused-vars
415:46  Warning: 'directUserError' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/repositories/event-type-repository.ts
4:10  Warning: 'createClient' is defined but never used.  @typescript-eslint/no-unused-vars
5:10  Warning: 'asAny' is defined but never used.  @typescript-eslint/no-unused-vars

./src/scripts/migrate-event-user-ids.ts
19:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
33:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
46:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
50:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
51:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
52:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
65:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
69:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
75:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/scripts/migrate.ts
34:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
44:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
76:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
159:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
163:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
177:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
183:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
187:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
196:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
197:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
244:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
258:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
259:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
260:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
261:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
262:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
276:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
288:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
291:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
294:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
297:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
303:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
311:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
314:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
323:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/utils/env-helpers.ts
87:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
88:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
89:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
90:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
91:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/utils/imageHandling.ts
97:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
119:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
126:11  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
135:15  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
153:69  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
158:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
164:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
183:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
203:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
219:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
231:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/utils/supabase/enhanced-middleware.ts
3:10  Warning: 'copyCookiesFromRequest' is defined but never used.  @typescript-eslint/no-unused-vars
59:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
74:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
123:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/utils/supabase/middleware.ts
1:35  Warning: 'CookieOptions' is defined but never used.  @typescript-eslint/no-unused-vars
43:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
58:13  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
62:37  Warning: 'options' is assigned a value but never used.  @typescript-eslint/no-unused-vars
114:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

./src/utils/url-utilities.ts
26:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
37:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
40:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
49:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
59:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
62:9  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
67:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
102:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
107:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
115:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
122:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
186:3  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
198:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
205:7  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console
219:5  Warning: Unexpected console statement. Only these console methods are allowed: warn, error.  no-console

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
