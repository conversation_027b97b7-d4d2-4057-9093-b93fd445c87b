import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

// Force dynamic rendering to ensure the route is not cached
export const dynamic = 'force-dynamic'

/**
 * API endpoint to reset the auth state
 * This is useful for recovering from auth errors like bad_oauth_state
 */
export async function POST(request: NextRequest) {
  return handleResetState(request);
}

/**
 * Support GET requests for the reset-state endpoint
 * This allows the endpoint to be called from links and redirects
 */
export async function GET(request: NextRequest) {
  return handleResetState(request);
}

/**
 * Shared handler for both GET and POST requests
 */
async function handleResetState(request: NextRequest) {
  try {
    // Get all cookies - must await cookies() to avoid sync-dynamic-apis error
    const cookieStore = await cookies()
    const allCookies = await cookieStore.getAll()

    // Specific Supabase and next-auth cookie names to ensure we clear
    const specificCookieNames = [
      // Supabase cookies
      'sb-access-token',
      'sb-refresh-token',
      'supabase-auth-token',
      '__supabase_session',
      'sb-provider-token',
      'sb-auth-token',
      'sb-eibzxudhnojsdxksgowo-auth-token',
      'sb-eibzxudhnojsdxksgowo-auth-token.0',
      'sb-eibzxudhnojsdxksgowo-auth-token.1',
      'sb-eibzxudhnojsdxksgowo-auth-token-code-verifier',
      '__refresh_J27R4vOE',

      // Next-auth cookies (removing these to ensure clean state)
      'next-auth.session-token',
      'next-auth.callback-url',
      'next-auth.csrf-token',
      '__Secure-next-auth.callback-url',
      '__Secure-next-auth.session-token',
      '__Secure-next-auth.csrf-token',
      '__Host-next-auth.csrf-token'
    ]

    // Log the cookies we're going to delete
    const supabaseCookies = allCookies.filter(cookie =>
      cookie.name.startsWith('sb-') ||
      cookie.name.includes('supabase') ||
      specificCookieNames.includes(cookie.name)
    )

    console.log('Clearing auth state cookies:', supabaseCookies.map(c => c.name))

    // Create a response
    const response = NextResponse.json({
      success: true,
      message: 'Auth state reset successful',
      clearedCookies: supabaseCookies.map(c => c.name)
    })

    // Delete all Supabase-related cookies
    for (const cookie of supabaseCookies) {
      // Delete from the cookie store - must await to avoid sync-dynamic-apis error
      await cookieStore.delete(cookie.name)

      // Also set an expired cookie in the response
      response.cookies.set({
        name: cookie.name,
        value: '',
        expires: new Date(0),
        path: '/',
      })
    }

    // Set a special cookie to indicate that the state has been reset
    // This will be used by the AuthButtons component to show the sign-in buttons
    // instead of the loading state
    response.cookies.set({
      name: 'sb-reset-complete',
      value: 'true',
      maxAge: 60, // Only keep this cookie for 60 seconds
      path: '/',
    })

    // Check if we should redirect after clearing cookies
    const url = new URL(request.url)
    const redirectTo = url.searchParams.get('redirect_to')

    if (redirectTo) {
      // If a redirect URL is provided, redirect there
      try {
        const redirectUrl = decodeURIComponent(redirectTo)
        return NextResponse.redirect(new URL(redirectUrl, request.url))
      } catch (redirectError) {
        console.error('Error redirecting after reset:', redirectError)
        // Fall back to returning the JSON response
      }
    }

    return response
  } catch (error) {
    console.error('Error resetting auth state:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to reset auth state' },
      { status: 500 }
    )
  }
}
