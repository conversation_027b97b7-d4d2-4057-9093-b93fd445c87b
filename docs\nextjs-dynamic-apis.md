# Next.js 15 Dynamic APIs

In Next.js 15, certain APIs that were previously synchronous are now asynchronous. This document explains how to properly use these dynamic APIs in your application.

## What Are Dynamic APIs?

Dynamic APIs in Next.js are APIs that opt into dynamic rendering. These include:

1. The `params` and `searchParams` props that get provided to:
   - Pages
   - Layouts
   - Metadata APIs
   - Route handlers

2. Functions from `next/headers`:
   - `cookies()`
   - `draftMode()`
   - `headers()`

## The Issue

In Next.js 15, these APIs have been made asynchronous. This means you can't directly access their properties without first awaiting them.

For example, this code will cause a warning:

```typescript
// This will cause a warning in Next.js 15
export default function Page({ params, searchParams }) {
  // Direct access of params.id or searchParams.query
  return <p>ID: {params.id}, Query: {searchParams.query}</p>
}
```

## How to Fix It

### In Server Components

In server components (which are async by default), you need to await the dynamic API before accessing its properties:

```typescript
// Correct usage in a Server Component
export default async function Page({ params, searchParams }) {
  // Await the params and searchParams before accessing their properties
  const { id } = await params;
  const { query } = await searchParams;
  
  return <p>ID: {id}, Query: {query}</p>
}
```

### In Client Components

In client components (which are synchronous), you need to use `React.use()` to unwrap the Promise:

```typescript
'use client'
import * as React from 'react'

// Correct usage in a Client Component
export default function Page({ params, searchParams }) {
  // Use React.use() to unwrap the Promise
  const { id } = React.use(params);
  const { query } = React.use(searchParams);
  
  return <p>ID: {id}, Query: {query}</p>
}
```

## Examples in Our Codebase

### Dashboard Page

```typescript
export default async function DashboardPage({
  searchParams
}: {
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  // We need to await searchParams before accessing its properties
  const params = await searchParams;
  const noRedirect = params?.no_redirect === 'true';
  
  // Rest of the component...
}
```

### Sign-In Page

```typescript
export default async function SignInPage({
  searchParams
}: {
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  // We need to await searchParams before accessing its properties
  const params = await searchParams;
  const noRedirect = params?.no_redirect === 'true';
  
  // Rest of the component...
}
```

### Event Detail Page

```typescript
export default async function EventDetail({ params }: EventDetailProps) {
  // Await params to fix the "params should be awaited" error
  const { slug } = await params;
  
  // Rest of the component...
}
```

## Best Practices

1. **Delay Unwrapping**: You can delay unwrapping the Promise (either with `await` or `React.use()`) until you actually need to consume the value. This will allow Next.js to statically render more of your page.

2. **Type Annotations**: Make sure your type annotations reflect the asynchronous nature of these APIs.

3. **Error Handling**: Use try/catch blocks when awaiting these APIs to handle potential errors.

4. **Codemod**: You can use the `next-async-request-api` codemod to automatically fix many of these issues:

   ```bash
   npx @next/codemod@canary next-async-request-api .
   ```

## Further Reading

- [Next.js Documentation: Dynamic APIs are Asynchronous](https://nextjs.org/docs/messages/sync-dynamic-apis)
- [Next.js 15 Upgrade Guide](https://nextjs.org/docs/app/guides/upgrading/version-15)
