---
description: This is the rules to follow while you the coding
globs: 
alwaysApply: true
---
# Cursor Rules

## Project Patterns
- Memory Bank structure initialized and must be maintained
- All significant changes must be documented in appropriate Memory Bank files
- Follow edge-first development practices
- Implement strict tenant data isolation
- Use Zod for all form validations
- Maintain clear type definitions

## User Preferences
- OS: darwin 19.6.0
- Shell: /bin/zsh
- Package Manager: pnpm
- Editor: VS Code (recommended)

## Implementation Guidelines
- Use Next.js App Router patterns
- Follow Tailwind CSS class conventions
- Implement proper error handling
- Maintain tenant context throughout
- Use Drizzle ORM for database operations
- Follow edge-first deployment practices

## Known Challenges
- Tenant data isolation requirements
- Dynamic form validation complexity
- Concurrent registration handling
- Real-time payment processing
- Data export performance

## Tool Usage
- Next.js for frontend and API
- Supabase for database
- Clerk for authentication
- Z<PERSON> for validation
- Zustand for state management
- Tailwind + Shadcn UI for styling

## Notes
- This file tracks project-specific patterns and preferences
- Update as new patterns and preferences are discovered
- Focus on maintaining code quality and consistency
- Regular review of implementation patterns 