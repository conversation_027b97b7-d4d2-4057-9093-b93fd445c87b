#!/usr/bin/env ts-node

/**
 * Admin script to fix schema cache without requiring authentication
 * Run with: npx ts-node scripts/fix-schema-cache-admin.ts
 */

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables must be set.');
  process.exit(1);
}

// Create Supabase client with service role key (admin privileges)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function fixSchemaCache() {
  console.log('Starting schema cache fix process...');
  
  // Step 1: Alter column types to force schema refresh
  const queries = [
    'ALTER TABLE saved_contacts ALTER COLUMN first_name TYPE TEXT;',
    'ALTER TABLE saved_contacts ALTER COLUMN last_name TYPE TEXT;',
    'ALTER TABLE saved_contacts ALTER COLUMN relationship TYPE TEXT;',
    'ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_name TYPE TEXT;',
    'ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_no TYPE TEXT;',
    'ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_relationship TYPE TEXT;',
  ];
  
  for (const query of queries) {
    console.log(`Executing: ${query}`);
    const { error } = await supabase.rpc('pg_catalog.pg_execute', { query });
    
    if (error) {
      console.error(`Error executing query: ${error.message}`);
    } else {
      console.log('✓ Success');
    }
  }
  
  // Step 2: Force schema cache reload
  console.log('Forcing schema cache reload...');
  const { error: notifyError } = await supabase.rpc('pg_notify', {
    channel: 'pgrst',
    payload: 'reload schema'
  });
  
  if (notifyError) {
    console.error(`Error notifying for schema reload: ${notifyError.message}`);
  } else {
    console.log('✓ Schema reload notification sent');
  }
  
  // Step 3: Test the schema
  console.log('Testing schema...');
  const { data: testData, error: testError } = await supabase
    .from('saved_contacts')
    .select('first_name')
    .limit(1);
  
  if (testError) {
    console.error(`Schema test failed: ${testError.message}`);
    console.log('The schema cache may not be fully updated yet.');
    console.log('You may need to restart the PostgREST service from the Supabase dashboard.');
  } else {
    console.log('✓ Schema test successful! The fix has been applied.');
  }
  
  console.log('\nSchema cache fix process completed.');
}

// Run the fix
fixSchemaCache()
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  }); 