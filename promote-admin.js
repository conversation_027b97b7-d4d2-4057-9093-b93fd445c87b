// Script to promote a user to admin role
const { createClient } = require('@supabase/supabase-js');

// Hardcode the Supabase URL and service role key from .env.local
const supabaseUrl = 'https://eibzxudhnojsdxksgowo.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVpYnp4dWRobm9qc2R4a3Nnb3dvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDI0MTgyNywiZXhwIjoyMDU5ODE3ODI3fQ.CROQAHcw6gmoD3qpB62tuibjTMbHfU9l_5tprY--C2k';

// Create Supabase admin client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Email of the user to promote
const userEmail = '<EMAIL>'; // Replace with your email

async function promoteToAdmin() {
  try {
    console.log(`Promoting user ${userEmail} to admin role...`);

    // First, get the user from the users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, auth_user_id, role')
      .eq('email', userEmail)
      .single();

    if (userError) {
      console.error('Error fetching user:', userError.message);
      return;
    }

    if (!userData) {
      console.error('User not found');
      return;
    }

    console.log('Current user data:', userData);

    // Update the user's role in the users table
    const { data: updateData, error: updateError } = await supabase
      .from('users')
      .update({ role: 'admin' })
      .eq('id', userData.id)
      .select();

    if (updateError) {
      console.error('Error updating user role:', updateError.message);
      return;
    }

    console.log('User role updated successfully:', updateData);

    // If the user has an auth_user_id, update their metadata in Auth
    if (userData.auth_user_id) {
      const { data: authUpdate, error: authError } = await supabase.auth.admin.updateUserById(
        userData.auth_user_id,
        { user_metadata: { role: 'admin' } }
      );

      if (authError) {
        console.error('Error updating auth user metadata:', authError.message);
        return;
      }

      console.log('Auth user metadata updated successfully');
    }

    console.log('User has been promoted to admin role');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

promoteToAdmin();
