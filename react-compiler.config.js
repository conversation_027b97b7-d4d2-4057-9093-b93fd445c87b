/**
 * React Compiler configuration
 * @see https://react.dev/learn/react-compiler
 */
module.exports = {
  // Enable compiler in production only initially
  enabled: process.env.NODE_ENV === 'production',
  
  // Verbose logging during build
  verbose: true,
  
  // Paths to include/exclude
  include: ['src/**/*.{js,jsx,ts,tsx}'],
  exclude: [
    '**/*.test.{js,jsx,ts,tsx}', 
    '**/node_modules/**',
    '.next/**',
    'out/**'
  ],
  
  // Compiler options
  options: {
    // Preserve original component names in production
    preserveNames: true,
    
    // Optimize component props
    optimizeProps: true,
    
    // Optimize component state
    optimizeState: true,
  }
};
