# Next-Auth Removal Documentation

This document outlines the changes made to remove Next-Auth from the Fuiyoo application and ensure that only Supabase Auth is used for authentication.

## Changes Made

1. **Removed Next-Auth Dependency**
   - Removed `next-auth` from package.json

2. **Updated <PERSON><PERSON> Handling**
   - Added next-auth cookie names to the reset-state API
   - Added next-auth cookie names to the clear-session API
   - Ensured all next-auth cookies are properly cleared during sign-out

3. **Updated Middleware**
   - Added logging for next-auth cookies in the middleware
   - Added warning when next-auth cookies are present but Supabase session is not

## Why Remove Next-Auth?

Having two authentication systems (Next-Auth and Supabase Auth) in the same application can cause several issues:

1. **Conflicting Sessions**: The two systems maintain separate session states, which can get out of sync.
2. **Cookie Conflicts**: Both systems use cookies to store session information, which can lead to conflicts.
3. **Redirect Loops**: Authentication redirects can get confused between the two systems.
4. **Maintenance Overhead**: Maintaining two authentication systems requires more code and increases complexity.

## Next-Auth Cookie Names

The following next-auth cookies are now explicitly cleared during sign-out and reset operations:

```
next-auth.session-token
next-auth.callback-url
next-auth.csrf-token
__Secure-next-auth.callback-url
__Secure-next-auth.session-token
__Secure-next-auth.csrf-token
__Host-next-auth.csrf-token
```

## Debugging Authentication Issues

If you encounter authentication issues, you can:

1. Check the browser console for warnings about next-auth cookies
2. Use the `/api/auth/session-check` endpoint to view current session state
3. Use the `/api/auth/reset-state` endpoint to clear all authentication cookies
4. Use the `/api/auth/clear-session` endpoint to clear session cookies

## Zustand and State Management

Zustand is a state management library that can be used as an alternative to the React Context API. In our application:

- We currently use React Context (AuthContext) for authentication state management
- Zustand is available as a dependency but not actively used for auth state
- Zustand could be used in the future to simplify state management

## Best Practices for Authentication

1. **Single Auth Provider**: Use only one authentication provider (Supabase Auth)
2. **Server-Side Validation**: Always validate authentication on the server side
3. **Secure Cookies**: Use secure, HTTP-only cookies for session storage
4. **Refresh Tokens**: Implement proper token refresh mechanisms
5. **Error Handling**: Handle authentication errors gracefully

## Supabase Auth Flow

The Supabase Auth flow in our application works as follows:

1. **Sign In/Sign Up**: User signs in or signs up using Supabase Auth
2. **Callback**: Supabase redirects to `/auth/callback` with a code
3. **Session Exchange**: The callback route exchanges the code for a session
4. **Cookie Storage**: Session tokens are stored in cookies
5. **Middleware**: Middleware validates the session on each request
6. **Protected Routes**: Unauthenticated users are redirected to sign-in

## References

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Next.js Middleware Documentation](https://nextjs.org/docs/app/building-your-application/routing/middleware)
- [Supabase SSR Package](https://supabase.com/docs/guides/auth/server-side/nextjs)
