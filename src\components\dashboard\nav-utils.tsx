import {
  LayoutDashboard,
  Calendar,
  Users,
  Ticket,
  CreditCard,
  UserCircle
} from 'lucide-react';

export interface NavItem {
  href: string;
  label: string;
  iconName: string;
}

export function renderNavIcon(iconName: string) {
  switch (iconName) {
    case 'LayoutDashboard':
      return <LayoutDashboard className="mr-3 h-5 w-5 text-[hsl(var(--primary))]" />;
    case 'Calendar':
      return <Calendar className="mr-3 h-5 w-5 text-[hsl(var(--primary))]" />;
    case 'Users':
      return <Users className="mr-3 h-5 w-5 text-[hsl(var(--primary))]" />;
    case 'Ticket':
      return <Ticket className="mr-3 h-5 w-5 text-[hsl(var(--primary))]" />;
    case 'CreditCard':
      return <CreditCard className="mr-3 h-5 w-5 text-[hsl(var(--primary))]" />;
    case 'UserCircle':
      return <UserCircle className="mr-3 h-5 w-5 text-[hsl(var(--primary))]" />;
    default:
      return null;
  }
}