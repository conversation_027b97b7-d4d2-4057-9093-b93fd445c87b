# System Patterns

## Architecture Overview

The application follows a modern Next.js application architecture with the following key patterns:

1. **App Router Structure**: Using Next.js App Router for routing and layouts
2. **Server/Client Component Separation**: Clear distinction between server and client components
3. **Server Actions**: Using server actions for data mutations
4. **Authentication with Supabase Auth**: Authentication and RBAC via Supabase Auth
5. **Database with Supabase**: PostgreSQL database via Supabase
6. **UI Component Library**: Custom components built on Radix UI primitives with Tailwind CSS 4
7. **Mask Utilities**: Using Tailwind 4 mask utilities for UI enhancements
8. **HSL Color Notation**: Using explicit HSL notation for better theming support

## Component Architecture

### UI Component Patterns

1. **Primitive Components**
   - Built on Radix UI primitives for accessibility
   - Enhanced with Tailwind CSS for styling
   - Located in `components/ui/*`
   - Examples: Button, Dialog, Input, Card

2. **Feature Components**
   - Composed of primitive components
   - Specific to application features
   - Located in `components/{feature}/*`
   - Examples: ContactsList, PrivacySettings, ActivityHistory

3. **Layout Components**
   - Define the overall page structure
   - Located in `app/**/layout.tsx`
   - Handle common UI elements (headers, footers, navigation)

4. **Page Components**
   - Top-level components for each route
   - Located in `app/**/page.tsx`
   - Primarily server components pulling in client components as needed

### UI Utility Patterns

1. **Class Name Management**
   - Using `cn()` utility function in `lib/utils.ts`
   - Combines clsx for conditional classes and tailwind-merge for class merging
   - Handles class conflicts and conditional styling

   ```typescript
   // Example usage
   import { cn } from "@/lib/utils";

   function Button({ className, ...props }) {
     return (
       <button
         className={cn(
           "bg-primary text-white px-4 py-2 rounded",
           props.disabled && "opacity-50 cursor-not-allowed",
           className
         )}
         {...props}
       />
     );
   }
   ```

2. **Component Props Pattern**
   - Using TypeScript interfaces for component props
   - Extending HTML element props with React.ComponentPropsWithoutRef
   - Component variants using discriminated unions

## Authentication Patterns

1. **Auth Protection**
   - Using Supabase Auth middleware for route protection
   - Protected routes defined in middleware.ts
   - Public routes explicitly specified

2. **Server-Side Auth**
   - Using Supabase Auth helpers for server-side authentication
   - Checking authentication in server components and actions
   - Fetching user data and permissions

3. **Client-Side Auth**
   - Using Supabase Auth client for authentication
   - Conditional UI based on authentication state
   - Redirects for unauthenticated users

4. **OAuth Integration**
   - Google OAuth authentication
   - Handling OAuth callbacks
   - User profile synchronization

## Data Fetching Patterns

1. **Server Component Data Fetching**
   - Direct database access in server components
   - Passing data down to client components as props
   - Avoiding client-side fetching when possible

2. **Server Actions for Mutations**
   - Using 'use server' actions for data mutations
   - Form submissions handled by server actions
   - Revalidation paths after mutations

3. **TypeScript Schema Validation**
   - Using Zod for schema validation
   - Defining schemas next to their usage in actions
   - Type inference from Zod schemas

## Database Access Patterns

1. **Server Action Client**
   - Using `createServerActionClient` for database operations
   - Database operations wrapped in try/catch blocks
   - Consistent error handling throughout

2. **Error Handling Pattern**
   - Try/catch blocks for all database operations
   - Structured error responses with { error, data } pattern
   - Detailed error messages in development, generic in production

3. **Revalidation Pattern**
   - Using `revalidatePath()` after mutations
   - Targeted revalidation for specific routes
   - Client-side route refresh after mutations

## Dialog Component Pattern

1. **Dialog Usage**
   - Using Radix UI Dialog component for modals
   - Controlling open state with React useState
   - Triggering dialogs from user actions

   ```typescript
   import {
     Dialog,
     DialogContent,
     DialogHeader,
     DialogTitle,
     DialogFooter,
   } from "@src/components/ui/dialog";

   function MyDialog() {
     const [open, setOpen] = useState(false);

     return (
       <Dialog open={open} onOpenChange={setOpen}>
         <DialogTrigger>Open Dialog</DialogTrigger>
         <DialogContent>
           <DialogHeader>
             <DialogTitle>Dialog Title</DialogTitle>
           </DialogHeader>
           <div>Dialog content goes here</div>
           <DialogFooter>
             <Button onClick={() => setOpen(false)}>Close</Button>
           </DialogFooter>
         </DialogContent>
       </Dialog>
     );
   }
   ```

2. **Confirmation Dialog Pattern**
   - Specific pattern for confirmation actions
   - Custom hooks for confirmation dialogs
   - Used for destructive actions like deletion

## Form Handling Patterns

1. **Form Validation**
   - Using Zod for schema validation
   - Client-side validation before submission
   - Server-side validation in server actions

2. **Form State Management**
   - Using React Hook Form for form state
   - Controlled inputs with validation
   - Form submission to server actions

3. **Error Handling**
   - Field-level error messages
   - Form-level error messages
   - Validation feedback to users

## Authentication Flow

1. **Sign Up Flow**
   - Using Supabase Auth for sign-up
   - Email/password and Google OAuth options
   - Email verification process
   - Initial profile creation

2. **Login Flow**
   - Using Supabase Auth for sign-in
   - Remember me functionality
   - Password reset flow
   - OAuth provider authentication

3. **Session Management**
   - Using Supabase Auth for session management
   - Protected routes via middleware
   - Session refresh handling
   - Secure logout functionality with cookie clearing

## Data Models and Relationships

### User System
- `users` ⟷ one-to-many ⟷ `saved_contacts`
- `users` ⟷ one-to-many ⟷ `privacy_consents`
- `users` ⟷ one-to-many ⟷ `profile_activity`

### Events System
- `users` ⟷ one-to-many ⟷ `organizations`
- `organizations` ⟷ one-to-many ⟷ `events`
- `events` ⟷ one-to-many ⟷ `ticket_types`
- `users` ⟷ one-to-many ⟷ `registrations`
- `ticket_types` ⟷ one-to-many ⟷ `registrations`

### Security & Compliance
- `users` ⟷ one-to-many ⟷ `emergency_contact_access_logs`
- `privacy_consents` ⟷ many-to-one ⟷ `privacy_consent_versions`

## Component Structure
- Server components for data-heavy pages
- Client components for interactive elements
- Page layout components provide consistent structure
- Feature-specific components organized by domain

## Data Flow
- Server actions for data mutations
- React server components for data fetching
- Client-side form state management
- Revalidation paths for updated data

## RBAC Implementation
- Supabase Auth for user authentication and role management
- Three primary roles: admin, user, event organizer
- Role-based middleware for route protection
- Server-side role checking for secure operations
- Database-level role enforcement with Row Level Security (RLS)

## Form Handling
- Multi-step forms with progress tracking
- Zod for form validation
- Server actions for form submission
- Resumable applications with partial data storage

## State Management
- Server components for static content
- React hooks for client-side state
- Form state with React Hook Form
- Global state with Zustand where needed

## Architecture Overview
- Next.js 15 App Router based frontend with React 19
- Edge-first architecture optimized for Netlify
- Multitenant system with subdomain support
- Row-based tenant separation using tenant ID
- Role-based access control via Supabase Auth
- Dynamic form generation with Zod validation
- State management using Zustand
- Supabase for authentication and database
- Tailwind CSS 4 with mask utilities for UI enhancements
- HSL color notation for better theming support

## Component Reusability Patterns
- **Contact Information Component**
  - Centralized contact information in reusable components
  - Single source of truth for contact details
  - Consistent display across multiple pages
  - Easily updatable from central location

- **Mobile Navigation Pattern**
  - Responsive header with hamburger menu for mobile
  - Full-screen overlay mobile menu for small screens
  - Desktop navigation displayed for larger viewports
  - Consistent navigation experience across devices

## Database Schema

### Core Tables
1. `users`
   - Primary user information linked to Supabase Auth
   - Role-based access control (public/organizer/admin)
   - Email indexing for quick lookups
   - Auth user ID for Supabase Auth integration

2. `organizations`
   - Organization profiles for event organizers
   - Document verification workflow
   - Status tracking (pending/approved/rejected)
   - Links to verifying admin

3. `events`
   - Event details and metadata
   - Type-specific schema support
   - Status management (draft/published/cancelled/completed)
   - Date and status indexing

4. `ticketTypes`
   - Multiple ticket types per event
   - Price and quantity management
   - Type-specific metadata storage

5. `registrations`
   - Event registration records
   - QR code-based check-in
   - Form data storage
   - Attendance and collection tracking

6. `payments`
   - Payment records with fee calculations
   - Platform fee and SST tracking
   - Payment status management

7. `collections`
   - Physical item collection tracking
   - Collection type categorization
   - Status and timestamp tracking

### Key Relations
```mermaid
graph TD
    U[Users] --> O[Organizations]
    O --> E[Events]
    E --> T[TicketTypes]
    T --> R[Registrations]
    R --> P[Payments]
    R --> C[Collections]
```

### Validation Layer
1. Database Schema (Drizzle ORM)
   - Type definitions
   - Foreign key constraints
   - Default values
   - Indexes

2. Runtime Validation (Zod)
   - Input validation
   - Type coercion
   - Error messages
   - Custom validation rules

3. Type Safety (TypeScript)
   - Inferred types from schema
   - Form data type definitions
   - Custom form field types

## Design Patterns
- Multitenant Architecture Pattern
  - Per-tenant subdomain routing
  - Logical data separation by tenant ID
- Dynamic Form Pattern
  - Schema-driven form generation
  - Zod validation integration
  - Event-type specific validation rules
- Data Access Pattern
  - Drizzle ORM for database operations
  - Supabase client for PostgreSQL database access (not auth)
  - Edge-optimized queries
  - Tenant-aware data access
- Mobile-First Navigation Pattern
  - Responsive hamburger menu for small screens
  - Context-based navigation options
  - Optimized for thumb-friendly interaction zones

## Component Structure
- Core Components
  - Event Creation Wizard
  - Dynamic Form Builder
  - Ticket Management
  - QR Code Generator
  - Mobile Navigation Menu
  - Reusable Contact Information
- Feature Modules
  - Organizer Dashboard
  - Attendee Portal
  - Admin Panel
  - Payment Processing
  - Legal Pages
- Shared Components
  - Form Components
  - Authentication UI
  - Layout Components

## Data Flow
1. Event Creation
   - Schema Definition → Form Generation → Data Validation → Storage
2. Registration Flow
   - Form Fill → Validation → Payment → Ticket Generation
3. Check-in Flow
   - QR Scan → Validation → Status Update
4. Export Flow
   - Query → Transform → Format → Download

## Key Technical Decisions
- Using Next.js 15 App Router with React 19
- Supabase for database and authentication
- Supabase Auth for authentication and RBAC
- Zod for schema validation
- Zustand for state management
- Tailwind CSS 4 + Shadcn UI for styling
- Tailwind 4 mask utilities for UI enhancements
- HSL color notation for better theming support
- Client components for interactive UI elements
- Server components for static content when possible

## System Constraints
- Edge-first deployment requirements
- Multitenant data isolation
- Schema validation requirements
- Real-time payment processing
- Concurrent registration handling
- Data export performance

## Code Organization
```
src/
├── app/             # Next.js App Router
│   ├── (legal)/     # Legal pages route group
├── components/      # React components
│   ├── layout/      # Layout components (header, footer, mobile menu)
│   └── ui/          # UI components
├── lib/            # Shared utilities
│   ├── db/         # Database utilities
│   │   ├── types.ts    # Database types
│   │   └── client.ts   # Database client
│   └── validations/    # Zod schemas
├── schemas/        # Database schemas
├── stores/         # Zustand stores
└── db/             # Database models
    └── schema.ts   # Drizzle schema
```

## Notes
- Maintain strict tenant data isolation
- Optimize for edge deployment
- Ensure schema validation at all data entry points
- Regular schema version control
- Monitor query performance
- Use 'use client' directive for interactive components
- Prefer server components where possible for better performance

## Authentication Pattern
- Supabase Auth provides identity and authentication layer
- Protected routes through Supabase Auth middleware
- Auth state accessed via Supabase Auth client
- Custom sign-in and sign-up pages using Supabase Auth
- Google OAuth integration for social login
- Secure session management with proper cookie handling