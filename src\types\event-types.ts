import { z } from 'zod';

/**
 * Event field types for registration forms
 */
export enum FieldType {
  TEXT = 'text',
  EMAIL = 'email',
  NUMBER = 'number',
  PHONE = 'phone',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  DATE = 'date',
  TIME = 'time',
  DATETIME = 'datetime',
  FILE = 'file',
  ADDRESS = 'address',
  HEADING = 'heading',
  PARAGRAPH = 'paragraph',
  DIVIDER = 'divider',
}

/**
 * Base field definition interface
 */
export interface FieldDefinition {
  id: string;
  type: FieldType;
  label: string;
  description?: string;
  required?: boolean;
  order?: number;
}

/**
 * Field definition for text fields
 */
export interface TextField extends FieldDefinition {
  type: FieldType.TEXT | FieldType.EMAIL | FieldType.PHONE;
  placeholder?: string;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
}

/**
 * Field definition for number fields
 */
export interface NumberField extends FieldDefinition {
  type: FieldType.NUMBER;
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
}

/**
 * Field definition for select fields
 */
export interface SelectField extends FieldDefinition {
  type: FieldType.SELECT | FieldType.MULTISELECT | FieldType.RADIO;
  options: (string | { label: string; value: string })[];
  placeholder?: string;
}

/**
 * Field definition for checkbox fields
 */
export interface CheckboxField extends FieldDefinition {
  type: FieldType.CHECKBOX;
  defaultValue?: boolean;
}

/**
 * Field definition for date/time fields
 */
export interface DateTimeField extends FieldDefinition {
  type: FieldType.DATE | FieldType.TIME | FieldType.DATETIME;
  min?: string;
  max?: string;
}

/**
 * Field definition for display-only elements
 */
export interface DisplayField extends FieldDefinition {
  type: FieldType.HEADING | FieldType.PARAGRAPH | FieldType.DIVIDER;
  content?: string;
}

/**
 * Union type for all field definitions
 */
export type EventField =
  | TextField
  | NumberField
  | SelectField
  | CheckboxField
  | DateTimeField
  | DisplayField;

/**
 * Base fields configuration for event types
 */
export interface EventTypeBaseFields {
  requiresTshirtSize?: boolean;
  requiresBibNumber?: boolean;
  hasCategories?: boolean;
  requiresAttendanceCertificate?: boolean;
  requiresSeating?: boolean;
  requiresAgeVerification?: boolean;
  requiresMerchandise?: boolean;
  requiresRanking?: boolean;
  requiresCustomFields?: boolean;
  [key: string]: boolean | undefined;
}

/**
 * Event type definition
 */
export interface EventType {
  id: string;
  name: string;
  slug: string;
  description?: string;
  baseFields: EventTypeBaseFields;
  customFields: EventField[];
  icon?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Base event category definition
 */
export interface BaseEventCategory {
  id: string;
  eventId: string;
  name: string;
  description?: string;
  properties: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Running event category properties
 */
export interface RunningEventCategoryProperties {
  distance?: number;
  price?: number;
  startTime?: string;
  earlyBirdPrice?: number;
  earlyBirdEndDate?: string;
  bibPrefix?: string;
  bibStartNumber?: string;
  bibRequireGeneration?: boolean;
  registrationOpen?: boolean;
  registrationCloseDate?: string;
  registrationLimit?: number;
  registrationCount?: number;
}

/**
 * Conference event category properties
 */
export interface ConferenceEventCategoryProperties {
  venue?: string;
  speaker?: string;
  sessionDuration?: number;
  price?: number;
  startTime?: string;
  endTime?: string;
  registrationLimit?: number;
  registrationCount?: number;
}

/**
 * Running event category
 */
export interface RunningEventCategory extends BaseEventCategory {
  properties: RunningEventCategoryProperties;
}

/**
 * Conference event category
 */
export interface ConferenceEventCategory extends BaseEventCategory {
  properties: ConferenceEventCategoryProperties;
}

/**
 * Generic event category
 */
export interface EventCategory extends BaseEventCategory {
  // No legacy fields, just using the base category with properties
}

/**
 * Registration field definition
 */
export interface RegistrationField {
  id: string;
  eventId: string;
  fieldId: string;
  fieldType: FieldType;
  label: string;
  description?: string;
  isRequired: boolean;
  isPublic: boolean;
  validationRules?: any;
  defaultValue?: any;
  options?: any;
  orderIndex: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Field mapping definition
 */
export interface FieldMapping {
  id: string;
  eventId: string;
  fieldId: string;
  profileField: string;
  isBidirectional: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Emergency contact settings
 */
export interface EmergencyContactSettings {
  required: boolean;
  fields: string[];
  allowSameForMultipleRegistrations: boolean;
}

/**
 * Zod schema for event type
 */
export const eventTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug must only contain lowercase letters, numbers, and hyphens"),
  description: z.string().optional(),
  baseFields: z.record(z.boolean()).optional(),
  customFields: z.array(
    z.object({
      id: z.string().min(1),
      type: z.nativeEnum(FieldType),
      label: z.string().min(1),
      required: z.boolean().optional(),
      options: z.array(z.string()).or(z.array(z.object({
        label: z.string(),
        value: z.string()
      }))).optional(),
    }).and(
      z.union([
        z.object({ type: z.enum([FieldType.TEXT, FieldType.EMAIL, FieldType.PHONE]), placeholder: z.string().optional() }),
        z.object({ type: z.enum([FieldType.NUMBER]), min: z.number().optional(), max: z.number().optional() }),
        z.object({ type: z.enum([FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO]), options: z.array(z.any()) }),
        z.object({ type: z.enum([FieldType.CHECKBOX]), defaultValue: z.boolean().optional() }),
        z.object({ type: z.enum([FieldType.DATE, FieldType.TIME, FieldType.DATETIME]), min: z.string().optional(), max: z.string().optional() }),
        z.object({ type: z.enum([FieldType.HEADING, FieldType.PARAGRAPH]), content: z.string().optional() }),
        z.object({ type: z.enum([FieldType.DIVIDER]) }),
      ])
    )
  ),
  icon: z.string().optional(),
});

/**
 * Zod schema for base event category
 */
export const baseEventCategorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  description: z.string().optional(),
  properties: z.record(z.any()).default({}),
});

/**
 * Zod schema for running event category properties
 */
export const runningEventCategoryPropertiesSchema = z.object({
  distance: z.number().optional(),
  price: z.number().nonnegative().optional(),
  startTime: z.string().optional(),
  earlyBirdPrice: z.number().nonnegative().optional(),
  earlyBirdEndDate: z.string().optional(),
  bibPrefix: z.string().optional(),
  bibStartNumber: z.string().optional(),
  bibRequireGeneration: z.boolean().optional().default(true),
  registrationOpen: z.boolean().optional().default(true),
  registrationCloseDate: z.string().optional(),
  registrationLimit: z.number().int().positive().optional(),
  registrationCount: z.number().int().nonnegative().optional().default(0),
});

/**
 * Zod schema for conference event category properties
 */
export const conferenceEventCategoryPropertiesSchema = z.object({
  venue: z.string().optional(),
  speaker: z.string().optional(),
  sessionDuration: z.number().int().positive().optional(),
  price: z.number().nonnegative().optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
  registrationLimit: z.number().int().positive().optional(),
  registrationCount: z.number().int().nonnegative().optional().default(0),
});

/**
 * Zod schema for event category
 */
export const eventCategorySchema = baseEventCategorySchema;

/**
 * Zod schema for registration field
 */
export const registrationFieldSchema = z.object({
  fieldId: z.string().min(1, "Field ID is required"),
  fieldType: z.nativeEnum(FieldType),
  label: z.string().min(1, "Label is required"),
  description: z.string().optional(),
  isRequired: z.boolean().optional().default(false),
  isPublic: z.boolean().optional().default(true),
  validationRules: z.any().optional(),
  defaultValue: z.any().optional(),
  options: z.any().optional(),
  orderIndex: z.number().int().nonnegative().optional(),
});

/**
 * Zod schema for field mapping
 */
export const fieldMappingSchema = z.object({
  fieldId: z.string().min(1, "Field ID is required"),
  profileField: z.string().min(1, "Profile field is required"),
  isBidirectional: z.boolean().optional().default(false),
});

/**
 * Zod schema for emergency contact settings
 */
export const emergencyContactSettingsSchema = z.object({
  required: z.boolean().optional().default(false),
  fields: z.array(z.string()).optional().default(["name", "phone", "relationship"]),
  allowSameForMultipleRegistrations: z.boolean().optional().default(true),
});