'use server';

import { createServerActionClient } from "@/lib/supabase/actions";
import fs from 'fs';
import path from 'path';

/**
 * Apply a specific migration file to the database
 */
export async function applyMigration(migrationName: string) {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" };
    }

    // Check if user has admin role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('auth_user_id', session.user.id)
      .single();

    if (userError || !userData) {
      return { success: false, error: "Failed to verify user role" };
    }

    if (!['admin', 'super_admin'].includes(userData.role)) {
      return { success: false, error: "Only administrators can apply migrations" };
    }

    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'src', 'db', 'migrations', migrationName);

    if (!fs.existsSync(migrationPath)) {
      return { success: false, error: `Migration file ${migrationName} not found` };
    }

    const migrationSql = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration
    // @ts-expect-error - The RPC function name might vary between environments
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSql });

    if (error) {
      console.error("Migration error:", error);
      return { success: false, error: `Migration failed: ${error.message}` };
    }

    return { success: true };
  } catch (error) {
    console.error("Error applying migration:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
