-- Add event_types table to support different event templates
-- Migration: 0013_add_event_types.sql

-- Start transaction for atomic migration
BEGIN;

-- Create event_types table
CREATE TABLE IF NOT EXISTS event_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  base_fields JSONB NOT NULL DEFAULT '{}'::jsonb, -- Base fields required for this event type
  custom_fields JSONB NOT NULL DEFAULT '[]'::jsonb, -- Template for custom fields specific to this type
  icon TEXT, -- Icon reference for UI display
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create index for faster lookups by slug
CREATE INDEX IF NOT EXISTS event_types_slug_idx ON event_types(slug);

-- Add default event types
INSERT INTO event_types (name, slug, description, base_fields, custom_fields)
VALUES 
  ('Runs', 'runs', 'Running events including marathons, half-marathons, and fun runs', 
   '{
     "requiresTshirtSize": true,
     "requiresBibNumber": true,
     "hasCategories": true
   }'::jsonb,
   '[
     {
       "id": "tshirt_size",
       "type": "select",
       "label": "T-Shirt Size",
       "required": true,
       "options": ["XS", "S", "M", "L", "XL", "XXL", "XXXL"]
     },
     {
       "id": "distance_preference",
       "type": "radio",
       "label": "Distance Preference",
       "required": true,
       "options": ["Full Marathon", "Half Marathon", "Fun Run"]
     }
   ]'::jsonb),
  
  ('Seminar', 'seminar', 'Educational and professional development seminars', 
   '{
     "requiresAttendanceCertificate": true,
     "requiresSeating": false
   }'::jsonb,
   '[
     {
       "id": "occupation",
       "type": "text",
       "label": "Occupation",
       "required": false
     },
     {
       "id": "dietary_restrictions",
       "type": "multiselect",
       "label": "Dietary Restrictions",
       "required": false,
       "options": ["Vegetarian", "Vegan", "Gluten-Free", "Halal", "Kosher", "None"]
     }
   ]'::jsonb),
  
  ('Comedy', 'comedy', 'Stand-up comedy shows and performances', 
   '{
     "requiresSeating": true,
     "requiresAgeVerification": true
   }'::jsonb,
   '[
     {
       "id": "age_verification",
       "type": "checkbox",
       "label": "I confirm I am 18+ years old",
       "required": true
     }
   ]'::jsonb),
  
  ('Concert', 'concert', 'Music concerts and performances', 
   '{
     "requiresSeating": true,
     "requiresMerchandise": true
   }'::jsonb,
   '[
     {
       "id": "merchandise_preference",
       "type": "multiselect",
       "label": "Merchandise Interest",
       "required": false,
       "options": ["T-Shirt", "Poster", "CD/Vinyl", "Hoodie", "Hat", "None"]
     }
   ]'::jsonb),
  
  ('Chess', 'chess', 'Chess tournaments and competitions', 
   '{
     "requiresRanking": true,
     "requiresSeating": true
   }'::jsonb,
   '[
     {
       "id": "experience_level",
       "type": "select",
       "label": "Experience Level",
       "required": true,
       "options": ["Beginner", "Intermediate", "Advanced", "Master"]
     },
     {
       "id": "ranking",
       "type": "number",
       "label": "Current Rating/Ranking (if applicable)",
       "required": false
     }
   ]'::jsonb),
  
  ('Custom', 'custom', 'Custom event with fully configurable fields', 
   '{
     "requiresSeating": false,
     "requiresCustomFields": true
   }'::jsonb,
   '[]'::jsonb);

-- Add event_type_id reference to events table
ALTER TABLE events ADD COLUMN IF NOT EXISTS event_type_id UUID REFERENCES event_types(id);

-- Add comment for documentation
COMMENT ON TABLE event_types IS 'Predefined event types with their base and custom field templates';
COMMENT ON COLUMN event_types.base_fields IS 'JSON object containing base configuration for this event type';
COMMENT ON COLUMN event_types.custom_fields IS 'JSON array of custom fields templates for this event type';
COMMENT ON COLUMN events.event_type_id IS 'Reference to the event type template used for this event';

-- Force schema cache reload
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('0013_add_event_types', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit transaction
COMMIT; 