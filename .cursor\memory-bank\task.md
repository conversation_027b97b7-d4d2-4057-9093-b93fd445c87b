# Event Ticketing System Implementation Plan

## Phase 1: Core Infrastructure Setup

### Database Schema & Migration (Server-side)
- [x] Define and create `users` table schema with Supabase
- [x] Create `user_profiles` table with common registration fields
- [x] Design `saved_contacts` table for friends/family information
- [ ] Create `user_preferences` table for settings
- [x] Define `profile_activity` table for tracking changes
- [x] Create `organizations` table for event organizers
- [x] Define `privacy_consents` table for GDPR compliance
- [ ] Create `emergency_contacts` table with relationship mapping

### Authentication System (Server-side)
- [x] Configure authentication middleware (migrated from Clerk to Supabase Auth)
- [x] Set up RBAC with Supabase Auth (admin, user, organizer roles)
- [x] Implement protected route patterns
- [x] Create auth helpers for server-side auth checks
- [x] Implement Google OAuth authentication
- [ ] Set up organization verification flow

### Server Components & API Routes
- [x] Create base API route structure with error handling
- [x] Implement server actions for form submissions
- [x] Set up Zod validation schemas for API inputs
- [x] Create database client utility
- [x] Set up revalidation paths for data updates
- [x] Create server-side data fetching patterns

### Client Component Foundation
- [x] Set up client-side form state management
- [x] Create reusable form components with validation
- [x] Implement responsive layout components
- [x] Set up client-side auth with Supabase Auth (migrated from Clerk)
- [x] Create basic navigation structure
- [x] Set up route change handlers
- [x] Implement theme support with HSL color notation

### UI Component Infrastructure
- [x] Install and configure Dialog component from Radix UI
- [x] Create utility function (cn) for class name management
- [x] Set up package dependencies with pnpm
- [x] Create skeleton component for loading states
- [x] Implement tooltip component for UI guidance
- [x] Create standardized card component layouts
- [x] Implement Tailwind 4 mask utilities for UI enhancements
- [x] Add toast notifications with Sonner
- [x] Implement animations with Framer Motion

## Phase 2: User Management System

### User Profile Management (Server)
- [x] Create server actions for profile creation
- [x] Implement profile update server functions
- [x] Build contact management server actions
- [x] Set up profile data versioning logic
- [x] Create privacy consent handlers
- [x] Implement emergency contact storage logic

### User Profile UI (Client)
- [x] Build profile management dashboard (client component)
- [x] Create editable profile form with validation
- [x] Build friends/family contact management interface
- [ ] Implement preference center UI
- [x] Create profile completion tracker component
- [x] Build activity history viewer component
- [x] Create data export request component
- [ ] Implement emergency contact form component

### Authentication UI (Client)
- [x] Implement sign-up form with email verification
- [x] Create SSO buttons for Google authentication
- [x] Build profile creation wizard
- [ ] Create user onboarding experience
- [x] Implement forgot password flow
- [x] Build account linking interface (Google OAuth)

## Phase 3: Event Management System

### Event Data Models (Server)
- [x] Create `events` table with JSON configuration support
- [x] Define `event_types` table with templates ("Runs", seminar, comedy, concert, chess, custom)
- [x] Create `registration_fields` model for custom fields
- [x] Build `event_categories` model for running events (full marathon, half marathon, fun run)
- [x] Create `field_mappings` table for profile connections
- [x] Create server-side validation for event configs using Zod
- [x] Implement emergency contact settings model

### Event Creation Flow (Server)
- [x] Create server actions for event creation with template selection
- [x] Implement specialized running event configuration endpoints
- [x] Develop bib number auto-generation system with customization options
- [x] Build event type management endpoints for custom fields
- [x] Create ticket/seating management endpoints
- [x] Implement event publishing workflow with validation

### Event Creation UI (Client)
- [x] Build multi-step event creation wizard UI with step navigation
- [x] Create event type selection component with template previews
- [x] Implement drag-and-drop field editor for custom registration fields
- [x] Build running event specific configuration UI (categories, bib numbers, t-shirt sizes)
- [x] Create component library for field types (text, boolean, radio, multiselect)
- [x] Implement ticket and seating management interface
- [x] Build preview/publish controls with validation
- [x] Create real-time JSON structure preview

## Phase 4: Ticket Management System

### Ticket Data Models (Server)
- [x] Create `ticket_types` table with pricing and inventory
- [x] Define `orders` table for purchase tracking
- [x] Create `tickets` table with attendee information
- [x] Build `registrations` table with event-specific data
- [ ] Create `registration_history` for change tracking
- [ ] Implement inventory management logic
- [ ] Create ticket code generation service

### Ticket Management API (Server)
- [x] Implement ticket creation server actions
- [x] Create inventory update server functions
- [x] Build ticket availability checks
- [x] Implement ticket limit enforcement (max 10)
- [x] Create ticket validation rules engine
- [x] Implement QR code generation service

### Ticket Management UI (Client)
- [x] Build ticket type creation interface
- [x] Create pricing tier management component
- [x] Implement inventory tracking dashboard
- [x] Build ticket template designer
- [x] Create ticket preview component
- [x] Implement ticket settings panel

## Phase 5: Checkout System

### Order Processing (Server)
- [ ] Create order creation server actions
- [ ] Implement reservation system logic
- [ ] Build order expiration functionality
- [ ] Create payment processing abstraction layer
- [ ] Implement order status management
- [ ] Build order confirmation handlers
- [ ] Create notification dispatch system

### Shopping Cart (Client)
- [ ] Build shopping cart component
- [ ] Create multi-ticket checkout flow
- [ ] Implement attendee information form
- [ ] Build stored contact selector component
- [ ] Create order summary component
- [ ] Implement reservation timer component
- [ ] Build payment method selection
- [ ] Create order confirmation page

### Auto-Population System (Client + Server)
- [ ] Create server actions for profile data retrieval
- [ ] Build client component for smart field mapping
- [ ] Implement quick-fill functionality from profiles
- [ ] Create contact selector dropdown component
- [ ] Build field validation with profile suggestions
- [ ] Implement "save to profile" toggle component
- [ ] Create profile update prompts

## Phase 6: Running Event Specific Features

### Bib Number System (Server)
- [ ] Create `running_categories` table for race distances
- [ ] Implement `