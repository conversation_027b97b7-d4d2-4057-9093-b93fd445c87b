# System Patterns

## Architecture Overview
The Fuiyoo events platform follows a modern Next.js application architecture with the App Router pattern, emphasizing clear separation between server and client components.

```mermaid
flowchart TD
    User[User Browser] --> App[Next.js App]
    App --> PageRouter[Page Router]
    App --> AppRouter[App Router]
    
    AppRouter --> ServerComponents[Server Components]
    AppRouter --> ClientComponents[Client Components]
    
    ServerComponents --> DataFetching[Data Fetching]
    DataFetching --> MockData[Mock Data]
    DataFetching --> Supabase[Supabase DB]
    
    ClientComponents --> Forms[Form Components]
    ClientComponents --> UI[UI Components]
```

## Component Structure
- **Page Components**: Server components for overall page structure in src/app
- **Client Components**: Interactive components marked with 'use client'
- **UI Components**: shadcn/ui-based components in components/ui/ and src/components/ui/
- **Feature Components**: Components organized by feature in src/components/
- **Profile Components**: User profile related components in src/components/profile/
- **Dashboard Components**: Admin and user dashboard components in src/components/dashboard/
- **Event Components**: Event management components in src/components/events/

## Data Flow
- **Server Actions**: Using 'use server' for data mutations (src/app/actions/)
- **Client-side**: Manages UI state and form interactions
- **Form Submission**: Collects user input and submits via server actions
- **Clerk Authentication**: Manages user sessions and identity
- **Supabase Database**: Stores application data with proper user isolation

## Success Patterns

### Component Organization
The UI components are organized into the following structure:

- **UI Components**: shadcn/ui-based components in src/components/ui/
- **Feature Components**: Components organized by feature in src/components/
- **Profile Components**: User profile related components in src/components/profile/
- **Dashboard Components**: Admin and user dashboard components in src/components/dashboard/
- **Event Components**: Event management components in src/components/events/

### Type Safety
- Enum-based categories and locations (EventCategory, MalaysiaState)
- TypeScript interfaces for component props
- Zod validation for form data
- Strict typing for event and user data

### Error Handling
1. **UI Error Prevention**:
   - Non-empty values for select items
   - Conditional rendering based on data availability
   - Fallbacks for missing images

2. **User Experience**:
   - Placeholder text for empty states
   - Clear error messages
   - Form validation with Zod schemas

### Performance Optimization
- Next.js image optimization
- Efficient Tailwind CSS
- Configured webpack caching
- Edge-first deployment strategy

## Current Design Decisions
1. Using mockEvents.ts for development data
2. Contacts management for faster event registration
3. Select dropdowns with "all" option (not empty string)
4. Pexels as the image provider
5. Responsive design with mobile-first approach 

## Directory Structure

The project follows a Clear Architecture structure:

- Feature-specific components in src/components/
- UI components in src/components/ui/
- Layouts in src/components/layout/
- App routes in src/app/
