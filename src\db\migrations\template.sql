-- Template for database migrations
-- Usage: Copy this file and rename to {version}_{name}.sql
-- Example: 0011_add_user_preferences.sql

-- Migration name: {name}
-- Description: {description}
-- Author: {author}
-- Created: {date}

-- Start transaction for atomic migration
BEGIN;

-- ==========================================
-- Migration logic goes here
-- ==========================================

-- Example: Create a new table
-- CREATE TABLE IF NOT EXISTS example_table (
--   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--   user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
--   name TEXT NOT NULL,
--   description TEXT,
--   created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
--   updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
-- );

-- Example: Add a column
-- ALTER TABLE example_table ADD COLUMN IF NOT EXISTS new_column TEXT;

-- Example: Create an index
-- CREATE INDEX IF NOT EXISTS example_table_user_id_idx ON example_table(user_id);

-- Example: Update values
-- UPDATE example_table SET updated_at = now() WHERE condition;

-- ==========================================
-- End migration logic
-- ==========================================

-- Force schema cache reload
-- This notifies PostgREST to refresh its schema cache
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('{version}_{name}', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit the transaction
COMMIT; 