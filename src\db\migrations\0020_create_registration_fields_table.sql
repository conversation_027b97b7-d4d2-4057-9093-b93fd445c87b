-- Create registration_fields table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.registration_fields (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    field_id TEXT NOT NULL,
    field_type TEXT NOT NULL,
    label TEXT NOT NULL,
    description TEXT,
    is_required BOOLEAN DEFAULT false,
    is_public BOOLEAN DEFAULT true,
    validation_rules JSONB,
    default_value JSONB,
    options JSONB,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_registration_fields_event_id ON public.registration_fields(event_id);

-- Add comment
COMMENT ON TABLE public.registration_fields IS 'Custom registration fields for events';

-- Update schema_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'schema_migrations') THEN
        INSERT INTO schema_migrations (version, applied_at)
        VALUES ('0020_create_registration_fields_table', NOW())
        ON CONFLICT (version) DO UPDATE SET applied_at = NOW();
    END IF;
END
$$;
