# Event Management System - Data Models

This document describes the data models for the Event Management System.

## Overview

The Event Management System's data models consist of several tables:

1. `event_types` - Templates for different types of events (Runs, Seminar, Concert, etc.)
2. `event_categories` - Categories within events (e.g., Marathon distances for running events)
3. `registration_fields` - Custom fields for event registration forms
4. `field_mappings` - Mappings between registration fields and user profiles for auto-population

## Database Migration

The database migrations are managed through SQL files in the `src/db/migrations` directory.

### Migration Files

- `0013_add_event_types.sql` - Creates the event_types table and adds predefined event types
- `0014_add_running_event_categories.sql` - Creates the event_categories table with special focus on running events
- `0015_add_registration_fields.sql` - Creates registration_fields and field_mappings tables, and adds emergency contact settings to events

### Running Migrations

You can apply the migrations using the provided script:

```bash
# Run the migration script
pnpm ts-node scripts/apply-event-models.ts
```

## Data Models

### EventType

Represents a template for creating events of a specific type:

- Base fields:
  - `id`: UUID
  - `name`: Name of the event type (e.g., "Runs", "Seminar")
  - `slug`: URL-friendly identifier
  - `description`: Optional description of the event type
  - `base_fields`: JSON object with configuration flags for the event type
  - `custom_fields`: JSON array of default field templates for the event type
  - `icon`: Optional icon reference for UI display
  - `created_at`: Creation timestamp
  - `updated_at`: Last update timestamp

### EventCategory

Represents categories within events, particularly useful for running events:

- Base fields:
  - `id`: UUID
  - `event_id`: Reference to the parent event
  - `name`: Name of the category (e.g., "Full Marathon", "Half Marathon")
  - `description`: Optional description
  - `capacity`: Maximum number of participants (optional)
  - `price`: Price for this category (optional)
  - `start_time`: Start time for this category (optional)
  - `early_bird_price`: Discounted price (optional)
  - `early_bird_end_date`: When early bird pricing ends (optional)
  - `bib_prefix`: Prefix for bib numbers (e.g., "FM-" for Full Marathon)
  - `bib_start_number`: Starting number for bib sequence
  - `bib_require_generation`: Whether bib numbers should be auto-generated
  - `registration_open`: Whether registration is open for this category
  - `registration_close_date`: When registration closes (optional)
  - `registration_limit`: Maximum number of registrations (optional)
  - `registration_count`: Current count of registrations
  - `custom_fields`: JSON array of category-specific custom fields (optional)
  - `created_at`: Creation timestamp
  - `updated_at`: Last update timestamp

### RegistrationField

Represents a custom field in an event's registration form:

- Base fields:
  - `id`: UUID
  - `event_id`: Reference to the parent event
  - `field_id`: Unique identifier for the field within this event
  - `field_type`: Type of field (text, select, checkbox, etc.)
  - `label`: Display label for the field
  - `description`: Optional help text
  - `is_required`: Whether the field is required
  - `is_public`: Whether the field is visible to registrants (vs. admin-only)
  - `validation_rules`: JSON schema for field validation using Zod (optional)
  - `default_value`: Default value if any (optional)
  - `options`: Options for select, radio, checkbox fields (optional)
  - `order_index`: For ordering fields in the form
  - `created_at`: Creation timestamp
  - `updated_at`: Last update timestamp

### FieldMapping

Maps registration fields to user profile fields for auto-population:

- Base fields:
  - `id`: UUID
  - `event_id`: Reference to the parent event
  - `field_id`: The field_id in registration_fields to map
  - `profile_field`: The field in the user_profiles table to map to
  - `is_bidirectional`: Whether updates to profile should reflect in registration data
  - `created_at`: Creation timestamp
  - `updated_at`: Last update timestamp

## Repositories

The system includes TypeScript repositories for each model:

- `EventTypeRepository`: Manages event type operations
- `EventCategoryRepository`: Manages event category operations
- `RegistrationFieldRepository`: Manages registration field operations
- `FieldMappingRepository`: Manages field mapping operations

Each repository provides methods for creating, reading, updating and deleting records, with additional utility methods for common operations. 