-- Migration: Create users mapping table
-- This table maps external auth IDs (like <PERSON>) to internal UUIDs

CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY,
  external_id TEXT NOT NULL UNIQUE,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_external_id ON public.users (external_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users (email);

-- Comment on table and columns for documentation
COMMENT ON TABLE public.users IS 'Maps external user IDs (e.g., from Clerk) to internal UUIDs';
COMMENT ON COLUMN public.users.id IS 'Internal UUID used for database relationships';
COMMENT ON COLUMN public.users.external_id IS 'External ID from auth provider (e.g., Clerk user_id)';

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_users_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update the updated_at timestamp on update
DROP TRIGGER IF EXISTS users_updated_at ON public.users;
CREATE TRIGGER users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION update_users_updated_at(); 