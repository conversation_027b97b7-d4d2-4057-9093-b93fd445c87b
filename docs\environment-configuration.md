# Environment Configuration

This document explains how to configure the application for different environments (development, staging, production) and how authentication redirects work.

## Environment Files

The application uses different environment files for different environments:

- `.env.development`: Used for local development
- `.env.staging`: Used for the staging environment
- `.env.production`: Used for the production environment

## Important Environment Variables

### Base URLs

- `NEXT_PUBLIC_SITE_URL`: The base URL of the application
  - Development: `http://localhost:3000`
  - Staging: `https://staging--fuiyoo.netlify.app`
  - Production: `https://fuiyoo.netlify.app`

### Authentication URLs

- `NEXT_PUBLIC_AUTH_REDIRECT_URL`: The URL that Supabase will redirect to after authentication
  - Development: `http://localhost:3000/auth/callback`
  - Staging: `https://staging--fuiyoo.netlify.app/auth/callback`
  - Production: `https://fuiyoo.netlify.app/auth/callback`

## Supabase Configuration

In the Supabase dashboard, you need to configure the following:

1. **Site URL**: Set this to your production URL (`https://fuiyoo.netlify.app`)

2. **Redirect URLs**: Add all the environments you want to support:
   - `https://fuiyoo.netlify.app/auth/callback` (Production)
   - `https://staging--fuiyoo.netlify.app/auth/callback` (Staging)
   - `http://localhost:3000/auth/callback` (Development)

## How Authentication Redirects Work

The authentication flow works as follows:

1. User clicks "Sign in with Google" button
2. The application determines the correct callback URL based on the current environment
3. The user is redirected to Google for authentication
4. After successful authentication, Google redirects back to the callback URL
5. The callback handler exchanges the auth code for a session
6. The user is redirected to their intended destination (dashboard or original URL)

## Preventing Cross-Environment Redirects

The application includes safeguards to prevent redirecting users between different environments:

1. The `getBaseUrl()` function in `src/utils/url.ts` determines the correct base URL based on the current environment
2. The `getAuthCallbackUrl()` function ensures the callback URL matches the current environment
3. The auth callback handler checks if the redirect URL is for a different environment and redirects to the dashboard instead

## Troubleshooting

If you're experiencing issues with authentication redirects:

1. Check the browser console for logs about the callback URL and redirects
2. Verify that the correct environment variables are set for your environment
3. Make sure the redirect URLs are properly configured in the Supabase dashboard
4. Clear your browser cookies and local storage to start with a clean state

## Local Development

For local development:

1. Make sure you're using the `.env.development` file
2. Verify that `NEXT_PUBLIC_SITE_URL` is set to `http://localhost:3000`
3. Verify that `NEXT_PUBLIC_AUTH_REDIRECT_URL` is set to `http://localhost:3000/auth/callback`
4. Make sure `http://localhost:3000/auth/callback` is added to the redirect URLs in the Supabase dashboard
