# JSON-Based Event Categories Implementation

This document provides technical details about the implementation of JSON-based event categories in the Fuiyoo platform.

## Overview

The JSON-based category structure allows for flexible, event type-specific category properties without requiring database schema changes for each new event type. This approach:

1. Maintains a consistent database schema
2. Provides type safety through TypeScript interfaces
3. Supports different properties for different event types
4. Enables efficient querying of common properties

## Database Schema

The `event_categories` table includes a `properties` JSONB column that stores category-specific properties:

```sql
ALTER TABLE event_categories ADD COLUMN properties JSONB DEFAULT '{}'::jsonb;
```

## TypeScript Type System

### Base Types

```typescript
// Base event category definition
interface BaseEventCategory {
  id: string;
  eventId: string;
  name: string;
  description?: string;
  properties: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}
```

### Event Type-Specific Interfaces

```typescript
// Running event category properties
interface RunningEventCategoryProperties {
  distance?: number;
  capacity?: number;
  price?: number;
  startTime?: string;
  earlyBirdPrice?: number;
  earlyBirdEndDate?: string;
  bibPrefix?: string;
  bibStartNumber?: string;
  bibRequireGeneration?: boolean;
  registrationOpen?: boolean;
  registrationCloseDate?: string;
  registrationLimit?: number;
  registrationCount?: number;
}

// Conference event category properties
interface ConferenceEventCategoryProperties {
  venue?: string;
  speaker?: string;
  sessionDuration?: number;
  maxAttendees?: number;
  price?: number;
  startTime?: string;
  endTime?: string;
}

// Running event category
interface RunningEventCategory extends BaseEventCategory {
  properties: RunningEventCategoryProperties;
}

// Conference event category
interface ConferenceEventCategory extends BaseEventCategory {
  properties: ConferenceEventCategoryProperties;
}
```

## Repository Layer

The repository layer handles the conversion between application types and database records:

```typescript
// Create a category with type-specific properties
async createCategory(
  category: Omit<EventCategory, 'id' | 'createdAt' | 'updatedAt'> | 
  (Omit<BaseEventCategory, 'id' | 'createdAt' | 'updatedAt'> & { eventType?: string })
): Promise<EventCategory> {
  // Implementation details...
}
```

## Event Type Templates

Templates are defined for different event types to pre-populate category structures:

```typescript
// Running event category template
export const runningEventCategoryTemplate = {
  properties: {
    distance: 0,
    bibPrefix: '',
    bibStartNumber: '1001',
    bibRequireGeneration: true,
    registrationOpen: true,
    registrationCount: 0,
  }
};

// Conference event category template
export const conferenceEventCategoryTemplate = {
  properties: {
    venue: '',
    sessionDuration: 60,
    maxAttendees: 100,
    requiresRegistration: true,
  }
};
```

## Querying JSON Properties

Supabase and PostgreSQL provide operators for querying JSON data:

```typescript
// Get categories by event type-specific property
async getCategoriesByProperty(
  eventId: string, 
  propertyName: string, 
  propertyValue: any
): Promise<EventCategory[]> {
  const { data, error } = await supabase
    .from('event_categories')
    .select('*')
    .eq('event_id', eventId)
    .eq(`properties->>${propertyName}`, propertyValue);
  
  // Process results...
}
```

## UI Implementation

The UI dynamically renders form fields and displays based on the event type:

1. Form fields are rendered based on the event type
2. Properties are stored in the JSON object
3. The UI can toggle between legacy and JSON-based formats
4. Properties are displayed in a collapsible section

## Backward Compatibility

The implementation maintains backward compatibility with the existing schema:

1. Legacy fields are still supported
2. When using the JSON format, data is also stored in legacy fields
3. When reading, the code checks for both formats
4. The UI can toggle between formats

## Benefits

1. **Flexibility**: Easily adapt to different event types without schema changes
2. **Future-Proof**: Add new category properties without migrations
3. **Type Safety**: Maintain TypeScript type checking with interfaces
4. **Performance**: Keep core fields queryable while allowing flexible properties
5. **Simplified Code**: Cleaner repository layer with less mapping code

## Usage Examples

### Creating a Running Event Category

```typescript
const runningCategory = {
  name: "10K Run",
  description: "10 kilometer run",
  properties: {
    distance: 10,
    bibPrefix: "10K-",
    bibStartNumber: "1001",
    price: 50,
    capacity: 500,
    registrationOpen: true
  },
  eventType: "running"
};

await eventCategoryRepository.createCategory(runningCategory);
```

### Creating a Conference Event Category

```typescript
const conferenceCategory = {
  name: "Workshop Session",
  description: "Hands-on workshop",
  properties: {
    venue: "Room 101",
    speaker: "Jane Doe",
    sessionDuration: 90,
    maxAttendees: 30,
    price: 25
  },
  eventType: "conference"
};

await eventCategoryRepository.createCategory(conferenceCategory);
```

### Querying Categories

```typescript
// Get all running categories with distance > 5km
const longRunCategories = await eventCategoryRepository.getCategoriesByProperty(
  eventId,
  "distance",
  5
);

// Get all categories with a specific speaker
const speakerCategories = await eventCategoryRepository.getCategoriesByProperty(
  eventId,
  "speaker",
  "Jane Doe"
);
```
