/**
 * Direct database migration to add city column to events table
 * Uses direct PostgreSQL connection to add the column regardless of framework
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

async function addCityColumn() {
  console.log('Adding city column to events table with direct SQL...');
  
  // Create a direct SQL command
  const alterTableCommand = `
  DO $$
  BEGIN
    -- Check if the column exists first
    IF NOT EXISTS (
      SELECT 1 
      FROM information_schema.columns 
      WHERE table_name = 'events' AND column_name = 'city'
    ) THEN
      -- Add the column if it doesn't exist
      ALTER TABLE events ADD COLUMN city TEXT;
      
      -- Notify the schema reload
      PERFORM pg_notify('pgrst', 'reload schema');
      
      RAISE NOTICE 'Added city column to events table';
    ELSE
      RAISE NOTICE 'City column already exists in events table';
    END IF;
  END
  $$;
  `;
  
  try {
    // Attempt to connect using Supabase environment variables
    console.log('Trying to connect to database...');
    
    // Find any database URL from Supabase
    let databaseUrl = process.env.DATABASE_URL || 
                       process.env.SUPABASE_DATABASE_URL || 
                       process.env.POSTGRES_URL;
                       
    if (!databaseUrl) {
      console.error('No database URL found. Cannot proceed.');
      console.log('Please set DATABASE_URL or SUPABASE_DATABASE_URL in .env.local');
      process.exit(1);
    }
    
    // Use the native pg client for better performance
    const { Client } = require('pg');
    const client = new Client({ connectionString: databaseUrl });
    
    // Connect to the database
    await client.connect();
    console.log('Connected to database successfully');
    
    // Execute the SQL command
    console.log('Executing SQL command...');
    const result = await client.query(alterTableCommand);
    console.log('Command executed successfully');
    
    // Close the connection
    await client.end();
    console.log('Database connection closed');
    console.log('City column migration completed');
  } catch (error) {
    console.error('Error adding city column:', error);
    process.exit(1);
  }
}

// Run the migration
addCityColumn(); 