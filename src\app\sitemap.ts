import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  // Get the base URL from environment or use a default
  const baseUrl = process?.env?.NEXT_PUBLIC_SITE_URL || 'https://fuiyoo.netlify.app'

  // Define static routes - update this based on your actual routes
  const staticRoutes = [
    '',
    '/about',
    '/contact',
    '/login',
  ].map(route => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: route === '' ? 1 : 0.8,
  }))

  // You can add dynamic routes here if needed, for example:
  // const dynamicRoutes = await fetchDynamicRoutes()
  //   .then(routes => routes.map(route => ({
  //     url: `${baseUrl}/${route.slug}`,
  //     lastModified: new Date(route.updatedAt),
  //     changeFrequency: 'daily' as const,
  //     priority: 0.7,
  //   })))

  return [
    ...staticRoutes,
    // ...dynamicRoutes, // Uncomment if you implement dynamic routes
  ]
}