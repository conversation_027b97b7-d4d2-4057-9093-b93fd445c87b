-- Create organization_applications table
CREATE TABLE IF NOT EXISTS public.organization_applications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL REFERENCES public.users(id),
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'approved', 'rejected')),
  data JSONB NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  submitted_at TIMESTAMPTZ,
  reviewed_at TIMESTAMPTZ,
  reviewed_by TEXT REFERENCES public.users(id),
  rejection_reason TEXT
);

-- Add indexes
CREATE INDEX IF NOT EXISTS organization_applications_user_id_idx ON public.organization_applications(user_id);
CREATE INDEX IF NOT EXISTS organization_applications_status_idx ON public.organization_applications(status);

-- Add RLS policies
ALTER TABLE public.organization_applications ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own applications
CREATE POLICY "Users can view their own applications"
  ON public.organization_applications
  FOR SELECT
  USING (auth.uid()::text = user_id OR 
         EXISTS (
           SELECT 1 FROM public.users 
           WHERE users.auth_user_id = auth.uid()::text 
           AND (users.role = 'admin' OR users.role = 'super_admin')
         ));

-- Policy for users to insert their own applications
CREATE POLICY "Users can insert their own applications"
  ON public.organization_applications
  FOR INSERT
  WITH CHECK (auth.uid()::text = user_id);

-- Policy for users to update their own draft applications
CREATE POLICY "Users can update their own draft applications"
  ON public.organization_applications
  FOR UPDATE
  USING (auth.uid()::text = user_id AND status = 'draft')
  WITH CHECK (auth.uid()::text = user_id AND status = 'draft');

-- Policy for admins to update any application
CREATE POLICY "Admins can update any application"
  ON public.organization_applications
  FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM public.users 
    WHERE users.auth_user_id = auth.uid()::text 
    AND (users.role = 'admin' OR users.role = 'super_admin')
  ));

-- Add comment
COMMENT ON TABLE public.organization_applications IS 'Applications for organization/event organizer status';
