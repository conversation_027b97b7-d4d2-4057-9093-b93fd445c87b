# Supabase Authentication with Next.js

This document outlines the best practices for implementing Supabase authentication in a Next.js application.

## Key Components

1. **Middleware**: Handles authentication state across the application
2. **Cookie Management**: Ensures proper synchronization between browser and server
3. **Auth Context**: Provides authentication state to components
4. **API Routes**: Handle authentication callbacks and session management

## Middleware Implementation

The middleware is responsible for:
- Refreshing authentication tokens
- Managing cookie state
- Redirecting unauthenticated users when necessary

```typescript
// middleware.ts
import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            request.cookies.set(name, value)
          })
          
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          
          cookiesToSet.forEach(({ name, value, options }) => {
            response.cookies.set(name, value, options)
          })
        },
      },
    }
  )

  // IMPORTANT: Get both user and session to validate authentication state
  const { data: { user } } = await supabase.auth.getUser()
  const { data: { session } } = await supabase.auth.getSession()

  // Handle protected routes
  if (!user && !session && !isPublicRoute(request.nextUrl.pathname)) {
    const redirectUrl = new URL('/login', request.url)
    redirectUrl.searchParams.set('redirect_url', request.url)
    return NextResponse.redirect(redirectUrl)
  }

  return response
}

function isPublicRoute(path: string) {
  const publicRoutes = [
    '/',
    '/login',
    '/signup',
    '/auth/callback',
    '/api/auth',
  ]
  
  return publicRoutes.some(route => path.startsWith(route))
}
```

## Cookie Handling

Proper cookie handling is crucial for maintaining authentication state:

1. Always use `getAll()` and `setAll()` methods for cookies
2. Copy cookies between responses when creating new responses
3. Ensure cookies are properly set in both request and response objects

## Auth Context

The Auth Context provides authentication state to components:

```typescript
// contexts/auth-context.tsx
'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

const AuthContext = createContext({
  isSignedIn: false,
  user: null,
  loading: true,
  refreshSession: async () => {}
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }) {
  const [isSignedIn, setIsSignedIn] = useState(false)
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  const refreshSession = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      const { data: { session } } = await supabase.auth.getSession()
      
      setIsSignedIn(!!user && !!session && !error)
      setUser(user)
      setLoading(false)
    } catch (error) {
      console.error('Error refreshing session:', error)
      setIsSignedIn(false)
      setUser(null)
      setLoading(false)
    }
  }

  useEffect(() => {
    refreshSession()
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setIsSignedIn(!!session)
        
        if (!session) {
          setUser(null)
          setLoading(false)
        } else {
          await refreshSession()
        }
      }
    )
    
    return () => {
      subscription.unsubscribe()
    }
  }, [])

  return (
    <AuthContext.Provider value={{ isSignedIn, user, loading, refreshSession }}>
      {children}
    </AuthContext.Provider>
  )
}
```

## API Routes for Auth

Implement these API routes for authentication:

1. `/auth/callback`: Handles OAuth and email confirmation callbacks
2. `/api/auth/reset-state`: Resets authentication state
3. `/api/auth/clear-session`: Clears session cookies

## Troubleshooting

Common issues and solutions:

1. **Redirect Loops**: Ensure middleware properly detects and breaks redirect loops
2. **Cookie Synchronization**: Make sure cookies are properly synchronized between browser and server
3. **Session Validation**: Always validate both user and session objects
4. **Loading States**: Handle loading states properly to prevent UI flickering

## References

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Next.js Middleware Documentation](https://nextjs.org/docs/app/building-your-application/routing/middleware)
- [Supabase SSR Package](https://supabase.com/docs/guides/auth/server-side/nextjs)
