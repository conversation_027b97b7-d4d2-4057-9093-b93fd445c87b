import { NextRequest, NextResponse } from "next/server";
import { isAdmin } from "../../../../lib/auth";
import { UserRole } from "../../../../types/roles";
import { getUsers } from "../../../../app/admin/actions";
// Remove Clerk import as we're using Supabase for auth
// import { createClerkClient } from "@clerk/nextjs/server";
import { z } from "zod";

// Query params validation schema
const searchParamsSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(10),
  orderBy: z.enum(['created_at', 'last_active', 'email']).optional(),
  orderDir: z.enum(['asc', 'desc']).optional()
});

/**
 * GET handler for user search
 */
export async function GET(request: NextRequest) {
  // Check if the current user is an admin
  if (!(await isAdmin())) {
    return NextResponse.json(
      { success: false, message: "Unauthorized access" },
      { status: 403 }
    );
  }

  try {
    const searchParams = request.nextUrl.searchParams;

    // Parse and validate query parameters
    const validatedParams = searchParamsSchema.parse({
      search: searchParams.get('search') || undefined,
      page: searchParams.get('page') || 1,
      limit: searchParams.get('limit') || 10,
      orderBy: searchParams.get('orderBy') || undefined,
      orderDir: searchParams.get('orderDir') || undefined
    });

    // Call our server action to handle the actual logic
    const result = await getUsers({
      query: validatedParams.search,
      page: validatedParams.page,
      limit: validatedParams.limit,
      orderBy: validatedParams.orderBy || 'created_at',
      orderDir: validatedParams.orderDir || 'desc'
    });

    return NextResponse.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error("Error in users API route:", error);

    // Handle validation errors specifically
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid parameters",
          errors: error.errors?.map(e => ({ path: e.path, message: e.message })) || []
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, message: "An error occurred processing your request" },
      { status: 500 }
    );
  }
}

/**
 * POST handler for bulk user operations
 */
export async function POST(request: NextRequest) {
  // Check if the current user is an admin
  if (!(await isAdmin())) {
    return NextResponse.json(
      { success: false, message: "Unauthorized access" },
      { status: 403 }
    );
  }

  try {
    const data = await request.json();

    // Validate operation type
    if (!data.operation || typeof data.operation !== 'string') {
      return NextResponse.json(
        { success: false, message: "Missing or invalid operation" },
        { status: 400 }
      );
    }

    // Handle different bulk operations
    switch (data.operation) {
      case 'export':
        // Handle export operation (e.g., generate CSV of users)
        return NextResponse.json({
          success: true,
          message: "Export started, you will receive an email when ready",
        });

      case 'bulk_role_change':
        // Validate bulk role change parameters
        if (!data.userIds || !Array.isArray(data.userIds) || !data.role) {
          return NextResponse.json(
            { success: false, message: "Missing required parameters: userIds and role" },
            { status: 400 }
          );
        }

        // Handle bulk role changes
        // This would normally use a queue for large sets
        // For example: await queueBulkRoleChange(data.userIds, data.role);

        return NextResponse.json({
          success: true,
          message: `Role change operation queued for ${data.userIds.length} users`
        });

      default:
        return NextResponse.json(
          { success: false, message: `Unknown operation: ${data.operation}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error in users API POST route:", error);
    return NextResponse.json(
      { success: false, message: "An error occurred processing your request" },
      { status: 500 }
    );
  }
}