-- Add capacity and registration settings to events table
-- Migration: 0019_add_event_capacity_and_registration_settings.sql

-- Start transaction for atomic migration
BEGIN;

-- Add total capacity field to events table
ALTER TABLE events ADD COLUMN IF NOT EXISTS total_capacity INTEGER;

-- Add registration closing date field to events table
ALTER TABLE events ADD COLUMN IF NOT EXISTS registration_close_date TIMESTAMP WITH TIME ZONE;

-- Add category-specific closing dates toggle
ALTER TABLE events ADD COLUMN IF NOT EXISTS allow_category_specific_closing_dates BOOLEAN DEFAULT false;

-- Add T-shirt options field to events table
ALTER TABLE events ADD COLUMN IF NOT EXISTS tshirt_options JSONB DEFAULT '{
  "enabled": false,
  "sizes": ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
  "description": "",
  "size_chart_image": null
}'::jsonb;

-- Add comment for documentation
COMMENT ON COLUMN events.total_capacity IS 'Total capacity for the entire event';
COMMENT ON COLUMN events.registration_close_date IS 'Default registration closing date for all categories';
COMMENT ON COLUMN events.allow_category_specific_closing_dates IS 'Whether to allow different closing dates for each category';
COMMENT ON COLUMN events.tshirt_options IS 'Configuration for event T-shirt options including available sizes';

-- Force schema cache reload
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('0019_add_event_capacity_and_registration_settings', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit transaction
COMMIT;
