# Currency Formatting

This document explains how currency formatting works in the Fuiyoo application.

## Overview

The application supports displaying prices in the correct currency format based on the event's country. This includes:

- Using the appropriate currency symbol (e.g., $, £, €, RM)
- Placing the symbol in the correct position (prefix or suffix)
- Using the correct decimal and thousands separators
- Handling special cases for currencies without decimal places

## Implementation

The currency formatting is implemented in `src/lib/utils/currency-utils.ts` and provides the following functions:

- `formatCurrency(price, countryCode)`: Formats a price according to the country's currency format
- `formatPriceRange(minPrice, maxPrice, countryCode)`: Formats a price range with the correct currency format
- `getCurrencyInfo(countryCode)`: Gets currency information for a specific country

## Supported Countries and Currencies

The system supports many countries and currencies, including:

| Country | Currency Code | Symbol | Example |
|---------|--------------|--------|---------|
| Malaysia | MYR | RM | RM10.00 |
| United States | USD | $ | $10.00 |
| United Kingdom | GBP | £ | £10.00 |
| European Union | EUR | € | 10,00 € |
| Japan | JPY | ¥ | ¥1,000 |
| Australia | AUD | A$ | A$10.00 |
| Singapore | SGD | S$ | S$10.00 |
| Canada | CAD | C$ | C$10.00 |
| China | CNY | ¥ | ¥10.00 |
| India | INR | ₹ | ₹10.00 |
| Brazil | BRL | R$ | R$10,00 |
| South Africa | ZAR | R | R10.00 |
| Thailand | THB | ฿ | ฿10.00 |
| Indonesia | IDR | Rp | Rp10.000 |
| Philippines | PHP | ₱ | ₱10.00 |
| Vietnam | VND | ₫ | 10.000 ₫ |
| New Zealand | NZD | NZ$ | NZ$10.00 |

## Usage

### In Components

```typescript
import { formatCurrency, formatPriceRange } from '@/lib/utils/currency-utils';

// Format a single price
const formattedPrice = formatCurrency(10.99, 'MY'); // Returns "RM10.99"

// Format a price range
const priceRange = formatPriceRange(10.99, 19.99, 'MY'); // Returns "RM10.99 - RM19.99"
```

### In the Event Preview

The event preview uses the country code from the event's form data to format prices:

```typescript
// Format price based on event country
const price = formatCurrency(category.price, formData.country);
```

## Adding New Currencies

To add support for a new currency, update the `CURRENCY_MAP` in `src/lib/utils/currency-utils.ts`:

```typescript
'XX': {
  code: 'XYZ',
  symbol: '¤',
  position: 'prefix', // or 'suffix'
  decimalSeparator: '.',
  thousandsSeparator: ','
}
```

## Special Cases

Some currencies have special formatting requirements:

1. **No Decimal Places**: Currencies like JPY, KRW, VND, and IDR typically don't use decimal places
2. **Symbol Position**: Some currencies place the symbol before the amount (prefix), while others place it after (suffix)
3. **Separators**: Different countries use different characters for decimal and thousands separators
