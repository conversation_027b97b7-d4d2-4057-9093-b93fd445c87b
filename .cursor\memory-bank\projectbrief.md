# Project Brief

## Project Overview
The project, now named Fuiyoo (formerly Fuiyoh), involves enhancing an existing event management platform with two key features:
1. A user promotion flow that allows regular users to apply to become event organizers
2. An admin dashboard with comprehensive RBAC for managing users, events, and platform settings

## Core Requirements

### User Promotion Flow
- Create an Organizations tab in the user dashboard
- Implement a multi-step organization registration form based on provided design
- Make the application process resumable for users who don't complete it in one session
- Store application data securely with proper validation
- Show notifications for incomplete applications
- Allow users to continue from where they left off

### Admin Dashboard
- Create a protected admin area with proper RBAC
- Implement user management with role controls
- Build an approval workflow for organization applications
- Create event management capabilities
- Add platform analytics and reporting
- Implement RBAC settings management

## Technical Requirements
- Implement Clerk RBAC with three roles: admin, user, and event organizer
- Follow Next.js best practices with App Router
- Create reusable components for consistent UX
- Implement proper form validation with Zod
- Store application data in TURSO database
- Follow Tailwind and Shadcn UI design patterns

## Project Goals
- Improve user experience by providing a clear path to becoming an event organizer
- Enhance platform management capabilities for administrators
- Ensure proper access control throughout the platform
- Create a secure and user-friendly application process
- Implement a comprehensive admin dashboard

## Project Scope
- User Management System
  - Public user registration and authentication
  - Organizer application and verification process
  - Document submission and approval workflow
  - Role-based access control

- Event Management System
  - Event creation wizard with type-specific fields
  - Ticket management and pricing
  - Participant data management
  - QR Code check-in system
  - Goody bag distribution tracking

- Attendee Features
  - Event discovery and registration
  - Secure payment processing
  - E-ticket system with QR codes
  - Attendance and collection history

- Organizer Features
  - Organization profile management
  - Document submission portal
  - Event publishing workflow
  - Revenue collection and tracking
  - Attendance scanning tools
  - Distribution management

- Admin Features
  - Tenant management
  - Organizer verification
  - Document review system
  - Event moderation
  - Revenue tracking

- Technical Implementation
  - Next.js frontend with App Router
  - TURSO database with Drizzle ORM
  - Clerk authentication
  - Edge-first deployment

## User Tiers
1. Public Users (Attendees)
   - Register and login to platform
   - Browse and purchase event tickets
   - Receive QR code-based e-tickets
   - Track attendance and collections
   - View purchase history

2. Event Organizers
   - Start as public users
   - Submit organization verification documents
   - Manage organization profile
   - Create and publish events
   - Track ticket sales and revenue
   - Manage event check-ins
   - Handle goody bag distribution

3. Platform Administrators
   - Review organizer applications
   - Verify submitted documents
   - Manage user roles and permissions
   - Monitor platform activities
   - Handle revenue distribution

## Success Criteria
- Number of events created per month
- Tickets sold per event type
- Organizer retention rate after first event
- Zero errors in logistics data exports
- Platform uptime and performance metrics
- Revenue targets through 6% platform fee
- Organizer verification turnaround time
- Attendee satisfaction metrics

## Timeline
- Project Start Date: [Date]
- Major Milestones: [To be defined based on sprint planning]

## Stakeholders
- Event Organizers (NGOs, companies, schools, event agencies)
- Event Attendees (Public users)
- Platform Administrators (Super admins and tenant admins)

## Key Decisions
- Using Next.js App Router for frontend architecture
- Implementing multitenant support via subdomains
- Using TURSO (edge SQLite) for database
- Implementing Clerk for authentication
- Setting platform fee at 6% or RM2.50 minimum
- Requiring document verification for organizers
- Using QR codes for attendance tracking

## Notes
- Project has been renamed from Fuiyoh to Fuiyoo
- This document serves as the foundation for all other Memory Bank files
- Update this document when core project requirements or scope changes 