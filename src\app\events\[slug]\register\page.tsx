import React from 'react';
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { EventRepository } from '@/repositories/event-repository';
import { EventCategoryRepository } from '@/repositories/event-category-repository';
import { createClient } from '@/lib/supabase/server';
import { formatDate } from '@/utils/formatDate';
import { formatCurrency } from '@/lib/utils/currency-utils';

interface EventRegistrationProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function EventRegistrationPage({ params }: EventRegistrationProps) {
  // Await params to fix the "params should be awaited" error in Next.js 14+
  const { slug } = await params;

  // Fetch the event from the database
  const eventRepository = new EventRepository();
  const event = await eventRepository.getEventBySlug(slug);

  if (!event || event.status !== 'published') {
    notFound();
  }

  // Check if registration is open
  const now = new Date();
  const registrationCloseDate = event.registrationCloseDate ? new Date(event.registrationCloseDate) : null;

  if (registrationCloseDate && now > registrationCloseDate) {
    // Registration is closed
    return (
      <div className="container max-w-4xl mx-auto px-4 py-12">
        <Link href={`/events/${slug}`} className="flex items-center text-sm text-muted-foreground hover:text-foreground mb-8">
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to Event
        </Link>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Registration Closed</CardTitle>
            <CardDescription>
              Registration for this event has ended on {formatDate(event.registrationCloseDate)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-6">
              The registration period for "{event.title}" has ended. Please contact the event organizer for more information.
            </p>
            <Button asChild>
              <Link href={`/events/${slug}`}>
                Return to Event Page
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Fetch event categories separately
  const categoryRepository = new EventCategoryRepository();
  const eventCategories = await categoryRepository.getCategoriesByEventId(event.id);

  console.log(`Loaded ${eventCategories.length} categories for registration of event ${event.title}:`,
    eventCategories.map(cat => ({
      id: cat.id,
      name: cat.name,
      price: cat.properties?.price
    }))
  );

  // Check if user is authenticated
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  // If not authenticated, redirect to sign in
  if (!user) {
    // Store the current URL to redirect back after sign in
    redirect(`/sign-in?redirect=/events/${slug}/register`);
  }

  return (
    <div className="container max-w-4xl mx-auto px-4 py-12">
      <Link href={`/events/${slug}`} className="flex items-center text-sm text-muted-foreground hover:text-foreground mb-8">
        <ArrowLeft className="w-4 h-4 mr-1" />
        Back to Event
      </Link>

      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Register for {event.title}</h1>
        <p className="text-muted-foreground">
          {registrationCloseDate ? (
            <>Registration closes on {formatDate(event.registrationCloseDate)}</>
          ) : (
            <>Registration is currently open</>
          )}
        </p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Select Ticket Category</CardTitle>
          <CardDescription>
            Choose the ticket category you want to register for
          </CardDescription>
        </CardHeader>
        <CardContent>
          {eventCategories.length > 0 ? (
            <div className="space-y-4">
              {eventCategories.map((category: any) => {
                const isAvailable = !category.properties?.registrationLimit ||
                  (category.properties.registrationCount < category.properties.registrationLimit);

                return (
                  <div
                    key={category.id}
                    className={`border border-border rounded-lg p-4 bg-card ${!isAvailable ? 'opacity-60' : ''}`}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium text-card-foreground">{category.name}</h3>
                      {category.properties?.price ? (
                        <span className="font-medium text-card-foreground">
                          {formatCurrency(parseFloat(category.properties.price), event.country)}
                        </span>
                      ) : (
                        <span className="text-green-600 font-medium">Free</span>
                      )}
                    </div>

                    {category.description && (
                      <p className="text-sm text-muted-foreground mb-2">{category.description}</p>
                    )}

                    {category.properties?.registrationLimit && (
                      <p className="text-xs text-muted-foreground mb-3">
                        {category.properties.registrationCount || 0} / {category.properties.registrationLimit} spots filled
                      </p>
                    )}

                    <Button
                      className="w-full"
                      disabled={!isAvailable}
                      asChild
                    >
                      <Link href={`/events/${slug}/register/${category.id}`}>
                        {isAvailable ? 'Select' : 'Sold Out'}
                      </Link>
                    </Button>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No ticket categories available for this event.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
