# Supabase Authentication Configuration Guide

This document provides a comprehensive guide for configuring Supabase authentication in the Fuiyoo application, with a focus on ensuring proper redirect URLs across all environments.

## Environment URLs

The application supports three environments:

1. **Development**: `http://localhost:3000`
2. **Staging**: `https://staging--fuiyoo.netlify.app`
3. **Production**: `https://fuiyoo.netlify.app`

## Supabase Dashboard Configuration

### Site URL

In the Supabase dashboard, set the Site URL to the production URL:

```
https://fuiyoo.netlify.app
```

### Redirect URLs

Add the following URLs to the "Additional Redirect URLs" section:

```
http://localhost:3000/**
https://staging--fuiyoo.netlify.app/**
https://fuiyoo.netlify.app/**
```

The wildcard (`**`) ensures that all paths under these domains are allowed as redirect targets.

## Environment Variables

Ensure the following environment variables are set correctly for each environment:

### Development (.env.local)

```
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_AUTH_REDIRECT_URL=http://localhost:3000/auth/callback
```

### Staging (.env.staging)

```
NEXT_PUBLIC_SITE_URL=https://staging--fuiyoo.netlify.app
NEXT_PUBLIC_AUTH_REDIRECT_URL=https://staging--fuiyoo.netlify.app/auth/callback
```

### Production (.env.production)

```
NEXT_PUBLIC_SITE_URL=https://fuiyoo.netlify.app
NEXT_PUBLIC_AUTH_REDIRECT_URL=https://fuiyoo.netlify.app/auth/callback
```

## Authentication Flow

The authentication flow works as follows:

1. User initiates sign-in (email/password, OAuth, etc.)
2. Supabase redirects to the callback URL (`/auth/callback`)
3. The callback route exchanges the auth code for a session
4. The user is redirected to their intended destination

## Preventing Cross-Environment Redirects

To prevent issues with cross-environment redirects (e.g., redirecting from production to localhost), the application implements several safeguards:

1. The `getAuthCallbackUrl()` function in `src/utils/url-utilities.ts` ensures the callback URL is appropriate for the current environment.
2. The `fixCrossEnvironmentRedirect()` function detects and fixes cross-environment redirects.
3. The auth callback route checks for and corrects localhost redirects in production.

## Troubleshooting

If you encounter issues with authentication redirects:

1. **Check Supabase Dashboard Configuration**: Ensure the Site URL and Redirect URLs are correctly set.
2. **Verify Environment Variables**: Make sure the environment variables are correctly set for each environment.
3. **Clear Browser Cookies**: Authentication issues can sometimes be resolved by clearing cookies.
4. **Check Console Logs**: Look for logs related to authentication and redirects to identify the issue.

### Common Issues

#### Redirecting to localhost in production

If users are being redirected to localhost after authentication in production:

1. Ensure the `NEXT_PUBLIC_SITE_URL` and `NEXT_PUBLIC_AUTH_REDIRECT_URL` are correctly set in the production environment.
2. Check that the Supabase dashboard has the correct Site URL.
3. Verify that the `getAuthCallbackUrl()` function is returning the correct URL.

#### Authentication fails with "Invalid redirect URL"

This usually means the redirect URL is not in the allowed list in the Supabase dashboard. Add the URL to the "Additional Redirect URLs" section.

## Testing Authentication

To test authentication:

1. Sign out completely and clear cookies
2. Try signing in with different methods (email/password, Google, etc.)
3. Verify that you are redirected to the correct URL after authentication
4. Check that the session is properly established

## References

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Supabase Redirect URLs](https://supabase.com/docs/guides/auth/redirect-urls)
- [Next.js Authentication](https://nextjs.org/docs/authentication)
