{"extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-debugger": "warn", "prefer-const": "warn", "no-var": "error", "object-shorthand": "warn"}, "ignorePatterns": ["node_modules/", ".next/", "out/", "public/", "src/components/events/event-wizard/wizard-container.tsx", "src/lib/error-utils.ts", "src/lib/supabase/types.ts", "src/types/event-types.ts", "src/types/event.ts"]}