"use client";

import React from 'react';
import { ToastProvider } from '@/components/ui/use-toast';
import { AuthProvider } from '@/contexts/auth-context';
import { CookieConsentProvider } from '@/contexts/cookie-consent-context';
import { ThemeProvider } from '@/components/theme/theme-provider';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <AuthProvider>
        <CookieConsentProvider>
          <ToastProvider>
            {children}
          </ToastProvider>
        </CookieConsentProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}