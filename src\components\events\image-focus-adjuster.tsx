'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { MoveVertical, Check, X } from 'lucide-react';
import Image from 'next/image';

interface ImageFocusAdjusterProps {
  imageUrl: string;
  isOpen: boolean;
  onClose: () => void;
  onSave: (focusPoint: { y: number }) => void;
  initialFocusPoint?: { y: number };
}

export function ImageFocusAdjuster({
  imageUrl,
  isOpen,
  onClose,
  onSave,
  initialFocusPoint = { y: 50 }
}: ImageFocusAdjusterProps) {
  const [focusPoint, setFocusPoint] = useState(initialFocusPoint);
  const containerRef = useRef<HTMLDivElement>(null);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Reset focus point when dialog opens
  useEffect(() => {
    if (isOpen) {
      setFocusPoint(initialFocusPoint);
      setImageLoaded(false);
    }
  }, [isOpen, initialFocusPoint]);

  // Handle image load
  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    setImageLoaded(true);
  };

  // Handle vertical position change
  const handleVerticalChange = (value: number[]) => {
    if (value.length > 0 && value[0] !== undefined) {
      setFocusPoint({ y: value[0] });
    }
  };

  // Save focus point and close dialog
  const handleSave = () => {
    onSave(focusPoint);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Adjust Cover Image Vertical Position</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div
            ref={containerRef}
            className="relative w-full h-[300px] overflow-hidden rounded-md border"
          >
            {imageUrl && (
              <div
                className="absolute inset-0 transition-transform duration-200 w-full"
                style={{
                  objectPosition: `center ${focusPoint.y}%`
                }}
              >
                <Image
                  src={imageUrl}
                  alt="Cover image"
                  fill
                  className="object-cover"
                  style={{
                    objectPosition: `center ${focusPoint.y}%`
                  }}
                  onLoad={handleImageLoad}
                  priority
                />
              </div>
            )}

            {/* Rectangle focus indicator */}
            <div
              className="absolute w-full h-16 bg-transparent border-2 border-white pointer-events-none"
              style={{
                top: `calc(${focusPoint.y}% - 32px)`,
                boxShadow: '0 0 4px rgba(0,0,0,0.7)'
              }}
            />
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center">
                <MoveVertical className="w-5 h-5 mr-2" />
                <span className="text-sm font-medium">Vertical Position: {focusPoint.y}%</span>
              </div>
              <Slider
                value={[focusPoint.y]}
                min={0}
                max={100}
                step={1}
                onValueChange={handleVerticalChange}
              />
            </div>
          </div>

          <div className="bg-muted/50 p-4 rounded-md">
            <p className="text-sm text-muted-foreground">
              Use the slider to adjust the vertical position of your cover image.
              The rectangle shows the area that will be centered when displayed at different sizes.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleSave}>
            <Check className="w-4 h-4 mr-2" />
            Save Position
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
