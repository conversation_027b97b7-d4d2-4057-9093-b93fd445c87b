'use client'

import { createBrowserClient } from '@supabase/ssr'
import { type Database } from './types'

/**
 * Create Supabase client for browser-side database operations and authentication
 * This implementation uses a singleton pattern to avoid creating multiple clients
 */
let supabaseInstance: ReturnType<typeof createBrowserClient<Database>> | null = null;

export function createClient() {
  if (supabaseInstance) return supabaseInstance;

  // Validate environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    const missingVars = [];
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!supabaseKey) missingVars.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');

    const errorMessage = `Missing Supabase credentials: ${missingVars.join(', ')}. Please check your environment variables.`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Create and cache the client
  try {
    console.log('Creating Supabase browser client with URL:', supabaseUrl);
    supabaseInstance = createBrowserClient<Database>(supabaseUrl, supabaseKey);
    console.log('Supabase client created successfully');
    return supabaseInstance;
  } catch (error) {
    console.error('Error creating Supabase client:', error);
    throw error;
  }
}

// Export a singleton instance
export const supabase = createClient();
export default supabase;