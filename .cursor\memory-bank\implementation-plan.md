# Implementation Plan for User Promotion and Admin Dashboard

## Overview
This plan outlines the implementation of two key features for the platform:
1. User promotion flow to become event organizers
2. Admin dashboard with RBAC integration

## Implementation Phases

| Phase | Task | Description | Dependencies |
|-------|------|-------------|--------------|
| 1 | Set up RBAC Infrastructure | Implement Clerk RBAC with admin, user, and event organizer roles | None |
| 2 | Create User Dashboard Organizations Tab | Build UI for organizations tab in user dashboard | Phase 1 |
| 3 | Create Organization Promotion Application Form | Multi-step form with progress saving | Phase 2 |
| 4 | Implement Application Resumability | Allow users to continue incomplete applications | Phase 3 |
| 5 | Create Admin Dashboard Basic Structure | Set up protected routes and layout for admin panel | Phase 1 |
| 6 | Implement User Management in Admin | Create user listing, search, and RBAC controls | Phase 5 |
| 7 | Build Promotion Approval Workflow | Create approval UI for organization applications | Phase 3, 6 |
| 8 | Implement Event Management | Create event CRUD operations in admin | Phase 5 |
| 9 | Add Platform Analytics | Build analytics dashboard for admins | Phase 5, 8 |
| 10 | Final Testing & Integration | Test all workflows and fix issues | All previous phases |

## Detailed Implementation Plan

### Phase 1: Set up RBAC Infrastructure
1. Create TypeScript definitions for user roles
   - Define enum for roles (admin, user, event_organizer)
   - Set up TypeScript interfaces for role metadata

2. Implement Clerk metadata for role storage
   - Configure Clerk to store role information in user metadata
   - Set up initial role assignment for new users

3. Create utility functions for role checking
   - Implement `checkRole()` helper function
   - Create role-based redirection utilities

4. Set up middleware for route protection
   - Configure Next.js middleware for protected routes
   - Implement role-based route protection

### Phase 2: Create User Dashboard Organizations Tab
1. Create organizations tab UI in user dashboard
   - Build navigation component for the tab
   - Implement tab content layout

2. Check user role status
   - Fetch and display current role information
   - Show appropriate UI based on role

3. Implement "Upgrade to Event Organizer" CTA
   - Design CTA component for regular users
   - Add click handler to initiate application process

4. Create logic for existing organizers
   - Show organization details for users with event_organizer role
   - Implement organization profile view

### Phase 3: Create Organization Promotion Application Form
1. Design multi-step form layout
   - Create UI based on the provided image
   - Implement step navigation
   - use supabase MCP tools to create two storage bucket(images, documents)
   - use the images bucket to store profile image, organisation logo, event poster and event photo gallery with intelligent filename prefix
   - use the documents bucket to store organisation documents, receipts and other document with intelligent filename also

2. Group related fields together
   - Organize form into logical sections:
     - Company Information
     - Contact Details
     - Payment Information
     - File Uploads

3. Implement form validation
   - Create Zod schemas for each form section
   - Implement client-side validation

4. Create form state management
   - Implement progress tracking
   - Set up form context for data persistence

5. Build API endpoints for saving
   - Create server actions for form submission
   - Implement database schema for applications

### Phase 4: Implement Application Resumability
1. Create database schema for incomplete applications
   - Design schema to store partial application data
   - Implement status tracking for applications

2. Build notification component
   - Create UI for showing incomplete application notices
   - Implement dashboard notification system

3. Implement "Continue Application" flow
   - Create button and navigation logic
   - Load partial data into form

4. Ensure data persistence
   - Implement auto-save functionality
   - Create API endpoints for progress updates

### Phase 5: Create Admin Dashboard Basic Structure
1. Create protected `/admin` route
   - Set up route with admin-only access
   - Implement middleware protection

2. Implement admin dashboard layout
   - Create responsive layout components
   - Build header and navigation components

3. Build sidebar navigation
   - Create navigation for different admin sections:
     - User Management
     - Application Approvals
     - Event Management
     - Analytics
     - Settings

4. Set up role-based protection
   - Implement route guards for admin-only sections
   - Create permission-based UI rendering

### Phase 6: Implement User Management in Admin
1. Create user listing page
   - Implement table/grid view of users
   - Add search and filtering functionality

2. Build user profile view
   - Create detailed user information page
   - Implement edit functionality

3. Implement role management UI
   - Create interface for changing user roles
   - Implement role modification controls

4. Create server actions
   - Implement API endpoints for role updates
   - Add audit logging for role changes

### Phase 7: Build Promotion Approval Workflow
1. Create pending applications listing
   - Build UI for viewing pending organization applications
   - Implement sorting and filtering

2. Implement application review interface
   - Create detailed view of submitted applications
   - Build UI for reviewing form data and uploads

3. Build approval/rejection flow
   - Implement approval controls and process
   - Create rejection process with reason entry

4. Create notification system
   - Implement email notifications for status changes
   - Add in-app notifications for applicants

### Phase 8: Implement Event Management
1. Create event listing
   - Build admin view of all platform events
   - Implement search and filtering

2. Build event detail view
   - Create detailed event information page
   - Implement event approval controls

3. Implement event status management
   - Add controls for changing event status
   - Create event moderation functionality

4. Create event analytics
   - Build metrics view for individual events
   - Implement performance tracking

### Phase 9: Add Platform Analytics
1. Implement user growth metrics
   - Create charts for user acquisition
   - Build user engagement analytics

2. Create event performance analytics
   - Implement event success metrics
   - Build attendance and engagement tracking

3. Build organization activity dashboard
   - Create organization performance metrics
   - Implement activity tracking

4. Implement custom reporting
   - Build report generation tools
   - Create export functionality

### Phase 10: Final Testing & Integration
1. Test all user flows
   - Verify application submission process
   - Test approval workflows
   - Validate event management

2. Ensure proper role-based access
   - Test access restrictions
   - Verify permission-based UI rendering

3. Fix usability issues
   - Address any UX problems
   - Improve responsive behavior

4. Optimize performance
   - Implement performance optimizations
   - Reduce load times and improve responsiveness 