/**
 * Role definitions for the application
 */

export enum UserRole {
  PUBLIC = 'public',
  USER = 'user',
  EVENT_ORGANIZER = 'event_organizer',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

export interface RoleMetadata {
  role: UserRole;
  createdAt?: string;
  updatedAt?: string;
}

export interface OrganizationApplication {
  id: string;
  userId: string;
  status: 'draft' | 'submitted' | 'approved' | 'rejected';
  data: OrganizationApplicationData;
  createdAt: string;
  updatedAt: string;
  submittedAt: string | null;
  reviewedAt: string | null;
  reviewedBy: string | null;
  rejectionReason: string | null;
}

export interface OrganizationApplicationData {
  // Company Information
  organizationName: string;
  organizationType: string;
  description: string;
  website?: string | undefined;
  logo?: string | undefined;

  // Contact Details
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  } | undefined;

  // Event Experience
  experienceDescription?: string | undefined;
  pastEvents?: {
    name: string;
    date: string;
    attendees: number;
    description: string;
    photos?: string[] | undefined;
  }[] | undefined;

  // Documents
  documents?: {
    businessRegistration?: string | undefined;
    taxDocuments?: string | undefined;
    identificationDocument?: string | undefined;
    otherDocuments?: string[] | undefined;
  } | undefined;

  // Completed form steps
  completedSteps: string[] | undefined;
}