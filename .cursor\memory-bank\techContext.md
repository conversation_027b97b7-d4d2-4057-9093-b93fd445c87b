# Technology Context

## Core Technologies

### Frontend
- **Framework**: Next.js 15.3.0 (App Router)
- **Language**: TypeScript 5.3.x
- **UI Library**: React 19.1.0
- **State Management**: React Context API for global state, useState and useReducer for component state
- **Styling**: Tailwind CSS 4.1.4 with custom theme configuration and mask utilities
- **Component Library**: Custom components built with Radix UI primitives (shadcn)
- **Class Utilities**: clsx and tailwind-merge for dynamic class name management
- **Form Handling**: React Hook Form with Zod validation
- **Icons**: Lucide React for consistent icon system
- **Animations**: Framer Motion for UI animations

### Backend
- **Authentication**: Supabase Auth with Google OAuth
- **Database**: Supabase (PostgreSQL)
- **API Layer**: Next.js Server Actions and Route Handlers
- **Storage**: Supabase Storage
- **Image Optimization**: Next.js Image component with Supabase storage

### Infrastructure
- **Hosting**: Netlify with Edge Functions
- **CI/CD**: Netlify CI/CD pipelines
- **Environment**: Node.js 18+

## Package Management
- **Package Manager**: pnpm (preferred over npm for better performance and dependency management)
- **Version Control**: Git with GitHub
- **Monorepo Structure**: Not applicable (single package)

## Development Setup
- **Editor**: VS Code with recommended extensions
- **Linting**: ESLint with Next.js configuration
- **Formatting**: Prettier
- **Testing**: Jest for unit tests, Playwright for E2E (planned)
- **Build Tool**: Next.js build system

## Technical Constraints
- **Performance**: Must maintain Core Web Vitals scores in green range
- **Accessibility**: Must be WCAG 2.1 AA compliant
- **Browser Support**: Modern browsers only (2 latest versions)
- **Deployment**: Edge runtime optimization
- **SEO**: Must be fully crawlable and indexable
- **Mobile Support**: Must be fully responsive with mobile-first design

## Dependencies
- **UI Components**:
  - @radix-ui/react-dialog: 1.1.7 (for modal dialogs)
  - @radix-ui/react-tooltip: 1.0.7 (for tooltips)
  - clsx: 2.1.1 (for conditional class names)
  - tailwind-merge: 2.6.0 (for merging Tailwind classes)
  - lucide-react: 0.487.0 (for icon system)
  - framer-motion: 11.0.3 (for animations)
  - sonner: 1.4.0 (for toast notifications)
- **Authentication**:
  - @supabase/auth-helpers-nextjs
  - @supabase/supabase-js
- **Database**:
  - @supabase/ssr
  - @supabase/supabase-js
- **Form Handling**:
  - react-hook-form
  - zod
- **Utilities**:
  - date-fns
  - nanoid
  - zustand (for state management)
  - next-themes (for theme management)

## API Integrations
- **Authentication**: Supabase Auth API (OAuth, Email)
- **Database**: Supabase API
- **Storage**: Supabase Storage API
- **Analytics**: Google Analytics (planned)
- **Payment**: Stripe (planned)
- **Email**: SendGrid (planned)

## Deployment Environments
- **Development**: Local development environment
- **Staging**: Staging environment (branch: staging)
- **Production**: Production environment at fuiyoo.netlify.app

## Tools and Services
- **Version Control**: GitHub
- **Issue Tracking**: GitHub Issues
- **Documentation**: Markdown files in the repository
- **API Documentation**: API endpoints documented in code comments

## Optimization Strategies
- **Code Splitting**: Automatic code splitting with Next.js
- **Image Optimization**: Using Next.js Image component
- **Caching**: Using SWR for data fetching cache
- **Edge Functions**: Using Netlify Edge Functions for API routes
- **Mask Utilities**: Using Tailwind 4 mask utilities for UI enhancements
- **HSL Color Notation**: Using explicit HSL notation for better theming support
- **React 19 Features**: Leveraging React 19 improvements for better performance

## Security Considerations
- **Authentication**: JWT-based authentication with Supabase Auth
- **Authorization**: Role-based access control (RBAC)
- **Data Protection**: HTTPS for all communications
- **Input Validation**: Zod schema validation for all inputs
- **CSRF Protection**: Built-in with Next.js
- **XSS Protection**: React's built-in XSS protection
- **Rate Limiting**: Implemented at the API level