'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { signOut } from '@/lib/supabase/auth'
import { getUserAvatar } from '@/utils/imageHandling'
import { LogOut, Settings, User as UserIcon, Moon, Sun, Monitor } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useAuth } from '@/contexts/auth-context'

type User = {
  id: string
  auth_user_id?: string
  first_name: string
  last_name: string | null
  email: string
  avatar: string | null
}

export function UserAvatarMenu({ user, loading = false }: { user: User, loading?: boolean }) {
  const [isOpen, setIsOpen] = useState(false)
  const [isSigningOut, setIsSigningOut] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const { theme, setTheme } = useTheme()
  const { refreshSession } = useAuth()

  // Ensure we have a valid user object
  const validUser = user && (user.email || user.first_name) ? user : null

  // Close menu when clicking outside - moved before early return
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Debug logging - only in development mode
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('UserAvatarMenu props:', {
        userId: user?.id,
        loading,
        hasValidUser: !!validUser,
        avatarUrl: validUser?.avatar || 'none'
      })
    }
  }, [user, loading, validUser])

  // Force a session refresh if we don't have a valid user
  useEffect(() => {
    if (!validUser && !loading) {
      if (process.env.NODE_ENV === 'development') {
        console.log('No valid user in UserAvatarMenu, refreshing session')
      }
      refreshSession()
    }
  }, [validUser, loading, refreshSession])

  // If we don't have a valid user, show a loading state
  if (!validUser) {
    if (process.env.NODE_ENV === 'development') {
      console.log('No valid user in UserAvatarMenu, showing loading state')
    }
    return <div className="h-9 w-9 rounded-full bg-gray-200 animate-pulse"></div>
  }

  const toggleMenu = () => {
    setIsOpen(!isOpen)
  }

  const handleSignOut = async () => {
    try {
      // Close the menu first
      setIsOpen(false)

      // Set signing out state
      setIsSigningOut(true)

      // Call the signOut function directly
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
      // Redirect to home page even if there's an error
      window.location.href = '/'
    } finally {
      setIsSigningOut(false)
    }
  }

  // No preloading or complex image loading logic

  return (
    <div className="relative" ref={menuRef}>
      {/* Avatar Button */}
      <button
        onClick={toggleMenu}
        className="relative h-9 w-9 rounded-full overflow-hidden cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary"
        aria-label="Open user menu"
        aria-expanded={isOpen}
        aria-controls="user-menu"
      >
        {validUser?.avatar ? (
          <div className="relative h-full w-full">
            <Image
              src={validUser.avatar}
              alt={`${validUser.first_name}'s profile`}
              fill
              sizes="36px"
              className="object-cover"
              data-testid="avatar-image"
              onError={(e) => {
                // If image fails to load, show initials instead
                e.currentTarget.style.display = 'none';
                const initialsEl = e.currentTarget.parentElement?.nextElementSibling;
                if (initialsEl && initialsEl instanceof HTMLElement) {
                  initialsEl.style.display = 'flex';
                }
              }}
            />
          </div>
        ) : null}

        {/* Initials as fallback or if no avatar */}
        <div
          className={`h-full w-full flex items-center justify-center bg-muted text-muted-foreground ${validUser?.avatar ? 'hidden' : ''
            }`}
          data-testid="avatar-initials"
        >
          {validUser?.first_name?.[0] || ''}{validUser?.last_name?.[0] || ''}
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          id="user-menu"
          className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-[hsl(var(--background))] border border-border z-50"
        >
          <div className="py-1">
            {/* User Info */}
            <div className="px-4 py-2 border-b border-border">
              <p className="text-sm font-medium">
                {validUser?.first_name || ''} {validUser?.last_name || ''}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {validUser?.email || ''}
              </p>
            </div>

            {/* Menu Items */}
            <Link
              href="/dashboard"
              className="flex items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
              onClick={() => setIsOpen(false)}
            >
              <UserIcon className="mr-2 h-4 w-4" />
              <span>Dashboard</span>
            </Link>

            <Link
              href="/dashboard/profile"
              className="flex items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
              onClick={() => setIsOpen(false)}
            >
              <UserIcon className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </Link>

            <Link
              href="/dashboard/settings"
              className="flex items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground"
              onClick={() => setIsOpen(false)}
            >
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </Link>

            {/* Theme Settings */}
            <div className="border-t border-border pt-1">
              <div className="px-4 py-2">
                <p className="text-xs font-medium text-muted-foreground mb-2">Theme</p>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => {
                      setTheme("light")
                      setIsOpen(false)
                    }}
                    className={`flex items-center justify-center p-2 rounded-md ${theme === "light" ? "bg-primary text-primary-foreground" : "hover:bg-accent hover:text-accent-foreground"
                      }`}
                    aria-label="Light theme"
                  >
                    <Sun className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => {
                      setTheme("dark")
                      setIsOpen(false)
                    }}
                    className={`flex items-center justify-center p-2 rounded-md ${theme === "dark" ? "bg-primary text-primary-foreground" : "hover:bg-accent hover:text-accent-foreground"
                      }`}
                    aria-label="Dark theme"
                  >
                    <Moon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => {
                      setTheme("system")
                      setIsOpen(false)
                    }}
                    className={`flex items-center justify-center p-2 rounded-md ${theme === "system" ? "bg-primary text-primary-foreground" : "hover:bg-accent hover:text-accent-foreground"
                      }`}
                    aria-label="System theme"
                  >
                    <Monitor className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            <div className="border-t border-border">
              <button
                onClick={handleSignOut}
                disabled={isSigningOut}
                className="flex w-full items-center px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground disabled:opacity-50"
              >
                {isSigningOut ? (
                  <>
                    <span className="mr-2 h-4 w-4 animate-spin">⟳</span>
                    <span>Signing out...</span>
                  </>
                ) : (
                  <>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
