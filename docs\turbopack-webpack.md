# Turbopack (Dev) and Webpack (Prod) Configuration Guide

This project uses Turbopack for development and Webpack for production builds. This document explains how they're configured and how to work with both environments.

## Development Environment (Turbopack)

Turbopack is used in development to provide faster refresh times and better developer experience.

### Configuration Files:

1. **`.turbopackrc.js`**: Contains Turbopack-specific configuration for development
2. **`next.config.js`**: Contains conditional Turbopack configuration

### Development Commands:

- `pnpm dev`: Start the development server with Turbopack
- `pnpm dev:turbo`: Explicitly use Turbopack for development
- `pnpm dev:turbo-max`: Use Turbopack with increased memory allocation
- `pnpm dev:turbo-debug`: Run Turbopack with additional debugging information
- `pnpm dev:standard`: Use the standard Next.js development server without Turbopack

### Development Features:

- Hot module replacement for faster refreshes
- Optimized for development speed
- Custom alias configuration for `@` to `src` directory
- Additional environment variables for development

## Production Environment (Webpack)

Webpack is used for production builds to ensure optimal bundling and performance.

### Configuration Files:

1. **`next.config.js`**: Contains Webpack configuration for production

### Production Commands:

- `pnpm build`: Standard production build
- `pnpm build:prod`: Explicit production build
- `pnpm build:netlify`: Production build configured for Netlify
- `pnpm build:analyze`: Production build with bundle analysis
- `pnpm build:force`: Force a clean production build
- `pnpm build:safe`: Safe production build with additional checks

### Production Features:

- Custom alias configuration for `@` to `src` directory
- Filesystem cache for faster subsequent builds
- Global object fix for "self is not defined" errors
- Optimized chunk splitting for better performance

## Switching Between Environments

When switching between development and production, you may want to clean your cache to ensure a fresh build:

```bash
./clean.sh
```

This script will clean:
- Next.js cache (`.next` directory)
- Node modules cache (`node_modules/.cache`)
- Turbopack cache (`.turbo` directory)

## Environment Detection

You can detect which environment you're in using the utility functions in `src/utils/env-helpers.ts`:

```typescript
import { isTurbopack, isWebpack, getBuildEnv } from '@/utils/env-helpers';

// Check if running in Turbopack (dev)
if (isTurbopack()) {
  // Development-only code
}

// Check if running in Webpack (prod)
if (isWebpack()) {
  // Production-only code
}

// Get the current environment name
console.log(`Current build environment: ${getBuildEnv()}`);
```

## Troubleshooting

### Common Issues in Development:

1. **Module not found errors**: 
   - Check that your import paths match the alias configuration
   - Restart the development server after adding new files

2. **Hot reload not working**:
   - Clear the Turbopack cache with `./clean.sh`
   - Check for syntax errors in your code

### Common Issues in Production:

1. **"self is not defined" errors**:
   - These should be fixed by the webpack configuration
   - If they persist, check browser-specific code

2. **Build timeouts**:
   - Use `pnpm build:turbo-max` to allocate more memory
   - Consider splitting large components into smaller ones

3. **Netlify-specific issues**:
   - Use `pnpm build:netlify` for Netlify-compatible builds
   - Check the Netlify configuration in `netlify.toml` 