import { Suspense } from 'react'
import { getCountries, type Country } from '@/data/countries'
import { ProfileForm } from './profile-form'
import { cache } from 'react'

interface SelectOption {
  value: string
  label: string
}

// Cache the countries data fetch to improve performance
const getCountriesData = cache(async () => {
  const countries = await getCountries()
  return countries.map((c: Country) => ({
    value: c.code,
    label: c.name,
    icon: c.flag
  }))
})

export default async function PersonalProfileEdit() {
  // Pre-fetch country data at the server level
  const countryOptions = await getCountriesData()

  return (
    <div className="space-y-6 pb-32">
      <div>
        <h1 className="text-2xl font-semibold mb-1">Edit Profile</h1>
        <p className="text-muted-foreground">Update your personal information and preferences</p>
      </div>

      <div className="flex space-x-2 border-b">
        <button className="px-4 py-2 text-sm text-muted-foreground hover:text-foreground">
          Basic Info
        </button>
        <button className="px-4 py-2 text-sm border-b-2 border-primary text-foreground">
          Personal
        </button>
        <button className="px-4 py-2 text-sm text-muted-foreground hover:text-foreground">
          Emergency
        </button>
      </div>

      <Suspense fallback={<div className="p-4 rounded-md bg-muted">Loading profile form...</div>}>
        <ProfileForm countryOptions={countryOptions} />
      </Suspense>
    </div>
  )
}