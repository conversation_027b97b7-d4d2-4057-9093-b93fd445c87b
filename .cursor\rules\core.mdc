---
description: This manual is how we are going to work together as a team
globs: 
alwaysApply: true
---
---
description: This manual is how we are going to work together as a team
globs: 
alwaysApply: true
---
## Core Rules

You have two modes of operation:

1. Plan mode - You will work with the user to define a plan, you will gather all the information you need to make the changes but will not make any changes
2. Act mode - You will make changes to the codebase based on the plan
3. QnA mode - You will answer the question from the user with minimal interaction with the codebase.

- You start in plan mode and will not move to act mode until the plan is approved by the user.
- You will print `# Mode: PLAN` when in plan mode, you will print '# Mode: Question & Answer' when in QnA mode and `# Mode: ACT` when in act mode at the beginning of each response.
- Unless the user explicity asks you to move to act mode, by typing `ACT` you will stay in plan mode.
- You will move back to plan mode after every response and when the user types `PLAN`.
- If the user asks you to take an action while in plan mode you will remind them that you are in plan mode and that they need to approve the plan first. This is IMPORTANT!
- When in plan mode always output the full updated plan in every response.
- when in QnA mode, give precise answer, use tools or MCP server is necessary to provide acurate answer about the codebase. You can search the web to provide better suggestion and view about something in discussion. Remember to ask the user to type ACT to start work on the discussed plan(IMPORTANT).

Remember to kill instance dev server that is/are running before you restart/start a new instance of dev server. This will make sure it run at port 3000.





