import { BaseRepository } from '@/lib/db/base-repository';
import { z } from 'zod';
import { FieldMapping, fieldMappingSchema } from '@/types/event-types';

/**
 * Field Mapping database schema for the repository
 */
const FieldMappingDBSchema = z.object({
  id: z.string().uuid(),
  event_id: z.string().uuid(),
  field_id: z.string(),
  profile_field: z.string(),
  is_bidirectional: z.boolean(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

/**
 * Type for the FieldMapping database model
 */
type FieldMappingDB = z.infer<typeof FieldMappingDBSchema>;

/**
 * Repository for field mapping operations
 */
export class FieldMappingRepository extends BaseRepository<FieldMappingDB> {
  constructor() {
    super('field_mappings', FieldMappingDBSchema);
  }

  /**
   * Transform the database field mapping to the application field mapping
   */
  private toFieldMapping(dbMapping: FieldMappingDB): FieldMapping {
    return {
      id: dbMapping.id,
      eventId: dbMapping.event_id,
      fieldId: dbMapping.field_id,
      profileField: dbMapping.profile_field,
      isBidirectional: dbMapping.is_bidirectional,
      createdAt: new Date(dbMapping.created_at),
      updatedAt: new Date(dbMapping.updated_at),
    };
  }

  /**
   * Transform the application field mapping to the database field mapping
   */
  private toFieldMappingDB(mapping: Partial<FieldMapping>): Partial<FieldMappingDB> {
    const result: Partial<FieldMappingDB> = {};

    if (mapping.eventId !== undefined) result.event_id = mapping.eventId;
    if (mapping.fieldId !== undefined) result.field_id = mapping.fieldId;
    if (mapping.profileField !== undefined) result.profile_field = mapping.profileField;
    if (mapping.isBidirectional !== undefined) result.is_bidirectional = mapping.isBidirectional;

    return result;
  }

  /**
   * Get all mappings for an event
   */
  async getMappingsByEventId(eventId: string): Promise<FieldMapping[]> {
    const result = await this.find({ event_id: eventId });

    if (!result.success) {
      throw new Error(`Failed to get field mappings: ${result.message}`);
    }

    return (result.data || []).map(this.toFieldMapping);
  }

  /**
   * Get mapping by ID
   */
  async getMappingById(id: string): Promise<FieldMapping | null> {
    const result = await this.findById(id);

    if (!result.success) {
      if (result.message?.includes('not found')) {
        return null;
      }
      throw new Error(`Failed to get field mapping: ${result.message}`);
    }

    return this.toFieldMapping(result.data!);
  }

  /**
   * Get mapping by event ID and field ID
   */
  async getMappingByEventAndFieldId(eventId: string, fieldId: string): Promise<FieldMapping | null> {
    const result = await this.find({ event_id: eventId, field_id: fieldId });

    if (!result.success) {
      throw new Error(`Failed to get field mapping: ${result.message}`);
    }

    if (!result.data || result.data.length === 0) {
      return null;
    }

    if (result.data && result.data.length > 0 && result.data[0]) {
      return this.toFieldMapping(result.data[0]);
    }
    return null;
  }

  /**
   * Create a new mapping
   */
  async createMapping(mapping: Omit<FieldMapping, 'id' | 'createdAt' | 'updatedAt'>): Promise<FieldMapping> {
    // Validate the mapping
    fieldMappingSchema.parse(mapping);

    const result = await this.create({
      id: crypto.randomUUID(),
      event_id: mapping.eventId,
      field_id: mapping.fieldId,
      profile_field: mapping.profileField,
      is_bidirectional: mapping.isBidirectional,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    if (!result.success) {
      throw new Error(`Failed to create field mapping: ${result.message}`);
    }

    return this.toFieldMapping(result.data!);
  }

  /**
   * Update a mapping
   */
  async updateMapping(id: string, mapping: Partial<Omit<FieldMapping, 'id' | 'createdAt' | 'updatedAt'>>): Promise<FieldMapping> {
    // Prepare the data to update
    const dbMapping = this.toFieldMappingDB(mapping);

    // Always update the updated_at timestamp
    dbMapping.updated_at = new Date().toISOString();

    const result = await this.update(id, dbMapping);

    if (!result.success) {
      throw new Error(`Failed to update field mapping: ${result.message}`);
    }

    return this.toFieldMapping(result.data!);
  }

  /**
   * Delete a mapping
   */
  async deleteMapping(id: string): Promise<void> {
    const result = await this.delete(id);

    if (!result.success) {
      throw new Error(`Failed to delete field mapping: ${result.message}`);
    }
  }

  /**
   * Create or update multiple mappings for an event
   */
  async createOrUpdateBulkMappings(eventId: string, mappings: Omit<FieldMapping, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<FieldMapping[]> {
    const results: FieldMapping[] = [];

    // Process each mapping
    for (const mapping of mappings) {
      // Check if the mapping already exists
      const existingMapping = await this.getMappingByEventAndFieldId(eventId, mapping.fieldId);

      if (existingMapping) {
        // Update existing mapping
        const updated = await this.updateMapping(existingMapping.id, {
          ...mapping,
          eventId // Ensure eventId is set
        });
        results.push(updated);
      } else {
        // Create new mapping
        const created = await this.createMapping({
          ...mapping,
          eventId // Ensure eventId is set
        });
        results.push(created);
      }
    }

    return results;
  }

  /**
   * Delete all mappings for an event
   */
  async deleteAllMappingsForEvent(eventId: string): Promise<void> {
    const mappings = await this.getMappingsByEventId(eventId);

    for (const mapping of mappings) {
      await this.deleteMapping(mapping.id);
    }
  }

  /**
   * Get all profile fields mapped for an event
   */
  async getProfileFieldsMappedForEvent(eventId: string): Promise<string[]> {
    const mappings = await this.getMappingsByEventId(eventId);
    return mappings.map(mapping => mapping.profileField);
  }
}