#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const rootDir = path.resolve(__dirname, '..');
const extensions = ['.ts', '.tsx', '.js', '.jsx'];
const find = '@/';
const replace = '@/';

// Function to find all matching files recursively
function findFiles(dir, extensions) {
  let results = [];
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !filePath.includes('node_modules') && !filePath.includes('.next') && !filePath.includes('.git')) {
      // Recursively search directories
      results = results.concat(findFiles(filePath, extensions));
    } else if (extensions.includes(path.extname(file).toLowerCase())) {
      // Add file if it matches the extensions
      results.push(filePath);
    }
  }
  
  return results;
}

// Function to process a file
function processFile(filePath) {
  console.log(`Processing: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Replace @/ with @/
  content = content.replace(new RegExp(find, 'g'), replace);
  
  // Only write to the file if changes were made
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  - Updated`);
    return true;
  }
  
  return false;
}

// Main execution
console.log('Starting import path fix script...');
console.log(`Replacing '${find}' with '${replace}' in all ${extensions.join(', ')} files`);

const files = findFiles(rootDir, extensions);
console.log(`Found ${files.length} files to process`);

let updatedCount = 0;
for (const file of files) {
  const updated = processFile(file);
  if (updated) updatedCount++;
}

console.log(`\nDone! Updated ${updatedCount} files.`); 