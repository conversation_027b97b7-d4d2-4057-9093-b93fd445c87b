import { z } from "zod";

// Schema for contact validation
export const ContactSchema = z.object({
  id: z.string().optional(),
  userId: z.string().optional(),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().optional(),
  relationship: z.string().min(1, "Relationship is required"),
  email: z.string().email("Valid email is required").optional().nullable(),
  phone: z.string().optional().nullable(),
  dateOfBirth: z.string().optional().nullable(),
  gender: z.string().optional().nullable(),
  tShirtSize: z.string().optional().nullable(),
  address: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  country: z.string().optional().nullable(),
  postcode: z.string().optional().nullable(),
  emergencyContactName: z.string().optional().nullable(),
  emergencyContactNo: z.string().optional().nullable(),
  emergencyContactRelationship: z.string().optional().nullable(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

export type ContactFormData = z.infer<typeof ContactSchema>; 