import { Metadata } from 'next'
import SignInForm from '@/components/auth/SignInForm'
import { Suspense } from 'react'

export const metadata: Metadata = {
  title: 'Sign In | Fuiyoo',
  description: 'Sign in to your Fuiyoo account',
}

// Make the page static to avoid dynamic server usage errors
export const dynamic = 'force-static'

export default function SignInPage() {
  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12 bg-background">
      <div className="w-full max-w-md">
        <div className="bg-card text-card-foreground shadow-lg rounded-lg p-8 border border-border">
          <Suspense fallback={<div>Loading...</div>}>
            <SignInForm />
          </Suspense>
        </div>
      </div>
    </div>
  )
}
