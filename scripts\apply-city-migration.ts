import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';
import * as https from 'https';

// Load environment variables from .env files
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

async function applyMigration() {
  // Get environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase environment variables');
    console.log('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local or .env');
    process.exit(1);
  }

  // Initialize Supabase client with service role key for full access
  const supabase = createClient(supabaseUrl, supabaseKey);

  console.log('Applying city column migration');
  
  try {
    // Directly run the ALTER TABLE command
    const { data, error } = await supabase.rpc('execute_sql', { 
      query: 'ALTER TABLE events ADD COLUMN IF NOT EXISTS city TEXT;' 
    });
    
    if (error) {
      console.error('Direct SQL execution failed:', error);
      
      // Try another approach
      console.log('Trying REST SQL API...');
      
      // Extract the project ID from the Supabase URL
      const projectId = supabaseUrl.match(/([^\/]+)(?=\.supabase\.co)/)?.[0];
      
      if (!projectId) {
        console.error('Could not determine project ID from Supabase URL');
        process.exit(1);
      }
      
      // Send a direct SQL query via REST API
      const sqlEndpoint = `https://${projectId}.supabase.co/rest/v1/rpc/execute_sql`;
      
      const postData = JSON.stringify({
        query: 'ALTER TABLE events ADD COLUMN IF NOT EXISTS city TEXT;'
      });
      
      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`
        }
      };
      
      const req = https.request(sqlEndpoint, options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          if (res.statusCode === 200) {
            console.log('City column added successfully!');
          } else {
            console.error(`REST SQL request failed with status ${res.statusCode}: ${data}`);
          }
        });
      });
      
      req.on('error', (e) => {
        console.error(`REST SQL request error: ${e.message}`);
      });
      
      req.write(postData);
      req.end();
    } else {
      console.log('City column added successfully!');
    }
  } catch (err) {
    console.error('Error executing migration:', err);
  }
}

applyMigration(); 