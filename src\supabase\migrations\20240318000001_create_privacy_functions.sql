-- Function to create privacy consent tables
CREATE OR REPLACE FUNCTION create_privacy_consents_table()
R<PERSON><PERSON><PERSON> void AS $$
BEGIN
  -- Create extension if not exists
  CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
  
  -- Create privacy_consent_versions table if not exists
  CREATE TABLE IF NOT EXISTS privacy_consent_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    version VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    consent_text TEXT NOT NULL,
    effective_date TIMESTAMP WITH TIME ZONE NOT NULL,
    expiry_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(version, name)
  );
  
  -- Create privacy_consents table if not exists
  CREATE TABLE IF NOT EXISTS privacy_consents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    consent_type VARCHAR(50) NOT NULL,
    consent_given BOOLEAN NOT NULL DEFAULT false,
    consent_version VARCHAR(50) NOT NULL,
    consent_text TEXT NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    previous_record_id UUID REFERENCES privacy_consents(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  );
  
  -- Create indexes if not exist
  CREATE INDEX IF NOT EXISTS idx_privacy_consents_user_id ON privacy_consents(user_id);
  CREATE INDEX IF NOT EXISTS idx_privacy_consents_type ON privacy_consents(consent_type);
  
  -- Insert initial consent versions if table is empty
  IF NOT EXISTS (SELECT 1 FROM privacy_consent_versions LIMIT 1) THEN
    INSERT INTO privacy_consent_versions (version, name, description, consent_text, effective_date) VALUES
    ('1.0', 'Marketing', 'Receive marketing communications and updates', 'I agree to receive marketing communications, newsletters, and updates about events and services.', CURRENT_TIMESTAMP),
    ('1.0', 'Analytics', 'Allow usage data collection for analytics', 'I agree to the collection and processing of my usage data for analytics and service improvement purposes.', CURRENT_TIMESTAMP),
    ('1.0', 'Third Party', 'Share data with trusted third parties', 'I agree to share my data with trusted third-party partners for service delivery and improvement.', CURRENT_TIMESTAMP),
    ('1.0', 'Data Sharing', 'Share profile data with event organizers', 'I agree to share my profile information with event organizers for event registration and management.', CURRENT_TIMESTAMP),
    ('1.0', 'Profile Display', 'Display profile in public listings', 'I agree to display my profile information in public event attendee listings.', CURRENT_TIMESTAMP);
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get user consents
CREATE OR REPLACE FUNCTION get_user_consents(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  consent_type VARCHAR(50),
  consent_given BOOLEAN,
  consent_version VARCHAR(50),
  consent_text TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pc.id,
    pc.consent_type,
    pc.consent_given,
    pc.consent_version,
    pc.consent_text,
    pc.expires_at,
    pc.created_at,
    pc.updated_at
  FROM privacy_consents pc
  WHERE pc.user_id = p_user_id
  ORDER BY pc.created_at DESC;
END;
$$ LANGUAGE plpgsql; 