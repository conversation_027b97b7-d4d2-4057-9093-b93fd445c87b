-- Add registration_fields and field_mappings tables
-- Migration: 0015_add_registration_fields.sql

-- Start transaction for atomic migration
BEGIN;

-- Create registration_fields table for custom fields
CREATE TABLE IF NOT EXISTS registration_fields (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  field_id TEXT NOT NULL, -- Unique identifier for the field within this event
  field_type TEXT NOT NULL, -- Text, number, select, checkbox, date, etc.
  label TEXT NOT NULL, -- Display label
  description TEXT, -- Help text
  is_required BOOLEAN DEFAULT false,
  is_public BOOLEAN DEFAULT true, -- Whether visible to registrants (vs. admin-only)
  validation_rules JSONB DEFAULT NULL, -- Zod validation schema as JSON
  default_value JSONB DEFAULT NULL, -- Default value if any
  options JSONB DEFAULT NULL, -- Options for select, radio, checkbox fields
  order_index INTEGER DEFAULT 0, -- For ordering fields in the form
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(event_id, field_id) -- Ensure field_id is unique per event
);

-- Create index for faster lookup by event
CREATE INDEX IF NOT EXISTS registration_fields_event_id_idx ON registration_fields(event_id);

-- Create field_mappings table to connect registration fields to user profiles
CREATE TABLE IF NOT EXISTS field_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  field_id TEXT NOT NULL, -- References registration_fields.field_id
  profile_field TEXT NOT NULL, -- The field in the user_profiles table to map to
  is_bidirectional BOOLEAN DEFAULT false, -- Whether updates to profile should reflect in registration too
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(event_id, field_id)
);

-- Create index for faster lookup by event
CREATE INDEX IF NOT EXISTS field_mappings_event_id_idx ON field_mappings(event_id);

-- Add emergency_contact_settings to events table
ALTER TABLE events ADD COLUMN IF NOT EXISTS emergency_contact_settings JSONB DEFAULT '{
  "required": false,
  "fields": ["name", "phone", "relationship"],
  "allow_same_for_multiple_registrations": true
}'::jsonb;

-- Add comments for documentation
COMMENT ON TABLE registration_fields IS 'Custom registration fields for events';
COMMENT ON COLUMN registration_fields.field_id IS 'Unique identifier for this field within the event';
COMMENT ON COLUMN registration_fields.field_type IS 'Type of field - text, select, checkbox, date, etc.';
COMMENT ON COLUMN registration_fields.validation_rules IS 'JSON schema for field validation using Zod';
COMMENT ON COLUMN registration_fields.options IS 'Array of options for select, radio, checkbox fields';

COMMENT ON TABLE field_mappings IS 'Maps registration fields to user profile fields for auto-population';
COMMENT ON COLUMN field_mappings.profile_field IS 'The field name in user_profiles to map to';
COMMENT ON COLUMN field_mappings.is_bidirectional IS 'If true, changes to profile will update registration data too';

COMMENT ON COLUMN events.emergency_contact_settings IS 'JSON configuration for emergency contact requirements for this event';

-- Force schema cache reload
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('0015_add_registration_fields', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit transaction
COMMIT; 