# Fuiyoo Design System

This document outlines the design system for the Fuiyoo application, focusing on creating a cheerful, colorful, and energetic user experience with indigo-500 as the main color.

## Color Palette

### Primary Colors
- **Primary (Indigo-500)**: `hsl(238, 75%, 58%)`
  - **Hover**: `hsl(238, 75%, 53%)`
  - **Active**: `hsl(238, 75%, 48%)`
  - **Focus**: `hsl(238, 75%, 58%)`
  - **Subtle**: `hsl(238, 75%, 96%)`

### Secondary Colors
- **Secondary (Teal)**: `hsl(174, 75%, 45%)`
  - **Hover**: `hsl(174, 75%, 40%)`
  - **Active**: `hsl(174, 75%, 35%)`

### Accent Colors
- **Accent (Vibrant Pink)**: `hsl(330, 85%, 60%)`
  - **Hover**: `hsl(330, 85%, 55%)`
  - **Active**: `hsl(330, 85%, 50%)`

### Status Colors
- **Success**: `hsl(142, 70%, 45%)`
- **Warning**: `hsl(38, 92%, 50%)`
- **Destructive**: `hsl(0, 84.2%, 60.2%)`

### Neutral Colors
- **Background**: `hsl(0, 0%, 100%)`
- **Foreground**: `hsl(224, 71.4%, 4.1%)`
- **Muted**: `hsl(220, 14.3%, 95.9%)`
- **Muted Foreground**: `hsl(220, 8.9%, 46.1%)`
- **Border**: `hsl(220, 13%, 91%)`

### Chart Colors
1. **Chart 1 (Indigo)**: `hsl(238, 75%, 58%)`
2. **Chart 2 (Teal)**: `hsl(174, 75%, 45%)`
3. **Chart 3 (Pink)**: `hsl(330, 85%, 60%)`
4. **Chart 4 (Amber)**: `hsl(38, 92%, 50%)`
5. **Chart 5 (Green)**: `hsl(142, 70%, 45%)`

## Typography

### Font Family
- **Primary Font**: Inter (sans-serif)

### Font Sizes
- **xs**: 0.75rem (12px)
- **sm**: 0.875rem (14px)
- **base**: 1rem (16px)
- **lg**: 1.125rem (18px)
- **xl**: 1.25rem (20px)
- **2xl**: 1.5rem (24px)
- **3xl**: 1.875rem (30px)
- **4xl**: 2.25rem (36px)
- **5xl**: 3rem (48px)

### Font Weights
- **Normal**: 400
- **Medium**: 500
- **Semibold**: 600
- **Bold**: 700

### Line Heights
- **Tight**: 1.25
- **Snug**: 1.375
- **Normal**: 1.5
- **Relaxed**: 1.625
- **Loose**: 2

## Spacing

- **1**: 0.25rem (4px)
- **2**: 0.5rem (8px)
- **3**: 0.75rem (12px)
- **4**: 1rem (16px)
- **5**: 1.25rem (20px)
- **6**: 1.5rem (24px)
- **8**: 2rem (32px)
- **10**: 2.5rem (40px)
- **12**: 3rem (48px)
- **16**: 4rem (64px)
- **20**: 5rem (80px)
- **24**: 6rem (96px)

## Border Radius

- **sm**: 0.125rem (2px)
- **md**: 0.375rem (6px)
- **lg**: 0.5rem (8px)
- **xl**: 0.75rem (12px)
- **2xl**: 1rem (16px)
- **full**: 9999px

## Shadows

- **sm**: 0 1px 2px 0 rgb(0 0 0 / 0.05)
- **md**: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)
- **lg**: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)
- **xl**: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)

## Interactive States

### Focus
- **Focus Ring**: `hsl(238, 75%, 58% / 0.35)`
- **Focus Visible**: `hsl(238, 75%, 58% / 0.7)`
- **Outline Color**: `hsl(238, 75%, 58% / 0.5)`

### Selection
- **Selected Background**: `hsl(238, 75%, 58% / 0.15)`

### Hover & Active
- **Hover Overlay**: `hsl(0, 0%, 0% / 0.05)`
- **Active Overlay**: `hsl(0, 0%, 0% / 0.1)`

## Dark Mode

The design system includes a comprehensive dark mode with adjusted colors for better contrast and readability in low-light environments.

### Dark Mode Colors
- **Background**: `hsl(224, 71.4%, 4.1%)`
- **Foreground**: `hsl(210, 20%, 98%)`
- **Primary**: `hsl(238, 75%, 65%)`
- **Secondary**: `hsl(174, 75%, 40%)`
- **Accent**: `hsl(330, 85%, 65%)`

## Component Examples

### Buttons
- **Primary**: Background color of indigo-500 with white text
- **Secondary**: Background color of teal with white text
- **Accent**: Background color of vibrant pink with white text
- **Outline**: Transparent background with indigo border
- **Ghost**: Transparent background that shows indigo on hover

### Cards
- Clean white background with subtle border
- Hover state with slight shadow elevation
- Optional gradient header from indigo to transparent

### Form Elements
- Inputs with clear focus states using the indigo focus ring
- Checkboxes and radio buttons with indigo accent
- Dropdowns with consistent styling

## Accessibility

- Minimum contrast ratio of 4.5:1 for normal text
- Focus states clearly visible for keyboard navigation
- Interactive elements have appropriate hover/focus states
- Semantic HTML elements used appropriately
- ARIA attributes for complex components

## Implementation

The design system is implemented using:
- Tailwind CSS for utility-based styling
- CSS variables for theming
- shadcn/ui components as a base
- Dark mode support via the `next-themes` library
