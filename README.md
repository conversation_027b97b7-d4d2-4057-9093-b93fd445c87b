# Fuiyoo

A Next.js application for event management and discovery.

## Development Scripts

This project includes three main scripts to simplify development, building, and running the application:

### 1. Development Server (`dev.sh`)

Start the development server with various options:

```bash
# Standard Webpack mode
./dev.sh

# Turbopack mode (faster)
./dev.sh --turbo

# Safe mode (falls back to standard webpack if Turbopack fails)
./dev.sh --turbo --safe

# Standard webpack with safe mode for API fallbacks
./dev.sh --safe

# Disable auto-fallback (by default, it will automatically fallback if Turbopack fails)
./dev.sh --turbo --no-fallback
```

### 2. Building the Application (`build.sh`)

Build the application with various options:

```bash
# Standard Webpack build
./build.sh

# Turbopack build (faster)
./build.sh --turbo

# Force rebuild (ignores cached builds)
./build.sh --force

# Skip static generation
./build.sh --skip-static

# Skip preflight checks
./build.sh --skip-preflight

# Clean .next directory before building
./build.sh --clean
```

You can combine options as needed:

```bash
# Clean and build with Turbopack
./build.sh --clean --turbo
```

### 3. Starting Production Server (`start.sh`)

Start the production server:

```bash
# Standard start
./start.sh

# Clean cache before starting
./start.sh --clean-cache
```

### 4. Cleaning the Project (`clean.sh`)

Clean various caches for a fresh build:

```bash
# Clean Next.js, node_modules, and Turbopack caches
./clean.sh
```

## Turbopack Configuration

This project uses an enhanced Turbopack configuration to avoid common issues:

- **Fixed `outputFileTracingRoot`**: Set to `__dirname` to avoid the `Option::unwrap()` on a `None` value error
- **Fallback for Optional Dependencies**: Added configuration to handle missing optional dependencies
- **Auto-fallback Mode**: The dev script automatically falls back to standard webpack if Turbopack encounters issues

### Troubleshooting Turbopack

If you encounter issues with Turbopack, try these solutions:

1. **Use the debug mode**:
   ```bash
   npm run dev:turbo-debug
   ```
   This generates a trace file in `.next/trace-turbopack` that can help diagnose issues.

2. **Use safe mode**:
   ```bash
   npm run dev:turbo-safe
   ```
   This will automatically fall back to standard webpack if Turbopack fails.

3. **Revert to standard webpack**:
   ```bash
   npm run dev:standard
   ```
   If Turbopack consistently fails, use the standard webpack development server.

## Configuration

The project uses several configuration files:

- **`next.config.js`**: Main Next.js configuration for both Turbopack and Webpack
- **`.turbopackrc.js`**: Turbopack-specific configuration for development
- **`netlify.toml`**: Configuration for Netlify deployments
- **`.env.local`**: Local environment variables (not committed to git)

See `docs/turbopack-webpack.md` for detailed documentation on how the project handles both development (Turbopack) and production (Webpack) environments.

## Environment Variables

The project uses environment variables for configuration. See `docs/environment-variables.md` for a complete list of environment variables and their usage.

Key environment variables:

- `NEXT_PUBLIC_SITE_URL`: The public URL of the site in production
- `NEXT_PUBLIC_DEV_URL`: The development URL for local development (default: http://localhost:3000)
- `NEXT_PUBLIC_DEV_API_URL`: The API base URL for development (default: http://localhost:3000/api)
- `NEXT_PUBLIC_SUPABASE_URL`: The URL of your Supabase project
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: The anonymous key for Supabase client access

## Environment Detection

The project includes utilities to detect the current build environment:

```typescript
import { isTurbopack, isWebpack, isDev, isProd } from '@/utils/env-helpers';

// Use different logic based on the environment
if (isTurbopack()) {
  console.log('Running in Turbopack development mode');
}

if (isWebpack()) {
  console.log('Running in Webpack mode');
}

// Or check the build mode
if (isDev()) {
  console.log('Running in development mode');
}

if (isProd()) {
  console.log('Running in production mode');
}
```

## Dependencies

See `package.json` for the complete list of dependencies. Key technologies include:

- Next.js 15.3.0
- React 19.1.0
- Supabase for database and authentication
- Tailwind CSS for styling
- Framer Motion for animations