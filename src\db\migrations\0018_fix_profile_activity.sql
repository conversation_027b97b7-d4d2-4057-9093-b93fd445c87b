-- Migration: Fix profile activity tracking
-- This migration aligns the profile_activity table with the code expectations
-- and sets up automatic activity logging via triggers

-- Start transaction for atomic migration
BEGIN;

-- Step 1: Add missing columns to profile_activity table
ALTER TABLE profile_activity
  ADD COLUMN IF NOT EXISTS activity_type TEXT,
  ADD COLUMN IF NOT EXISTS activity_description TEXT,
  ADD COLUMN IF NOT EXISTS previous_value JSONB,
  ADD COLUMN IF NOT EXISTS new_value JSONB,
  ADD COLUMN IF NOT EXISTS field_name TEXT;

-- Step 2: Make required columns nullable to work with the new schema
ALTER TABLE profile_activity
  ALTER COLUMN profile_id DROP NOT NULL,
  ALTER COLUMN version DROP NOT NULL,
  ALTER COLUMN change_type DROP NOT NULL;

-- Step 3: Update existing columns to match expected structure
-- Map existing columns to new structure where possible
UPDATE profile_activity
SET
  activity_type = change_type,
  activity_description = 'Profile information updated',
  previous_value = changed_fields,
  new_value = changed_fields
WHERE activity_type IS NULL;

-- Step 4: Create helper function to record profile changes
CREATE OR REPLACE FUNCTION log_profile_change()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO profile_activity (
    user_id,
    activity_type,
    activity_description,
    previous_value,
    new_value
  ) VALUES (
    NEW.id,
    'profile_update',
    'Profile information updated',
    row_to_json(OLD)::jsonb,
    row_to_json(NEW)::jsonb
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create trigger on users table to record changes
DROP TRIGGER IF EXISTS profile_update_trigger ON users;
CREATE TRIGGER profile_update_trigger
AFTER UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION log_profile_change();

-- Step 5: Add indices for faster lookups if they don't exist
CREATE INDEX IF NOT EXISTS profile_activity_activity_type_idx ON profile_activity(activity_type);
CREATE INDEX IF NOT EXISTS profile_activity_created_at_idx ON profile_activity(created_at);

-- Force schema cache reload
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('0018_fix_profile_activity', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit transaction
COMMIT;
