import { z } from 'zod';

/**
 * Schema for creating a new event
 */
export const createEventSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  description: z.string().optional(),
  eventType: z.string(),
  location: z.string().optional(),
  country: z.string().optional(),
  state: z.string().optional(),
  city: z.string().optional(),
  startDate: z.string().nullable().optional(),
  endDate: z.string().nullable().optional(),
  timezone: z.string().optional(),
  status: z.enum(["draft", "published", "cancelled", "completed"]).default("draft"),
  id: z.string().optional(),
  categories: z.array(
    z.object({
      id: z.string().optional(),
      name: z.string().min(1, { message: "Category name is required" }),
      description: z.string().optional(),
      price: z.number().optional(),
      earlyBirdPrice: z.number().optional(),
      earlyBirdEndDate: z.string().optional(),
      registrationCloseDate: z.string().optional(),
      startTime: z.string().optional(),
      customFields: z.array(z.any()).optional(),
    })
  ).optional(),
  customFields: z.array(z.any()).optional(),
  emergencyContactSettings: z.object({
    required: z.boolean().default(false),
    fields: z.array(z.string()).default(['name', 'phone', 'relationship']),
    allowSameForMultipleRegistrations: z.boolean().default(true),
  }).optional(),
});

export type CreateEventInput = z.infer<typeof createEventSchema>; 