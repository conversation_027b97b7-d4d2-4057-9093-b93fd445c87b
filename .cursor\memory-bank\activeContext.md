# Active Context

## Current Focus
We are implementing UI improvements and enhancing the user experience, specifically:
1. Improving UI component theming for better dark mode support
2. Enhancing homepage readability with Tailwind 4 masks
3. Centering event type icons for better visual alignment
4. Migrating from Clerk to Supabase Auth
5. Implementing event management features
6. Optimizing for Next.js 15 and React 19

## Recent Changes
1. **UI Improvements**
   - Fixed tooltip theming with explicit HSL notation for better dark mode support
   - Centered event type icons in the event creation wizard
   - Enhanced homepage readability with Tailwind 4 masks
   - Improved text contrast on the homepage with background masks and gradients

2. **Authentication**
   - Migrated from Clerk to Supabase Auth
   - Implemented Google OAuth authentication
   - Added auth-related utility functions in src/lib/supabase/auth.ts

3. **Event Management**
   - Implemented event type selection with centered icons
   - Added event calendar view
   - Created T-shirt size chart upload functionality
   - Implemented event preview gallery

4. **Build System**
   - Fixed build errors by cleaning Next.js cache
   - Optimized for Next.js 15 and React 19
   - Added Tailwind 4 mask utilities

## Next Steps
- Continue improving UI components for better theming
- Enhance event management features
- Implement event registration flow
- Optimize performance with React 19 features
- Complete the migration from Clerk to Supabase Auth

## Active Decisions
- Using Supabase Auth for authentication
- Using Tailwind 4 mask utilities for UI enhancements
- Using explicit HSL notation for better theming support
- Following client/server component separation patterns
- Using Supabase for database operations
- Using pnpm as the package manager

## Current Challenges
1. Dialog Component Integration:
   - Properly implementing the Dialog component from Radix UI
   - Ensuring accessibility and responsive behavior
   - Handling open/close states properly

2. Class Name Management:
   - Efficiently combining Tailwind CSS classes
   - Handling conditional classes
   - Maintaining proper specificity

3. Performance Optimization:
   - Reducing initial load time
   - Optimizing API calls
   - Implementing proper caching strategies
   - Ensuring fast edge performance

4. Testing Strategy:
   - Creating comprehensive test suite
   - Testing edge cases in user flows
   - Ensuring cross-browser compatibility
   - Automated testing infrastructure

5. Authentication Integration:
   - Correctly using Clerk authentication in server actions
   - Handling authentication state consistently
   - Properly typing auth functions and responses

## Brand and Design
- Primary brand color: Emerald Green (#10b981)
- Complementary colors:
  - Deep Blue (#1e40af)
  - Accent Orange (#f97316)
  - Neutral Gray (#6b7280)
- Typography:
  - Headings: Inter (sans-serif)
  - Body: Geist Sans
- Design principles:
  - Clean, minimalist interface
  - Card-based layout
  - Consistent spacing and padding
  - Subtle shadows for depth
  - Rounded corners for elements

## File Structure
- App Router architecture with route groups
- Component organization by feature
- Shared UI components in /components/ui
- Feature-specific components in /components/{feature}
- Layout components in /components/layout
- API routes in /api
- Server actions in /app/actions
- Database utilities in /lib/supabase
- Authentication handlers in middleware.ts and /api/webhooks
- Utility functions in /lib/utils.ts

## Project URLs
- Development: http://localhost:3000
- Production: https://fuiyoo.netlify.app
- Supabase Dashboard: https://supabase.com/dashboard/project/{project-id}
- Clerk Dashboard: https://dashboard.clerk.com