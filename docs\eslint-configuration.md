# ESLint Configuration in Fuiyoo

## Current Setup

The project uses Next.js's built-in ESLint configuration through `eslint-config-next` version 14.1.0, which is compatible with ESLint 8.x. The configuration extends `next/core-web-vitals` and includes custom rules for handling unused variables.

## Configuration Files

- `.eslintrc.json`: Contains the main ESLint configuration
- `next.config.js`: Contains the Next.js ESLint integration settings

## Known Issues

There is currently an issue with the ESLint configuration when running `next lint` or during builds. The error occurs in the `src/lib/supabase/types.ts` file with the `@typescript-eslint/no-unused-vars` rule:

```
Cannot read properties of undefined (reading 'type')
Occurred while linting /path/to/src/lib/supabase/types.ts:1
Rule: "@typescript-eslint/no-unused-vars"
```

As a temporary workaround, ESLint has been disabled during builds by setting `ignoreDuringBuilds: true` in the `next.config.js` file.

## Potential Solutions

1. **Update Dependencies**: Ensure that all ESLint-related packages are compatible with each other. The current setup uses ESLint 8.57.1 but also has some ESLint 9.x packages installed.

2. **Fix the Specific File**: The `src/lib/supabase/types.ts` file is a large generated file with Supabase types. It could be excluded from linting by adding it to the `ignorePatterns` in `.eslintrc.json`.

3. **Upgrade Configuration**: When upgrading to ESLint 9.x, the configuration format changes from the traditional format to the new flat config format. This would require significant changes to the ESLint setup.

## Recommended Approach

For now, the recommended approach is to keep ESLint disabled during builds to prevent build failures. When running ESLint manually, you can use the following command to exclude the problematic file:

```bash
npx eslint . --ignore-pattern "src/lib/supabase/types.ts"
```

In the future, when upgrading to Next.js 15 or later, consider upgrading the ESLint configuration to match the latest ESLint version and follow the Next.js documentation for the recommended setup.
