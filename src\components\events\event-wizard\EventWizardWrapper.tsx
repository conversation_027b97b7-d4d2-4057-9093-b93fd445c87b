'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { EventWizard } from '@/components/events/event-wizard/wizard-container';
import { getEvent, getEventCategories, getEventFields } from '@/app/actions/events';
import { EventType } from '@/types/event-types';
import { toast } from '@/components/ui/use-toast';
import { Event } from '@/repositories/event-repository';

interface EventWizardWrapperProps {
  eventTypes: EventType[];
  pageTitle: string;
  key?: string; // Add key prop for forcing remounts
}

export function EventWizardWrapper({ eventTypes, pageTitle }: EventWizardWrapperProps) {
  console.log('[DEBUG] EventWizardWrapper - Component mounted with props:', {
    eventTypesCount: eventTypes?.length || 0,
    pageTitle
  });

  const searchParams = useSearchParams();
  const eventId = searchParams.get('id');

  console.log('[DEBUG] EventWizardWrapper - Search params parsed:', {
    eventId,
    allParams: Object.fromEntries(searchParams.entries())
  });

  const [existingEvent, setExistingEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(!!eventId);
  const [error, setError] = useState<string | null>(null);

  console.log('[DEBUG] EventWizardWrapper - Initial state set:', {
    hasEventId: !!eventId,
    initialLoading: !!eventId,
    initialError: error
  });

  // Debug logging for development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Event ID from URL:', eventId);
      console.log('EventWizardWrapper state:', {
        loading,
        error: !!error,
        hasExistingEvent: !!existingEvent,
        eventTypesCount: eventTypes?.length || 0
      });
    }
  }, [eventId, loading, error, existingEvent, eventTypes?.length]);

  useEffect(() => {
    async function loadEvent() {
      if (!eventId) {
        return;
      }

      try {
        console.log('[DEBUG] Setting loading state to true for event ID:', eventId);
        setLoading(true);
        setError(null);

        console.log('[DEBUG] Starting to load event data for ID:', eventId);

        // Add retry logic for network errors
        let eventResponse;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            // Load the event data
            eventResponse = await getEvent(eventId);
            console.log('[DEBUG] Event response:', eventResponse);
            break; // If successful, break out of the retry loop
          } catch (fetchError) {
            retryCount++;
            console.error(`[DEBUG] Error fetching event (attempt ${retryCount}/${maxRetries}):`, fetchError);

            if (retryCount >= maxRetries) {
              console.error('[DEBUG] Max retries reached, giving up');
              throw new Error(`Failed to fetch event after ${maxRetries} attempts: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`);
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.min(1000 * Math.pow(2, retryCount), 5000);
            console.log(`[DEBUG] Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        // Check for authentication errors specifically
        if (eventResponse && eventResponse.error === 'Authentication required') {
          console.error('[DEBUG] Authentication error loading event');

          // Instead of redirecting immediately, check if we're already authenticated
          // This helps prevent redirect loops
          try {
            // Try to get the current auth state from localStorage
            const hasLocalStorageAuth = Object.keys(localStorage).some(key =>
              key.startsWith('sb-') && localStorage.getItem(key)?.includes('"access_token"')
            );

            console.log('[DEBUG] Local storage auth check:', hasLocalStorageAuth);

            if (hasLocalStorageAuth) {
              // We appear to have auth data in localStorage but the server doesn't recognize it
              // This could be a race condition or cookie issue
              console.log('[DEBUG] Detected potential auth state mismatch - not redirecting to avoid loop');

              // Set a specific error that doesn't trigger a redirect
              setError('Authentication state is being refreshed. Please try again in a moment.');
              toast({
                title: 'Session Refreshing',
                description: 'Your session is being refreshed. Please try again in a moment.',
                variant: 'default',
              });
              return;
            }

            // If we don't have local storage auth, we can safely redirect
            // Store the current URL to redirect back after authentication
            const currentUrl = window.location.href;

            console.log('[DEBUG] Redirecting to sign-in with redirect URL:', currentUrl);

            // Redirect to sign-in with the current URL as the redirect target
            window.location.href = `/sign-in?redirect_url=${encodeURIComponent(currentUrl)}`;
            return;
          } catch (authCheckError) {
            console.error('[DEBUG] Error checking auth state:', authCheckError);
            // If there's an error checking auth state, set a generic error
            setError('Authentication error. Please try refreshing the page.');
            return;
          }
        }

        if (!eventResponse || !eventResponse.success || !eventResponse.data) {
          console.error('[DEBUG] Failed to load event:', eventResponse?.error);
          setError(eventResponse?.error || 'Failed to load event');
          toast({
            title: 'Error',
            description: 'Failed to load event data. Please try again.',
            variant: 'destructive',
          });
          return;
        }

        const event = eventResponse.data;
        console.log('[DEBUG] Loaded event data:', JSON.stringify(event, null, 2));

        // Check if the event is marked as unauthorized
        if ('_unauthorized' in event && event._unauthorized) {
          console.warn('[DEBUG] Event is marked as unauthorized, but proceeding with display');
          // We'll still show the event but might disable editing
        }

        // Load categories for this event
        try {
          console.log('[DEBUG] Starting to load categories for event ID:', eventId);
          const categoriesResponse = await getEventCategories(eventId);
          console.log('[DEBUG] Categories response:', categoriesResponse);

          if (categoriesResponse.success && categoriesResponse.data) {
            console.log('[DEBUG] Loaded categories:', JSON.stringify(categoriesResponse.data, null, 2));

            // Log each category's price information for debugging
            if (Array.isArray(categoriesResponse.data)) {
              categoriesResponse.data.forEach((category, index) => {
                console.log(`[DEBUG] Category ${index + 1} (${category.name || 'unnamed'}) price info:`, {
                  directPrice: category.properties?.price,
                  propertiesPrice: category.properties?.price,
                  propertiesObject: category.properties
                });
              });
            }

            // Add categories to the event object
            event.categories = categoriesResponse.data;
          } else if (categoriesResponse.error) {
            console.warn('[DEBUG] Categories loaded with error:', categoriesResponse.error);
            // Initialize with empty array to prevent null errors
            event.categories = [];
          }
        } catch (categoryError) {
          console.error('[DEBUG] Error loading categories:', categoryError);
          // Initialize with empty array to prevent null errors
          event.categories = [];
        }

        // Load custom fields for this event
        try {
          console.log('[DEBUG] Starting to load fields for event ID:', eventId);
          const fieldsResponse = await getEventFields(eventId);
          console.log('[DEBUG] Fields response:', fieldsResponse);

          if (fieldsResponse.success && fieldsResponse.data) {
            console.log('[DEBUG] Loaded custom fields:', fieldsResponse.data);
            // Add custom fields to the event object
            event.customFields = fieldsResponse.data;
          } else if (fieldsResponse.error) {
            console.warn('[DEBUG] Fields loaded with error:', fieldsResponse.error);
            // Initialize with empty array to prevent null errors
            event.customFields = [];
          }
        } catch (fieldsError) {
          console.error('[DEBUG] Error loading custom fields:', fieldsError);
          // Initialize with empty array to prevent null errors
          event.customFields = [];
        }

        // Set the event with all its related data
        console.log('[DEBUG] Setting existingEvent with complete data:', {
          id: event.id,
          title: event.title,
          hasCategories: Array.isArray(event.categories) && event.categories.length > 0,
          categoriesCount: Array.isArray(event.categories) ? event.categories.length : 0,
          hasCustomFields: Array.isArray(event.customFields) && event.customFields.length > 0,
          customFieldsCount: Array.isArray(event.customFields) ? event.customFields.length : 0
        });
        setExistingEvent(event);

      } catch (err) {
        console.error('[DEBUG] Error loading event:', err);
        setError('An unexpected error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load event data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        // Always update loading state regardless of background refresh
        // This ensures the wizard doesn't get stuck in loading state
        console.log('[DEBUG] Setting loading to false after event load');
        setLoading(false);
      }
    }

    loadEvent();
  }, [eventId, searchParams]);

  // Add effect to monitor existingEvent changes
  useEffect(() => {
    if (existingEvent) {
      console.log('[DEBUG] existingEvent state updated:', {
        id: existingEvent.id,
        title: existingEvent.title,
        hasCategories: Array.isArray(existingEvent.categories) && existingEvent.categories.length > 0,
        categoriesCount: Array.isArray(existingEvent.categories) ? existingEvent.categories.length : 0,
        hasCustomFields: Array.isArray(existingEvent.customFields) && existingEvent.customFields.length > 0,
        customFieldsCount: Array.isArray(existingEvent.customFields) ? existingEvent.customFields.length : 0
      });
    }
  }, [existingEvent]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading event data...</p>
        </div>
      </div>
    );
  }

  if (error && eventId) {
    console.log('[DEBUG] Rendering error state for event ID:', eventId, 'Error:', error);

    // Check if it's a network error
    const isNetworkError = error.includes('Failed to fetch') || error.includes('Network error');

    return (
      <div className="bg-destructive/10 p-6 rounded-lg text-center">
        <h3 className="text-xl font-semibold mb-2">Error Loading Event</h3>
        <p className="text-muted-foreground mb-4">{error}</p>

        {isNetworkError ? (
          <>
            <p className="mb-4">There seems to be a network issue. Please check your internet connection.</p>
            <button
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </button>
          </>
        ) : (
          <p>Please go back and try again, or create a new event.</p>
        )}
      </div>
    );
  }

  console.log('[DEBUG] Rendering EventWizard with data:', {
    eventId,
    hasExistingEvent: !!existingEvent,
    eventTitle: existingEvent?.title || 'New Event',
    eventTypesCount: eventTypes?.length || 0
  });

  return <EventWizard eventTypes={eventTypes} existingEvent={existingEvent} pageTitle={pageTitle} />;
}
