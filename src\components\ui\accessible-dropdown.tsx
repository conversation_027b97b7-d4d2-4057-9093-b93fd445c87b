'use client'

import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"
import { createPortal } from "react-dom"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoreHorizontal } from "lucide-react"

interface AccessibleDropdownProps {
  children: React.ReactNode
  buttonLabel: string
  buttonIcon?: React.ReactNode
  align?: "start" | "center" | "end"
}

export function AccessibleDropdown({
  children,
  buttonLabel,
  buttonIcon = <MoreHorizontal className="h-4 w-4" />,
  align = "end"
}: AccessibleDropdownProps) {
  const [open, setOpen] = React.useState(false)
  const buttonRef = React.useRef<HTMLButtonElement>(null)
  const dropdownRef = React.useRef<HTMLDivElement>(null)
  const [mounted, setMounted] = React.useState(false)
  const [dropdownPosition, setDropdownPosition] = React.useState({ top: 0, left: 0 })
  const positionRef = React.useRef({ top: 0, left: 0 })

  // Use this to avoid unnecessary re-renders
  const setPositionWithoutRerender = (position: { top: number, left: number }) => {
    positionRef.current = position;
    setDropdownPosition(position);
  }

  // Set mounted state after component mounts (for SSR compatibility)
  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Calculate dropdown position when buttonRect changes or when dropdown opens
  const updateDropdownPosition = React.useCallback(() => {
    if (open && buttonRef.current && dropdownRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const viewportMargin = 16; // Margin from viewport edges
      const dropdownWidth = dropdownRef.current.offsetWidth;
      const dropdownHeight = dropdownRef.current.offsetHeight;

      // Get current scroll position
      const scrollX = window.scrollX || window.pageXOffset;
      const scrollY = window.scrollY || window.pageYOffset;

      // Calculate absolute position of the button in the document
      const buttonAbsoluteTop = buttonRect.top + scrollY;
      const buttonAbsoluteBottom = buttonRect.bottom + scrollY;
      const buttonAbsoluteLeft = buttonRect.left + scrollX;
      const buttonAbsoluteRight = buttonRect.right + scrollX;

      // Initial position calculation - position below the button
      let top = buttonAbsoluteBottom + 8;
      let left = align === "end"
        ? buttonAbsoluteRight - dropdownWidth
        : align === "center"
          ? buttonAbsoluteLeft + (buttonRect.width / 2) - (dropdownWidth / 2)
          : buttonAbsoluteLeft;

      // Ensure dropdown stays within viewport horizontally
      const viewportWidth = window.innerWidth;
      if (left < scrollX + viewportMargin) {
        left = scrollX + viewportMargin;
      } else if (left + dropdownWidth > scrollX + viewportWidth - viewportMargin) {
        left = scrollX + viewportWidth - dropdownWidth - viewportMargin;
      }

      // Ensure dropdown stays within viewport vertically
      const viewportHeight = window.innerHeight;
      const visibleButtonBottom = Math.min(buttonRect.bottom, viewportHeight);
      const visibleButtonTop = Math.max(buttonRect.top, 0);

      // Space below the button in the viewport
      const bottomSpace = viewportHeight - visibleButtonBottom;

      // Space above the button in the viewport
      const topSpace = visibleButtonTop;

      // If not enough space below, and more space above, show above the button
      if (dropdownHeight > bottomSpace - viewportMargin && topSpace > bottomSpace && topSpace > dropdownHeight + viewportMargin) {
        top = buttonAbsoluteTop - dropdownHeight - 8;
      }

      // If dropdown would be outside viewport at the top, position at top of viewport with margin
      if (top < scrollY + viewportMargin) {
        top = scrollY + viewportMargin;
      }

      // If dropdown would be outside viewport at the bottom, position at bottom of viewport with margin
      if (top + dropdownHeight > scrollY + viewportHeight - viewportMargin) {
        // Only adjust if there's enough height for the dropdown
        if (viewportHeight > dropdownHeight + (viewportMargin * 2)) {
          top = scrollY + viewportHeight - dropdownHeight - viewportMargin;
        }
      }

      // Only update state if position has changed significantly (1px threshold)
      if (
        Math.abs(positionRef.current.top - top) > 1 ||
        Math.abs(positionRef.current.left - left) > 1
      ) {
        setPositionWithoutRerender({ top, left });
      }
    }
  }, [open, align]);

  // Update position when dropdown opens
  React.useEffect(() => {
    if (open) {
      updateDropdownPosition();
    }
    return undefined; // Explicit return to fix TS7030 error
  }, [open, updateDropdownPosition]);

  // Continuously update position using requestAnimationFrame with throttling
  React.useEffect(() => {
    if (open) {
      let animationFrameId: number;
      let lastUpdateTime = 0;
      const throttleInterval = 1000 / 30; // Limit to 30 updates per second

      const updatePosition = (timestamp: number) => {
        // Throttle updates to reduce CPU usage
        if (timestamp - lastUpdateTime > throttleInterval) {
          updateDropdownPosition();
          lastUpdateTime = timestamp;
        }
        animationFrameId = requestAnimationFrame(updatePosition);
      };

      // Start the animation frame loop
      animationFrameId = requestAnimationFrame(updatePosition);

      // Also listen for scroll and resize events to ensure we catch all position changes
      const handleEvent = () => {
        updateDropdownPosition();
      };

      window.addEventListener('resize', handleEvent);
      window.addEventListener('scroll', handleEvent, true);

      return () => {
        // Clean up
        cancelAnimationFrame(animationFrameId);
        window.removeEventListener('resize', handleEvent);
        window.removeEventListener('scroll', handleEvent, true);
      };
    }

    return undefined; // Explicit return for non-open case
  }, [open, updateDropdownPosition]);

  // Close the dropdown when Escape is pressed
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open) {
        setOpen(false)
        buttonRef.current?.focus()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [open])

  // Close the dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        open &&
        dropdownRef.current &&
        buttonRef.current &&
        !dropdownRef.current.contains(e.target as Node) &&
        !buttonRef.current.contains(e.target as Node)
      ) {
        setOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [open])

  return (
    <div className="relative">
      <Button
        ref={buttonRef}
        variant="ghost"
        size="icon"
        aria-label={buttonLabel}
        aria-expanded={open}
        aria-haspopup="menu"
        onClick={() => setOpen(!open)}
      >
        {buttonIcon}
        <span className="sr-only">{buttonLabel}</span>
      </Button>

      {open && mounted && createPortal(
        <div
          ref={dropdownRef}
          className={cn(
            "fixed z-[100] min-w-[8rem] overflow-hidden rounded-md border border-border",
            "bg-popover text-popover-foreground p-1 shadow-md backdrop-blur-none",
            "animate-in fade-in-0 zoom-in-95"
          )}
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            maxHeight: 'calc(100vh - 32px)',
            overflowY: 'auto',
            backgroundColor: 'hsl(var(--popover))',
          }}
          role="menu"
          aria-orientation="vertical"
        >
          {children}
        </div>,
        document.body
      )}
    </div>
  )
}

interface AccessibleDropdownItemProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  destructive?: boolean
}

export function AccessibleDropdownItem({
  className,
  children,
  destructive = false,
  ...props
}: AccessibleDropdownItemProps) {
  return (
    <button
      className={cn(
        "relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none",
        "transition-colors hover:bg-accent hover:text-accent-foreground",
        "focus-visible:bg-accent focus-visible:text-accent-foreground",
        "data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
        destructive && "text-destructive hover:bg-destructive/10",
        className
      )}
      role="menuitem"
      {...props}
    >
      {children}
    </button>
  )
}
