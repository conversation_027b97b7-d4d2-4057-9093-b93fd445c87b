# Fuiyoo Design System

## Brand Colors
- **Primary**: <PERSON> (#10b981)
- **Secondary**: <PERSON> Blue (#1e40af)
- **Accent**: Orange (#f97316)
- **Neutral**: <PERSON> (#6b7280)
- **Success**: <PERSON> (#22c55e)
- **Warning**: <PERSON> (#f59e0b)
- **Error**: <PERSON> (#ef4444)
- **Info**: <PERSON> (#0ea5e9)

## Chart Colors
- Chart 1: #10b981 (Primary Green)
- Chart 2: #3b82f6 (Blue)
- Chart 3: #f97316 (Orange)
- Chart 4: #8b5cf6 (<PERSON>)
- Chart 5: #ec4899 (Pink)

## Typography
- **Headings**: Inter (sans-serif)
  - h1: 2.25rem (36px), font-weight: 700
  - h2: 1.875rem (30px), font-weight: 700
  - h3: 1.5rem (24px), font-weight: 600
  - h4: 1.25rem (20px), font-weight: 600
  - h5: 1.125rem (18px), font-weight: 600
  - h6: 1rem (16px), font-weight: 600

- **Body**: <PERSON><PERSON><PERSON>
  - Regular text: 1rem (16px), font-weight: 400
  - Small text: 0.875rem (14px), font-weight: 400
  - Tiny text: 0.75rem (12px), font-weight: 400

## Spacing System
- **Base unit**: 0.25rem (4px)
- **Scale**:
  - 1: 0.25rem (4px)
  - 2: 0.5rem (8px)
  - 3: 0.75rem (12px)
  - 4: 1rem (16px)
  - 5: 1.25rem (20px)
  - 6: 1.5rem (24px)
  - 8: 2rem (32px)
  - 10: 2.5rem (40px)
  - 12: 3rem (48px)
  - 16: 4rem (64px)
  - 20: 5rem (80px)
  - 24: 6rem (96px)

## Border Radius
- **Small**: 0.125rem (2px)
- **Default**: 0.25rem (4px)
- **Medium**: 0.375rem (6px)
- **Large**: 0.5rem (8px)
- **XL**: 0.75rem (12px)
- **2XL**: 1rem (16px)
- **Full**: 9999px (rounded)

## Shadows
- **SM**: 0 1px 2px 0 rgb(0 0 0 / 0.05)
- **Default**: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)
- **MD**: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)
- **LG**: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)
- **XL**: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)

## Component Styles

### Buttons
- **Primary**: Emerald Green background, white text
- **Secondary**: Gray background, dark text
- **Outline**: White background, colored border matching text
- **Ghost**: Transparent background, colored text
- **Link**: No background or border, colored text with underline on hover
- **Destructive**: Red background, white text

### Cards
- White background
- Light shadow
- Border radius: 0.5rem (8px)
- Padding: 1.5rem (24px)

### Forms
- **Inputs**: Gray border, rounded corners
- **Focus state**: Blue outline
- **Error state**: Red border, error message below
- **Disabled state**: Light gray background, muted text

### Tables
- Alternating row colors: white and very light gray
- Header row: Medium gray background
- Border: Light gray
- Padding: 0.75rem (12px) vertical, 1rem (16px) horizontal

### Navigation
- **Primary Navigation**: Dark background
- **Sidebar**: White background with subtle shadow
- **Mobile Navigation**: Bottom fixed navigation on small screens

## Responsive Breakpoints
- **SM**: 640px
- **MD**: 768px
- **LG**: 1024px
- **XL**: 1280px
- **2XL**: 1536px

## Animation
- **Duration**: 
  - Fast: 150ms
  - Default: 300ms
  - Slow: 500ms
- **Easing**: 
  - Default: cubic-bezier(0.4, 0, 0.2, 1)
  - In: cubic-bezier(0.4, 0, 1, 1)
  - Out: cubic-bezier(0, 0, 0.2, 1)
  - In-Out: cubic-bezier(0.4, 0, 0.2, 1)

## Accessibility
- Minimum contrast ratio: 4.5:1 for normal text
- Focus styles clearly visible
- Interactive elements have appropriate hover/focus states
- Appropriate use of semantic HTML elements
- ARIA attributes for complex components

## Implementation Details
- Using Tailwind CSS for styling
- Using shadcn/ui components as a base
- Custom Tailwind theme extends default configuration
- Global CSS variables for theming 