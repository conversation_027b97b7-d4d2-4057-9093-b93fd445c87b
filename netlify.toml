[build]
  command = "./netlify-prebuild.sh && pnpm build"
  publish = ".next"
  ignore = "git diff --quiet $CACHED_COMMIT_REF $COMMIT_REF ./src"

[build.environment]
  NETLIFY_USE_PNPM = "true"
  NODE_VERSION = "22"
  NPM_FLAGS = "--version" # Prevent Netlify npm install
  PNPM_FLAGS = "--no-frozen-lockfile" # Allow pnpm to update dependencies
  NEXT_TELEMETRY_DISABLED = "1"
  NEXT_FORCE_EDGE_IMAGES = "true"
  NODE_OPTIONS = "--max-old-space-size=4096 --enable-source-maps"
  NETLIFY_NEXT_PLUGIN_SKIP = "true" # Skip the Next.js plugin
  SKIP_ENV_VALIDATION = "true" # Skip Next.js built-in env validation
  NEXT_USE_NETLIFY_EDGE = "true" # Use Netlify edge functions

# Production context: all deploys from the Production branch set in your site's
# deploy contexts will inherit these settings.
[context.production.environment]
  NEXT_PUBLIC_SITE_URL = "https://fuiyoo.netlify.app"
  NODE_ENV = "production"

# Deploy Preview context: all deploys generated from a pull/merge request will
# inherit these settings.
[context.deploy-preview.environment]
  NODE_ENV = "staging"

# Branch deploy context: all deploys that are not from a pull/merge request or
# from the Production branch will inherit these settings.
[context.branch-deploy.environment]
  NODE_ENV = "staging"

# Specific branch context: all deploys from this specific branch will inherit
# these settings.
[context.staging.environment]
  NEXT_PUBLIC_SITE_URL = "https://staging--fuiyoo.netlify.app"
  NODE_ENV = "staging"

[build.processing]
  skip_processing = true

[build.processing.css]
  bundle = false
  minify = false

[build.processing.js]
  bundle = false
  minify = false

[build.processing.html]
  pretty_urls = true

[build.processing.images]
  compress = false

# Add caching headers for better performance
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/_next/image/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*"
  [headers.values]
    Content-Security-Policy = "default-src 'self' 'unsafe-inline' 'unsafe-eval' data:; img-src 'self' data: blob: *.pexels.com images.pexels.com lh3.googleusercontent.com *.googleusercontent.com eibzxudhnojsdxksgowo.supabase.co; font-src 'self' data:; connect-src 'self' https://*.netlify.app https://*.supabase.co https://*.googleapis.com https://*.stripe.com https://*.algolia.net https://*.algolia.io;"
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), interest-cohort=()"

[functions]
  node_bundler = "esbuild"
  external_node_modules = ["next", "sharp"]

[dev]
  command = "pnpm dev"
  port = 3001
  targetPort = 3000
  publish = ".next"
  framework = "#custom"
  autoLaunch = true
  processTimeout = 120

# Handle authentication redirects
[[redirects]]
  from = "/auth/callback"
  to = "/auth/callback"
  status = 200

# Redirect API requests to the server
[[redirects]]
  from = "/api/*"
  to = "/api/:splat"
  status = 200
  force = true

