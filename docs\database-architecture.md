# Fuiyoo Database Architecture

## Overview

Fuiyoo uses Supabase (PostgreSQL) as its primary database for all data storage needs. This document outlines the database architecture, access patterns, and best practices for working with the database.

## Technology Stack

- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Data Access**: Supabase JavaScript SDK
- **Schema Validation**: Zod
- **Type Safety**: TypeScript with generated types

## Architecture Principles

1. **Integrated Authentication and Data Storage**:
   - Supabase Auth handles all authentication and user management
   - Supabase is used for both authentication and data storage
   - Unified system for better security and performance

2. **Type Safety**:
   - Generated TypeScript types from database schema
   - Runtime validation with Zod
   - Consistent type usage across the application

3. **Authorization**:
   - Row-level security (RLS) policies in Supabase
   - Additional application-level authorization checks
   - Tenant isolation through data access patterns

## Database Schema

The database consists of several core tables:

1. **users**
   - Core user profile data
   - Linked with Supabase Auth via auth_user_id
   - Contains profile information and preferences

2. **events**
   - Event management data
   - Relationships with users and organizations

3. **organizations**
   - Organization management
   - Multi-tenant support

4. **ticket_types**
   - Event ticket configurations

5. **registrations**
   - Event registration records

6. **payments**
   - Payment transaction records

7. **collections**
   - Physical item collection tracking

## Data Access Patterns

### Server-Side Access

For server components and API routes, we use the server-side Supabase client:

```typescript
import { createClient } from '@/lib/supabase/server';

export async function getData() {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from('table_name')
    .select('*')
    .eq('user_id', userId);

  // Handle error and return data
}
```

### Client-Side Access

For client components, we use the browser-side Supabase client:

```typescript
'use client';

import { supabase } from '@/lib/supabase/client';

export function ClientComponent() {
  const fetchData = async () => {
    const { data, error } = await supabase
      .from('table_name')
      .select('*')
      .eq('user_id', userId);

    // Handle error and use data
  };

  // Component logic
}
```

## Authorization Checks

All data access should include proper authorization checks:

1. **Row-Level Security (RLS)**:
   - Configured in Supabase to restrict access based on user ID
   - Ensures users can only access their own data

2. **Application-Level Checks**:
   - Additional checks in server actions and API routes
   - Verification of user roles and permissions

Example RLS policy:

```sql
CREATE POLICY "Users can only access their own data"
ON public.user_data
FOR ALL
USING (auth.uid() = user_id);
```

Example application-level check:

```typescript
async function updateUserData(userId: string, data: UserData) {
  // Check if the current user is authorized
  const session = await auth();
  if (session?.userId !== userId) {
    throw new Error('Unauthorized');
  }

  // Proceed with update
  const supabase = await createClient();
  const { data: result, error } = await supabase
    .from('user_data')
    .update(data)
    .eq('user_id', userId);

  // Handle result
}
```

## Best Practices

1. **Always use typed queries**:
   ```typescript
   const { data } = await supabase
     .from('users')
     .select('id, name, email')
     .eq('id', userId)
     .returns<UserProfile>();
   ```

2. **Validate input data with Zod**:
   ```typescript
   const schema = z.object({
     name: z.string().min(2),
     email: z.string().email(),
   });

   const validated = schema.parse(inputData);
   ```

3. **Use repository pattern for data access**:
   ```typescript
   // In repository file
   export class UserRepository {
     async getUserById(id: string) {
       const supabase = await createClient();
       return supabase.from('users').select('*').eq('id', id).single();
     }
   }

   // In business logic
   const userRepo = new UserRepository();
   const user = await userRepo.getUserById(userId);
   ```

4. **Handle errors consistently**:
   ```typescript
   const { data, error } = await supabase.from('users').select('*');

   if (error) {
     console.error('Database error:', error);
     throw new Error(`Failed to fetch users: ${error.message}`);
   }

   return data;
   ```

## Migration Strategy

Database migrations are managed through SQL files in the `src/db/migrations` directory. Each migration is versioned and applied in sequence.

To create a new migration:

1. Create a new SQL file in the migrations directory
2. Follow the naming convention: `{version}_{name}.sql`
3. Include a transaction to ensure atomicity
4. Register the migration in the `schema_migrations` table

## Conclusion

By following these patterns and best practices, we ensure a consistent, type-safe, and secure approach to database access throughout the Fuiyoo application.
