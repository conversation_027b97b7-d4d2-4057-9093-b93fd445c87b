import { Skeleton } from '../../components/ui/skeleton';

export default function EventsLoading() {
  return (
    <main className="min-h-screen pb-16 pt-8">
      <div className="container max-w-5xl mx-auto px-4">
        <Skeleton className="h-10 w-48 mb-8" />
        
        {/* Search skeleton */}
        <div className="space-y-4 mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            <Skeleton className="h-10 flex-grow" />
            <div className="flex flex-col sm:flex-row gap-4">
              <Skeleton className="h-10 w-[180px]" />
              <Skeleton className="h-10 w-[180px]" />
              <Skeleton className="h-10 w-24" />
            </div>
          </div>
        </div>
        
        <Skeleton className="h-8 w-48 mb-6" />
        
        {/* Event card skeletons */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="border rounded-lg overflow-hidden">
              <Skeleton className="h-48 w-full" />
              <div className="p-4 space-y-4">
                <Skeleton className="h-6 w-4/5" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                
                <div className="space-y-2">
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </main>
  );
} 