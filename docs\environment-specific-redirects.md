# Environment-Specific Redirects in Fuiyoo

This document explains how authentication redirects are handled across different environments (local development, staging, and production) in the Fuiyoo application.

## Problem

When users authenticate in one environment (e.g., staging), they should be redirected back to the same environment after authentication. Previously, there was an issue where users authenticating on the staging environment (`staging--fuiyoo.netlify.app`) were being redirected to the local development environment (`localhost:3000`).

## Solution

We've implemented a comprehensive solution to ensure that authentication redirects always stay within the same environment:

1. **Environment Detection**: The application now properly detects the current environment (local, staging, production) using:
   - Client-side: `window.location.origin`
   - Server-side: Environment variables and Netlify-specific environment variables

2. **URL Utilities**: We've consolidated all URL utility functions to be environment-aware:
   - `getBaseUrl()`: Returns the correct base URL for the current environment
   - `getAuthCallbackUrl()`: Returns the correct callback URL for authentication
   - `fixCrossEnvironmentRedirect()`: Detects and fixes cross-environment redirects

3. **Auth Callback Route**: The `/auth/callback` route now checks for and prevents cross-environment redirects

4. **Unified Middleware**: The authentication middleware (now at the root level) ensures redirect URLs stay within the current environment

## Environment Configuration

Each environment has its own configuration file with the appropriate URLs:

### Local Development (.env.development)
```
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_AUTH_REDIRECT_URL=http://localhost:3000/auth/callback
```

### Staging (.env.staging)
```
NEXT_PUBLIC_SITE_URL=https://staging--fuiyoo.netlify.app
NEXT_PUBLIC_AUTH_REDIRECT_URL=https://staging--fuiyoo.netlify.app/auth/callback
```

### Production (.env.production)
```
NEXT_PUBLIC_SITE_URL=https://fuiyoo.netlify.app
NEXT_PUBLIC_AUTH_REDIRECT_URL=https://fuiyoo.netlify.app/auth/callback
```

## How It Works

1. **Client-Side Authentication**:
   - When a user signs in, the application uses `window.location.origin` to determine the current environment
   - This ensures the auth callback URL is always for the current environment
   - The `getBaseUrl()` function in `src/utils/url-utilities.ts` handles this logic

2. **Server-Side Authentication**:
   - The server uses environment variables to determine the correct URLs
   - If environment variables are not available, it falls back to environment-specific defaults
   - Netlify-specific environment variables (`CONTEXT`, `BRANCH`) are used to detect staging vs. production

3. **Cross-Environment Detection**:
   - The `fixCrossEnvironmentRedirect()` function in `src/utils/url-utilities.ts` detects if a redirect URL is for a different environment
   - If a cross-environment redirect is detected, it's fixed to stay within the current environment
   - This function is used in both the middleware and auth callback route

4. **Unified Middleware**:
   - The middleware at the root level (`middleware.ts`) handles all authentication and redirects
   - It uses the environment-specific URL utilities to ensure redirects stay within the same environment
   - The middleware also handles redirect loop prevention with the `no_redirect` parameter

## Testing

To test that redirects work correctly across environments:

1. **Local Development**:
   - Sign in at `http://localhost:3000/sign-in`
   - You should be redirected back to `http://localhost:3000/dashboard`

2. **Staging**:
   - Sign in at `https://staging--fuiyoo.netlify.app/sign-in`
   - You should be redirected back to `https://staging--fuiyoo.netlify.app/dashboard`

3. **Production**:
   - Sign in at `https://fuiyoo.netlify.app/sign-in`
   - You should be redirected back to `https://fuiyoo.netlify.app/dashboard`

## Troubleshooting

If you encounter redirect issues:

1. Check the browser console for logs about redirect URLs
2. Verify that the correct environment variables are loaded
3. Clear browser cookies and try again
4. Check for any hardcoded URLs in the codebase
5. Verify that the middleware is correctly loaded (should be at the root level, not in src/)
6. Check Supabase redirect URL configuration in the Supabase dashboard

## Implementation Details

### Key Files

- `middleware.ts` (root level): Main authentication middleware that handles redirects
- `src/utils/url-utilities.ts`: Consolidated URL utility functions for environment detection, base URL determination, authentication callback URLs, and cross-environment redirect prevention
- `src/app/auth/callback/route.ts`: Handles authentication callbacks from Supabase

### Environment Variables

Make sure these environment variables are set correctly in each environment:

- `NEXT_PUBLIC_SITE_URL`: The base URL for the application
- `NEXT_PUBLIC_AUTH_REDIRECT_URL`: The callback URL for authentication
- `NEXT_PUBLIC_SUPABASE_URL`: The URL of your Supabase project
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: The anonymous key for your Supabase project

### Supabase Configuration

In your Supabase project, make sure to configure the following redirect URLs:

1. Site URL: `https://fuiyoo.netlify.app`
2. Additional Redirect URLs:
   - `http://localhost:3000/**`
   - `https://staging--fuiyoo.netlify.app/**`

This ensures that Supabase will allow redirects to all your environments.
