'use client'
import React from 'react';
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { MultiSelect } from "@/components/ui/multiselect";
import Image from "next/image";
import Select from 'react-select'
import { getCountries, getStates, type Country, type State, FALLBACK_COUNTRIES, FALLBACK_STATES } from '@/data/countries'
import { use<PERSON><PERSON>, Controller, Control } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { createClient } from '@/lib/supabase/client';

interface SelectOption {
  value: string
  label: string
  icon?: string
}

type UserProfile = {
  id: string;
  first_name: string;
  last_name?: string;
  username?: string;
  gender?: string;
  bio?: string;
  avatar?: string;
  isPublic: number;
  stats?: {
    eventsAttended?: number;
    eventsHosted?: number;
    categories?: string[];
  };
  nationality?: string;
  ic?: string;
  passport?: string;
  dateOfBirth?: string;
  contactNo?: string;
  address?: string;
  apartment?: string;
  city?: string;
  postcode?: string;
  country?: string;
  state?: string;
  emergencyContactName?: string;
  emergencyContactNo?: string;
  emergencyContactRelationship?: string;
  tshirtSize?: string;
};

interface ProfileEditFormProps {
  profile: UserProfile;
  onCancel: () => void;
  onSuccess?: () => void;
}

const profileSchema = z.object({
  nationality: z.string().optional(),
  country: z.string().optional(),
  state: z.string().optional(),
  tshirtSize: z.string().optional(),
  // ... other fields
});

type ProfileFormData = z.infer<typeof profileSchema>;

interface ControlledSelectProps {
  name: keyof ProfileFormData;
  control: Control<ProfileFormData>;
  options: SelectOption[];
  placeholder: string;
  isDisabled?: boolean;
  isLoading?: boolean;
  onChange?: (value: string | undefined) => void;
}

const ControlledSelect = ({
  name,
  control,
  options,
  placeholder,
  isDisabled,
  isLoading,
  onChange
}: ControlledSelectProps) => (
  <Controller
    name={name}
    control={control}
    render={({ field }) => (
      <Select
        {...field}
        className="react-select"
        classNamePrefix="react-select"
        options={options}
        placeholder={placeholder}
        isClearable
        isDisabled={isDisabled}
        isLoading={isLoading}
        value={options.find(x => x.value === field.value) || null}
        onChange={(option) => {
          field.onChange(option?.value);
          onChange?.(option?.value);
        }}
        formatOptionLabel={(option) => (
          <div className="flex items-center">
            {option.icon && <span className="mr-2">{option.icon}</span>}
            <span>{option.label}</span>
          </div>
        )}
      />
    )}
  />
);

export function ProfileEditForm({ profile, onCancel, onSuccess }: ProfileEditFormProps) {
  const supabase = createClient();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<UserProfile>({ ...profile });
  const [categories, setCategories] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("basic");
  const [countryOptions, setCountryOptions] = useState<SelectOption[]>([]);
  const [stateOptions, setStateOptions] = useState<SelectOption[]>([]);
  const [isLoadingStates, setIsLoadingStates] = useState(false);
  const [authUser, setAuthUser] = useState<any>(null);

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      nationality: profile.nationality,
      country: profile.country,
      state: profile.state,
      tshirtSize: profile.tshirtSize,
      // ... other fields
    }
  });

  // Fetch auth user data and sync contact number from metadata
  useEffect(() => {
    const fetchAuthUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setAuthUser(user);

        // Check if phone number exists in user metadata
        const phoneNumber = user.user_metadata?.phone_number || user.phone || '';
        if (phoneNumber) {
          setFormData(prev => ({
            ...prev,
            contactNo: phoneNumber
          }));
        }
      }
    };

    fetchAuthUser();
  }, [supabase.auth]);

  // Fetch available categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        if (!response.ok) throw new Error('Failed to fetch categories');
        const data = await response.json();
        setCategories(data.map((cat: any) => cat.name));
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast({
          title: "Error",
          description: "Failed to load categories. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchCategories();
  }, []);

  // Fetch countries on mount
  useEffect(() => {
    const fetchCountryOptions = async () => {
      try {
        // Try to fetch countries but immediately fall back if any error occurs
        let countries: Country[] = [];
        try {
          countries = await getCountries();
        } catch {
          countries = FALLBACK_COUNTRIES;
        }

        // Map the countries to options - using flag as a separate property
        const options = countries.map((country: Country) => ({
          value: country.code,
          label: country.name,
          icon: country.flag
        }));

        setCountryOptions(options);
      } catch (error) {
        // Ultimate fallback - use FALLBACK_COUNTRIES directly
        const options = FALLBACK_COUNTRIES.map((country: Country) => ({
          value: country.code,
          label: country.name,
          icon: country.flag
        }));
        setCountryOptions(options);
        console.warn('Using fallback country options due to error');
      }
    };

    fetchCountryOptions();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleGenderChange = (value: string) => {
    setFormData({
      ...formData,
      gender: value
    });
  };

  const handleCategoriesChange = (selected: string[]) => {
    setFormData({
      ...formData,
      stats: {
        ...formData.stats,
        categories: selected
      }
    });
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData({
      ...formData,
      isPublic: checked ? 1 : 0
    });
  };

  const addCategory = (category: string) => {
    if (!category) return;

    const currentCategories = formData.stats?.categories || [];
    if (!currentCategories.includes(category)) {
      setFormData({
        ...formData,
        stats: {
          ...formData.stats,
          categories: [...currentCategories, category]
        }
      });
    }
  };

  const removeCategory = (category: string) => {
    const currentCategories = formData.stats?.categories || [];
    setFormData({
      ...formData,
      stats: {
        ...formData.stats,
        categories: currentCategories.filter(c => c !== category)
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          categories: formData.stats?.categories,
          tshirtSize: formData.tshirtSize
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(error || 'Failed to update profile');
      }

      toast({
        title: "Success",
        description: "Your profile has been updated successfully.",
      });

      if (onSuccess) {
        onSuccess();
      } else {
        onCancel();
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error",
        description: "Failed to update your profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Create FormData
      const formData = new FormData();
      formData.append('file', file);

      // Upload to your storage endpoint
      const response = await fetch('/api/upload/avatar', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload avatar');
      }

      const { url } = await response.json();

      // Update form data with new avatar URL
      setFormData(prev => ({
        ...prev,
        avatar: url
      }));

      toast({
        title: "Success",
        description: "Avatar uploaded successfully",
      });
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast({
        title: "Error",
        description: "Failed to upload avatar. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCountryChange = async (countryCode: string | undefined) => {
    if (!countryCode) {
      setStateOptions([]);
      return;
    }

    setIsLoadingStates(true);
    try {
      // Try to fetch states but immediately fall back if any error occurs
      let states: State[] = [];
      try {
        states = await getStates(countryCode);
      } catch {
        states = FALLBACK_STATES[countryCode] || [];
      }

      const options = states.map((state: State) => ({
        value: state.code,
        label: state.name
      }));

      setStateOptions(options);
    } catch (error) {
      // Ultimate fallback - use FALLBACK_STATES directly
      const fallbackStates = FALLBACK_STATES[countryCode] || [];
      const options = fallbackStates.map((state: State) => ({
        value: state.code,
        label: state.name
      }));
      setStateOptions(options);
      console.warn(`Using fallback states for ${countryCode} due to error`);
    } finally {
      setIsLoadingStates(false);
    }
  };

  const formatICNumber = (value: string) => {
    // Remove any non-numeric characters
    const numbers = value.replace(/\D/g, '');

    // Limit to 12 digits
    const limited = numbers.slice(0, 12);

    // Add dashes
    if (limited.length > 6) {
      return `${limited.slice(0, 6)}-${limited.slice(6, 8)}-${limited.slice(8)}`;
    } else if (limited.length > 0) {
      return limited;
    }
    return '';
  };

  return (
    <form onSubmit={handleSubmit}>
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Edit Profile</CardTitle>
          <CardDescription>
            Update your personal information and preferences
          </CardDescription>
        </CardHeader>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 w-full max-w-md mb-4 ml-6">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="personal">Personal</TabsTrigger>
            <TabsTrigger value="emergency">Emergency</TabsTrigger>
          </TabsList>

          <CardContent className="space-y-4">
            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2 text-center sm:text-left">
                  <Label htmlFor="first_name">First Name</Label>
                  <Input
                    id="first_name"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleChange}
                    required
                    className="text-center sm:text-left"
                  />
                </div>
                <div className="space-y-2 text-center sm:text-left">
                  <Label htmlFor="last_name">Last Name</Label>
                  <Input
                    id="last_name"
                    name="last_name"
                    value={formData.last_name || ''}
                    onChange={handleChange}
                    className="text-center sm:text-left"
                  />
                </div>
              </div>

              <div className="space-y-2 text-center sm:text-left">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  value={formData.username || ''}
                  onChange={handleChange}
                  className="text-center sm:text-left"
                />
              </div>

              <div className="space-y-2">
                <Label>Avatar</Label>
                <div className="flex items-center gap-4">
                  <div className="relative h-20 w-20 rounded-full overflow-hidden border">
                    <Image
                      src={formData.avatar || "/images/avatars/default.jpg"}
                      alt="Profile avatar"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <Input
                    id="avatar"
                    name="avatar"
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarChange}
                    className="max-w-[250px]"
                  />
                </div>
              </div>

              <div className="space-y-2 text-center sm:text-left">
                <Label>Gender</Label>
                <RadioGroup
                  value={formData.gender || ''}
                  onValueChange={handleGenderChange}
                  className="flex justify-center sm:justify-start space-x-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="male" id="male" />
                    <Label htmlFor="male">Male</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="female" id="female" />
                    <Label htmlFor="female">Female</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-2 text-center sm:text-left">
                <Label htmlFor="tshirtSize">T-shirt Size</Label>
                <select
                  id="tshirtSize"
                  name="tshirtSize"
                  value={formData.tshirtSize || ''}
                  onChange={(e) => {
                    setFormData({
                      ...formData,
                      tshirtSize: e.target.value
                    });
                  }}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="">Select size</option>
                  <option value="XS">XS</option>
                  <option value="S">S</option>
                  <option value="M">M</option>
                  <option value="L">L</option>
                  <option value="XL">XL</option>
                  <option value="XXL">XXL</option>
                  <option value="XXXL">XXXL</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label>Categories</Label>
                <MultiSelect
                  options={categories.map(cat => ({ label: cat, value: cat }))}
                  selected={(formData.stats?.categories || []).map(cat => ({ label: cat, value: cat }))}
                  onChange={(selected) => handleCategoriesChange(selected.map(s => s.value))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  name="bio"
                  placeholder="Tell us about yourself"
                  value={formData.bio || ''}
                  onChange={handleChange}
                  className="h-24"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="public-profile">Public Profile</Label>
                  <p className="text-sm text-muted-foreground">
                    Make your profile visible to other users
                  </p>
                </div>
                <Switch
                  id="public-profile"
                  checked={formData.isPublic === 1}
                  onCheckedChange={handleSwitchChange}
                />
              </div>
            </TabsContent>

            <TabsContent value="personal" className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="nationality">Nationality</Label>
                  <ControlledSelect
                    name="nationality"
                    control={form.control}
                    options={countryOptions}
                    placeholder="Select nationality..."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ic">IC Number</Label>
                  <Input
                    id="ic"
                    name="ic"
                    value={formData.ic ? formatICNumber(formData.ic) : ''}
                    onChange={(e) => {
                      const raw = e.target.value.replace(/\D/g, '');
                      setFormData(prev => ({
                        ...prev,
                        ic: raw
                      }));
                    }}
                    placeholder="e.g. 860528XXXXXX"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="passport">Passport</Label>
                  <Input
                    id="passport"
                    name="passport"
                    value={formData.passport || ''}
                    onChange={handleChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dateOfBirth">Date of Birth</Label>
                  <Input
                    id="dateOfBirth"
                    name="dateOfBirth"
                    type="date"
                    value={formData.dateOfBirth || ''}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="contactNo">Contact Number</Label>
                  <Input
                    id="contactNo"
                    name="contactNo"
                    value={formData.contactNo || ''}
                    onChange={handleChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    name="address"
                    value={formData.address || ''}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="apartment">Apartment/Unit</Label>
                  <Input
                    id="apartment"
                    name="apartment"
                    value={formData.apartment || ''}
                    onChange={handleChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    name="city"
                    value={formData.city || ''}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <ControlledSelect
                    name="country"
                    control={form.control}
                    options={countryOptions}
                    placeholder="Select country..."
                    onChange={handleCountryChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="state">State/Province</Label>
                  <ControlledSelect
                    name="state"
                    control={form.control}
                    options={stateOptions}
                    placeholder="Select state/province..."
                    isDisabled={!form.watch('country') || isLoadingStates}
                    isLoading={isLoadingStates}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="postcode">Postal Code</Label>
                <Input
                  id="postcode"
                  name="postcode"
                  value={formData.postcode || ''}
                  onChange={handleChange}
                />
              </div>
            </TabsContent>

            <TabsContent value="emergency" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="emergencyContactName">Emergency Contact Name</Label>
                <Input
                  id="emergencyContactName"
                  name="emergencyContactName"
                  value={formData.emergencyContactName || ''}
                  onChange={handleChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="emergencyContactNo">Emergency Contact Number</Label>
                <Input
                  id="emergencyContactNo"
                  name="emergencyContactNo"
                  value={formData.emergencyContactNo || ''}
                  onChange={handleChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="emergencyContactRelationship">Relationship</Label>
                <Input
                  id="emergencyContactRelationship"
                  name="emergencyContactRelationship"
                  value={formData.emergencyContactRelationship || ''}
                  onChange={handleChange}
                />
              </div>
            </TabsContent>
          </CardContent>
        </Tabs>

        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}
