import { Metadata } from 'next'
import { createClient } from '@/lib/supabase/pages-client'
import { redirect } from 'next/navigation'
import ProfileForm from './profile-form'

export const metadata: Metadata = {
  title: 'Profile | Fuiyoo',
  description: 'Manage your Fuiyoo profile',
}

export default async function ProfilePage() {
  // Check if user is authenticated
  const supabase = await createClient()
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    redirect('/sign-in')
  }
  
  // Get user profile data
  const { data: userData } = await supabase
    .from('users')
    .select('*')
    .eq('auth_user_id', session.user.id)
    .single()
  
  return (
    <div className="container mx-auto py-10">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Your Profile</h1>
        
        <div className="bg-white shadow-md rounded-lg p-6">
          <ProfileForm user={userData} />
        </div>
      </div>
    </div>
  )
}
