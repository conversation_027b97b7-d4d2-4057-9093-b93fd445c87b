'use server';

import { createClient } from '@/lib/supabase/server';
import { redirectToSignIn } from './redirect-utils';
import { setAuthResetCookie } from './cookie-utils';

/**
 * Error types for better error handling
 */
export enum ErrorType {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  NOT_FOUND = 'not_found',
  SERVER = 'server',
  DATABASE = 'database',
  NETWORK = 'network',
  UNKNOWN = 'unknown',
}

/**
 * Error response interface
 */
export interface ErrorResponse {
  type: ErrorType;
  message: string;
  status: number;
  details?: any;
}

/**
 * Create a standardized error response
 * @param type Error type
 * @param message Error message
 * @param status HTTP status code
 * @param details Additional error details
 * @returns Standardized error response
 */
export function createErrorResponse(
  type: ErrorType,
  message: string,
  status: number = 500,
  details?: any
): ErrorResponse {
  return {
    type,
    message,
    status,
    details,
  };
}

/**
 * Handle authentication errors
 * @param error The error to handle
 * @param redirectUrl The URL to redirect to after sign-in
 * @returns Never returns, redirects to sign-in
 */
export async function handleAuthError(error: any, redirectUrl?: string): Promise<never> {
  console.error('Authentication error:', error);
  
  // Set the auth reset cookie to force a fresh authentication
  await setAuthResetCookie();
  
  // Redirect to sign-in with error message
  redirectToSignIn(redirectUrl, {
    queryParams: {
      error: 'authentication_error',
      error_description: error.message || 'Authentication failed',
    },
  });
  
  // This is never reached due to the redirect
  throw new Error('Authentication error');
}

/**
 * Handle Supabase errors
 * @param error The Supabase error to handle
 * @returns Standardized error response
 */
export function handleSupabaseError(error: any): ErrorResponse {
  console.error('Supabase error:', error);
  
  // Check for authentication errors
  if (error.code === 'PGRST301' || error.code === 'PGRST302') {
    return createErrorResponse(
      ErrorType.AUTHENTICATION,
      'Authentication failed',
      401,
      error
    );
  }
  
  // Check for authorization errors
  if (error.code === 'PGRST401' || error.code === 'PGRST403') {
    return createErrorResponse(
      ErrorType.AUTHORIZATION,
      'You do not have permission to perform this action',
      403,
      error
    );
  }
  
  // Check for not found errors
  if (error.code === 'PGRST404') {
    return createErrorResponse(
      ErrorType.NOT_FOUND,
      'Resource not found',
      404,
      error
    );
  }
  
  // Check for validation errors
  if (error.code === 'PGRST400') {
    return createErrorResponse(
      ErrorType.VALIDATION,
      'Invalid request',
      400,
      error
    );
  }
  
  // Default to server error
  return createErrorResponse(
    ErrorType.SERVER,
    'An unexpected error occurred',
    500,
    error
  );
}

/**
 * Try to recover from an authentication error
 * @returns True if recovery was successful, false otherwise
 */
export async function tryAuthRecovery(): Promise<boolean> {
  try {
    const supabase = await createClient();
    
    // Try to refresh the session
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error || !data.session) {
      console.error('Auth recovery failed:', error);
      return false;
    }
    
    console.log('Auth recovery successful');
    return true;
  } catch (error) {
    console.error('Error during auth recovery:', error);
    return false;
  }
}

/**
 * Safely execute a function with error handling
 * @param fn The function to execute
 * @param errorHandler Optional custom error handler
 * @returns The result of the function or the error response
 */
export async function safeExecute<T>(
  fn: () => Promise<T>,
  errorHandler?: (error: any) => ErrorResponse
): Promise<T | ErrorResponse> {
  try {
    return await fn();
  } catch (error: any) {
    console.error('Error in safeExecute:', error);
    
    if (errorHandler) {
      return errorHandler(error);
    }
    
    // Default error handling
    if (error.code?.startsWith('PGRST')) {
      return handleSupabaseError(error);
    }
    
    return createErrorResponse(
      ErrorType.UNKNOWN,
      error.message || 'An unexpected error occurred',
      500,
      error
    );
  }
}
