'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { logger } from '@/lib/logger';

export default function AuthDebugger() {
  const [authStatus, setAuthStatus] = useState<string>('Checking...');
  const [userId, setUserId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClient();
        logger.debug('AuthDebugger: Supabase client created');

        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          logger.error('AuthDebugger: Session error', sessionError);
          setError(sessionError.message);
          setAuthStatus('Error');
          return;
        }

        if (session?.user) {
          logger.debug('AuthDebugger: User is authenticated', session.user);
          setAuthStatus('Authenticated');
          setUserId(session.user.id);
        } else {
          logger.debug('AuthDebugger: User is not authenticated');
          setAuthStatus('Not authenticated');
        }
      } catch (err) {
        logger.error('AuthDebugger: Unexpected error', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setAuthStatus('Error');
      }
    };

    checkAuth();
  }, []);

  return (
    <div className="p-4 bg-gray-100 rounded-md mb-4">
      <h3 className="font-bold mb-2">Auth Debugger</h3>
      <div className="space-y-2 text-sm">
        <p><strong>Status:</strong> {authStatus}</p>
        {userId && <p><strong>User ID:</strong> {userId}</p>}
        {error && <p className="text-red-500"><strong>Error:</strong> {error}</p>}
      </div>
    </div>
  );
}
