import { NextResponse } from "next/server";
import { createClient } from '@/lib/supabase/server';
import crypto from 'crypto';

// Type definitions for the registration process
interface UserProfileUpdateData {
  emergencyContactName?: string;
  emergencyContactNo?: string;
  emergencyContactRelationship?: string;
  updated_at?: string;
}

interface CategoryProperties {
  registrationCount?: number;
  [key: string]: unknown;
}

interface CategoryWithProperties {
  id: string;
  properties?: CategoryProperties | string;
  registration_count?: number;
  [key: string]: unknown;
}

export async function POST(request: Request) {
  try {
    // Get form data
    const formData = await request.formData();

    // Extract data from form
    const eventId = formData.get('eventId') as string;
    const categoryId = formData.get('categoryId') as string;
    const firstName = formData.get('firstName') as string;
    const lastName = formData.get('lastName') as string;
    const email = formData.get('email') as string;
    const phone = formData.get('phone') as string;
    const tshirtSize = formData.get('tshirtSize') as string;

    // Extract emergency contact information if provided
    const emergencyContactName = formData.get('emergencyContactName') as string;
    const emergencyContactNo = formData.get('emergencyContactNo') as string;
    const emergencyContactRelationship = formData.get('emergencyContactRelationship') as string;

    // Basic validation
    if (!eventId || !categoryId || !firstName || !email) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    // Get Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get the user's profile
    const { data: userProfile } = await supabase
      .from('users')
      .select('*')
      .eq('auth_user_id', user.id)
      .single();

    if (!userProfile) {
      return new NextResponse("User profile not found", { status: 404 });
    }

    // Generate a UUID for the registration
    const registrationId = crypto.randomUUID();

    // Create form data JSON
    const formDataJson = JSON.stringify({
      first_name: firstName,
      last_name: lastName,
      email,
      phone,
      tshirt_size: tshirtSize,
      emergency_contact_name: emergencyContactName || null,
      emergency_contact_no: emergencyContactNo || null,
      emergency_contact_relationship: emergencyContactRelationship || null
    });

    // Generate a ticket number
    const ticketNumber = `TIX-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

    // Generate a QR code (simplified for now)
    const qrCode = `${registrationId}-${ticketNumber}`;

    // Create registration record
    const { data: registration, error: registrationError } = await supabase
      .from('registrations')
      .insert({
        id: registrationId,
        event_id: eventId,
        user_id: userProfile.id,
        form_data: formDataJson,
        ticket_number: ticketNumber,
        qr_code: qrCode,
        ticket_type_id: categoryId, // Using category ID as ticket type ID
        attendance_status: 'pending',
        collection_status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (registrationError) {
      console.error("Error creating registration:", registrationError);
      return new NextResponse(`Registration failed: ${registrationError.message}`, { status: 500 });
    }

    // Update user profile with emergency contact info if provided and not already set
    if ((emergencyContactName || emergencyContactNo || emergencyContactRelationship) &&
      (!userProfile.emergencyContactName || !userProfile.emergencyContactNo || !userProfile.emergencyContactRelationship)) {

      const updateData: UserProfileUpdateData = {};

      if (emergencyContactName && !userProfile.emergencyContactName) {
        updateData.emergencyContactName = emergencyContactName;
      }

      if (emergencyContactNo && !userProfile.emergencyContactNo) {
        updateData.emergencyContactNo = emergencyContactNo;
      }

      if (emergencyContactRelationship && !userProfile.emergencyContactRelationship) {
        updateData.emergencyContactRelationship = emergencyContactRelationship;
      }

      // Only update if there's something to update
      if (Object.keys(updateData).length > 0) {
        const { error: updateError } = await supabase
          .from('users')
          .update({
            ...updateData,
            updated_at: new Date().toISOString()
          })
          .eq('id', userProfile.id);

        if (updateError) {
          console.error("Error updating user profile with emergency contact:", updateError);
          // Continue with registration even if profile update fails
        }
      }
    }

    // Update category registration count
    try {
      // First get the category
      const { data: category } = await supabase
        .from('event_categories')
        .select('*')
        .eq('id', categoryId)
        .single();

      if (category) {
        // Create or update properties object
        // Initialize an empty object if properties doesn't exist
        const propertiesObj: CategoryProperties = {};

        // Try to get existing properties if available
        try {
          // Check if the category has a registration_count field directly
          if ('registration_count' in category) {
            // Legacy schema - use the direct field
            propertiesObj.registrationCount = (category as CategoryWithProperties).registration_count || 0;
          } else {
            // Try to use the properties field if it exists
            const categoryProperties = (category as CategoryWithProperties).properties;

            if (categoryProperties) {
              // If properties is a string, parse it
              if (typeof categoryProperties === 'string') {
                Object.assign(propertiesObj, JSON.parse(categoryProperties));
              } else if (typeof categoryProperties === 'object') {
                Object.assign(propertiesObj, categoryProperties);
              }
            }
          }
        } catch (parseError) {
          console.error("Error parsing category properties:", parseError);
          // Continue with empty properties object
        }

        // Update registration count
        const currentCount = propertiesObj.registrationCount || 0;
        propertiesObj.registrationCount = currentCount + 1;

        // Update the category
        if ('registration_count' in category) {
          // Legacy schema - update the direct field
          await supabase
            .from('event_categories')
            .update({
              registration_count: propertiesObj.registrationCount,
              updated_at: new Date().toISOString()
            })
            .eq('id', categoryId);
        } else {
          // New schema - update the properties field
          await supabase
            .from('event_categories')
            .update({
              properties: propertiesObj,
              updated_at: new Date().toISOString()
            })
            .eq('id', categoryId);
        }
      }
    } catch (error) {
      console.error("Error updating category registration count:", error);
      // Continue with registration even if count update fails
    }

    // Redirect to success page
    return NextResponse.redirect(new URL(`/events/registration-success?registrationId=${registration.id}`, request.url));

  } catch (error) {
    console.error("Error in registration process:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
