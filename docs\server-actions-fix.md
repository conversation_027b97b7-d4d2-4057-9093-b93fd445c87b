# Server Actions Fix

This document explains the changes made to fix the Server Actions errors in the application.

## Problem

The application was encountering errors with Server Actions in the redirect utilities:

```
Server Actions must be async functions.
```

This error occurred because the redirect functions in `src/lib/redirect-utils.ts` were not declared as `async` functions, which is required for Server Actions in Next.js. Additionally, non-async utility functions like `isEventRegistrationUrl` and `getEventIdFromUrl` were causing errors because they were in a file with the `'use server'` directive.

## Solution

We made the following changes to fix the issues:

1. Created separate files for different types of functions:
   - Created `src/lib/url-utils.ts` for regular utility functions
   - Created `src/lib/redirect-actions.ts` for server actions with the `'use server'` directive
   - Updated `src/lib/redirect-utils.ts` to be a re-export file without the `'use server'` directive

2. Moved functions to the appropriate files:
   - Moved `isEventRegistrationUrl` and `getEventIdFromUrl` to `url-utils.ts`
   - Moved `safeRedirect`, `redirectToSignIn`, and `redirectToDashboard` to `redirect-actions.ts`
   - Made `redirect-utils.ts` re-export all functions for backward compatibility

3. Updated all redirect functions to be `async`:
   - `safeRedirect`
   - `redirectToSignIn`
   - `redirectToDashboard`

4. Updated all calls to redirect functions to use `await`:
   - In `src/lib/auth-utils.ts`
   - In various dashboard pages

5. Updated imports in all files to use the new module structure:
   - Updated `auth-utils.ts` to import from `redirect-actions.ts`
   - Updated `GoogleSignInButton.tsx` to import from `url-utils.ts`
   - Updated all pages to import from `redirect-actions.ts`

## Technical Details

### Server Actions Requirements

In Next.js, Server Actions must be declared as `async` functions. This is because Server Actions are designed to handle asynchronous operations like database queries, API calls, and redirects.

When a function uses the `'use server'` directive (either at the file level or within the function), it must be declared as `async`. This applies even if the function doesn't explicitly use `await` internally.

### Redirect Function Changes

The redirect functions were updated to be `async`:

```typescript
// Before
export function safeRedirect(url: string, options: RedirectOptions = {}) {
  // ...
  redirect(urlObj.toString());
}

// After
export async function safeRedirect(url: string, options: RedirectOptions = {}) {
  // ...
  redirect(urlObj.toString());
}
```

### Calling Redirect Functions

All calls to these functions were updated to use `await`:

```typescript
// Before
if (!isUserAuthenticated) {
  redirectToSignIn(redirectTo);
}

// After
if (!isUserAuthenticated) {
  await redirectToSignIn(redirectTo);
}
```

## Benefits of the Fix

1. **Compliance with Next.js Requirements**: The code now follows the Next.js requirement that Server Actions must be async functions.

2. **Proper Error Handling**: Async functions allow for better error handling with try/catch blocks.

3. **Consistent Pattern**: All redirect functions now follow a consistent pattern, making the code more maintainable.

4. **Future-Proofing**: The code is now ready for any future changes to the Next.js Server Actions API.

## Related Documentation

- [Next.js Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations)
- [Next.js Redirects](https://nextjs.org/docs/app/api-reference/functions/redirect)
