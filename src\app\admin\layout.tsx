import { AdminHeader } from './components/AdminHeader';
import { requireAdmin } from '@/lib/auth-utils';

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Use our centralized auth utility to require admin role
  // This handles both authentication and role checking in one call
  await requireAdmin();

  return (
    <div className="min-h-screen bg-background">
      <AdminHeader />
      <main className="py-6">
        {children}
      </main>
    </div>
  );
}