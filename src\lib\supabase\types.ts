export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      collections: {
        Row: {
          collected_at: string | null
          collected_by: string | null
          created_at: string
          id: string
          notes: string | null
          registration_id: string
          status: string
          type: string
          updated_at: string | null
        }
        Insert: {
          collected_at?: string | null
          collected_by?: string | null
          created_at?: string
          id: string
          notes?: string | null
          registration_id: string
          status?: string
          type: string
          updated_at?: string | null
        }
        Update: {
          collected_at?: string | null
          collected_by?: string | null
          created_at?: string
          id?: string
          notes?: string | null
          registration_id?: string
          status?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "collections_collected_by_users_id_fk"
            columns: ["collected_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collections_registration_id_registrations_id_fk"
            columns: ["registration_id"]
            isOneToOne: false
            referencedRelation: "registrations"
            referencedColumns: ["id"]
          }
        ]
      }
      events: {
        Row: {
          capacity: number | null
          category: string | null
          created_at: string | null
          description: string | null
          end_date: string
          id: string
          location: string | null
          organizer_id: string
          price: number | null
          start_date: string
          status: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          capacity?: number | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          end_date: string
          id?: string
          location?: string | null
          organizer_id: string
          price?: number | null
          start_date: string
          status?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          capacity?: number | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          end_date?: string
          id?: string
          location?: string | null
          organizer_id?: string
          price?: number | null
          start_date?: string
          status?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      organizations: {
        Row: {
          created_at: string
          description: string | null
          document_number: string
          document_type: string
          document_url: string
          id: string
          name: string
          updated_at: string | null
          user_id: string
          verification_status: string
          verified_at: string | null
          verified_by: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          document_number: string
          document_type: string
          document_url: string
          id: string
          name: string
          updated_at?: string | null
          user_id: string
          verification_status?: string
          verified_at?: string | null
          verified_by?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          document_number?: string
          document_type?: string
          document_url?: string
          id?: string
          name?: string
          updated_at?: string | null
          user_id?: string
          verification_status?: string
          verified_at?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organizations_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organizations_verified_by_users_id_fk"
            columns: ["verified_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      organization_applications: {
        Row: {
          id: string
          user_id: string
          status: string
          data: Record<string, any>
          created_at: string
          updated_at: string
          submitted_at: string | null
          reviewed_at: string | null
          reviewed_by: string | null
          rejection_reason: string | null
        }
        Insert: {
          id?: string
          user_id: string
          status: string
          data: Record<string, any>
          created_at?: string
          updated_at?: string
          submitted_at?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          rejection_reason?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          status?: string
          data?: Record<string, any>
          created_at?: string
          updated_at?: string
          submitted_at?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          rejection_reason?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organization_applications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_applications_reviewed_by_fkey"
            columns: ["reviewed_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      payments: {
        Row: {
          amount: number
          created_at: string
          id: string
          payment_method: string
          payment_reference: string | null
          platform_fee: number
          registration_id: string
          sst: number
          status: string
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          amount: number
          created_at?: string
          id: string
          payment_method: string
          payment_reference?: string | null
          platform_fee: number
          registration_id: string
          sst: number
          status?: string
          total_amount: number
          updated_at?: string | null
        }
        Update: {
          amount?: number
          created_at?: string
          id?: string
          payment_method?: string
          payment_reference?: string | null
          platform_fee?: number
          registration_id?: string
          sst?: number
          status?: string
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payments_registration_id_registrations_id_fk"
            columns: ["registration_id"]
            isOneToOne: false
            referencedRelation: "registrations"
            referencedColumns: ["id"]
          }
        ]
      }
      promotion_applications: {
        Row: {
          created_at: string
          document_url: string
          id: string
          notes: string | null
          reviewed_at: string | null
          reviewed_by: string | null
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          document_url: string
          id?: string
          notes?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          document_url?: string
          id?: string
          notes?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "promotion_applications_reviewed_by_fkey"
            columns: ["reviewed_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      registrations: {
        Row: {
          attendance_status: string
          collection_status: string
          created_at: string
          event_id: string
          form_data: string
          id: string
          qr_code: string
          ticket_number: string
          ticket_type_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          attendance_status?: string
          collection_status?: string
          created_at?: string
          event_id: string
          form_data: string
          id: string
          qr_code: string
          ticket_number: string
          ticket_type_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          attendance_status?: string
          collection_status?: string
          created_at?: string
          event_id?: string
          form_data?: string
          id?: string
          qr_code?: string
          ticket_number?: string
          ticket_type_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "registrations_ticket_type_id_ticket_types_id_fk"
            columns: ["ticket_type_id"]
            isOneToOne: false
            referencedRelation: "ticket_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "registrations_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      reviews: {
        Row: {
          comment: string | null
          created_at: string | null
          event_id: string
          id: string
          rating: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          comment?: string | null
          created_at?: string | null
          event_id: string
          id?: string
          rating?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          comment?: string | null
          created_at?: string | null
          event_id?: string
          id?: string
          rating?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reviews_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          }
        ]
      }
      ticket_types: {
        Row: {
          available_quantity: number
          created_at: string
          description: string | null
          event_id: string
          id: string
          metadata: string | null
          name: string
          price: number
          quantity: number
          updated_at: string | null
        }
        Insert: {
          available_quantity: number
          created_at?: string
          description?: string | null
          event_id: string
          id: string
          metadata?: string | null
          name: string
          price: number
          quantity: number
          updated_at?: string | null
        }
        Update: {
          available_quantity?: number
          created_at?: string
          description?: string | null
          event_id?: string
          id?: string
          metadata?: string | null
          name?: string
          price?: number
          quantity?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      tickets: {
        Row: {
          created_at: string | null
          event_id: string
          id: string
          price: number
          status: string | null
          ticket_type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          event_id: string
          id?: string
          price: number
          status?: string | null
          ticket_type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          event_id?: string
          id?: string
          price?: number
          status?: string | null
          ticket_type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tickets_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          created_at: string
          email: string
          first_name: string
          id: string
          last_name: string | null
          role: string
          updated_at: string | null
          username: string | null
          gender: string | null
          bio: string | null
          avatar: string | null
          nationality: string | null
          ic: string | null
          passport: string | null
          dateOfBirth: string | null
          contactNo: string | null
          address: string | null
          apartment: string | null
          city: string | null
          postcode: string | null
          country: string | null
          state: string | null
          interests: string | null
          isPublic: number
          emergencyContactName: string | null
          emergencyContactNo: string | null
          emergencyContactRelationship: string | null
          eventCategories: string | null
          tshirt_size: string | null
          auth_user_id: string | null
        }
        Insert: {
          created_at?: string
          email: string
          first_name: string
          id: string
          last_name?: string | null
          role?: string
          updated_at?: string | null
          username?: string | null
          gender?: string | null
          bio?: string | null
          avatar?: string | null
          nationality?: string | null
          ic?: string | null
          passport?: string | null
          dateOfBirth?: string | null
          contactNo?: string | null
          address?: string | null
          apartment?: string | null
          city?: string | null
          postcode?: string | null
          country?: string | null
          state?: string | null
          interests?: string | null
          isPublic?: number
          emergencyContactName?: string | null
          emergencyContactNo?: string | null
          emergencyContactRelationship?: string | null
          eventCategories?: string | null
          tshirt_size?: string | null
          auth_user_id?: string | null
        }
        Update: {
          created_at?: string
          email?: string
          first_name?: string
          id?: string
          last_name?: string | null
          role?: string
          updated_at?: string | null
          username?: string | null
          gender?: string | null
          bio?: string | null
          avatar?: string | null
          nationality?: string | null
          ic?: string | null
          passport?: string | null
          dateOfBirth?: string | null
          contactNo?: string | null
          address?: string | null
          apartment?: string | null
          city?: string | null
          postcode?: string | null
          country?: string | null
          state?: string | null
          interests?: string | null
          isPublic?: number
          emergencyContactName?: string | null
          emergencyContactNo?: string | null
          emergencyContactRelationship?: string | null
          eventCategories?: string | null
          tshirt_size?: string | null
          auth_user_id?: string | null
        }
        Relationships: []
      }
      event_images: {
        Row: {
          id: string
          event_id: string
          type: string
          url: string
          path: string
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          event_id: string
          type: string
          url: string
          path: string
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          event_id?: string
          type?: string
          url?: string
          path?: string
          created_at?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_images_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          }
        ]
      }
      saved_contacts: {
        Row: {
          id: string
          user_id: string
          first_name: string
          last_name: string | null
          relationship: string
          email: string | null
          phone: string | null
          date_of_birth: string | null
          gender: string | null
          tshirt_size: string | null
          address: string | null
          city: string | null
          state: string | null
          country: string | null
          postcode: string | null
          emergency_contact_name: string | null
          emergency_contact_no: string | null
          emergency_contact_relationship: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          first_name: string
          last_name?: string | null
          relationship: string
          email?: string | null
          phone?: string | null
          date_of_birth?: string | null
          gender?: string | null
          tshirt_size?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          country?: string | null
          postcode?: string | null
          emergency_contact_name?: string | null
          emergency_contact_no?: string | null
          emergency_contact_relationship?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          first_name?: string
          last_name?: string | null
          relationship?: string
          email?: string | null
          phone?: string | null
          date_of_birth?: string | null
          gender?: string | null
          tshirt_size?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          country?: string | null
          postcode?: string | null
          emergency_contact_name?: string | null
          emergency_contact_no?: string | null
          emergency_contact_relationship?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_user"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      privacy_consents: {
        Row: {
          id: string
          user_id: string
          consent_type: string
          consent_given: boolean
          consent_version: string
          consent_text: string
          expires_at: string | null
          ip_address: string | null
          user_agent: string | null
          previous_record_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          consent_type: string
          consent_given: boolean
          consent_version: string
          consent_text: string
          expires_at?: string | null
          ip_address?: string | null
          user_agent?: string | null
          previous_record_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          consent_type?: string
          consent_given?: boolean
          consent_version?: string
          consent_text?: string
          expires_at?: string | null
          ip_address?: string | null
          user_agent?: string | null
          previous_record_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_user"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_previous_record"
            columns: ["previous_record_id"]
            isOneToOne: false
            referencedRelation: "privacy_consents"
            referencedColumns: ["id"]
          }
        ]
      }
      privacy_consent_versions: {
        Row: {
          id: string
          version: string
          name: string
          description: string | null
          consent_text: string
          effective_date: string
          expiry_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          version: string
          name: string
          description?: string | null
          consent_text: string
          effective_date: string
          expiry_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          version?: string
          name?: string
          description?: string | null
          consent_text?: string
          effective_date?: string
          expiry_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      profile_activity: {
        Row: {
          id: string
          user_id: string
          activity_type: string
          activity_description: string
          previous_value: Record<string, any> | null
          new_value: Record<string, any> | null
          field_name: string | null
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          activity_type: string
          activity_description: string
          previous_value?: Record<string, any> | null
          new_value?: Record<string, any> | null
          field_name?: string | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          activity_type?: string
          activity_description?: string
          previous_value?: Record<string, any> | null
          new_value?: Record<string, any> | null
          field_name?: string | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_user"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      emergency_contact_access_logs: {
        Row: {
          id: string
          user_id: string
          event_id: string
          registration_id: string
          accessed_by: string
          accessed_at: string
          reason: string
          ip_address: string | null
          user_agent: string | null
        }
        Insert: {
          id?: string
          user_id: string
          event_id: string
          registration_id: string
          accessed_by: string
          accessed_at?: string
          reason: string
          ip_address?: string | null
          user_agent?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          event_id?: string
          registration_id?: string
          accessed_by?: string
          accessed_at?: string
          reason?: string
          ip_address?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_user"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_accessor"
            columns: ["accessed_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      event_categories: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      data_exports: {
        Row: {
          id: string
          user_id: string
          export_type: string
          export_format: string
          status: string
          requested_at: string
          processed_at: string | null
          completed_at: string | null
          download_url: string | null
          download_count: number
          expires_at: string | null
          error_message: string | null
          is_deleted: boolean
          created_at: string
          updated_at: string | null
          email: string | null
        }
        Insert: {
          id?: string
          user_id: string
          export_type: string
          export_format: string
          status?: string
          requested_at?: string
          processed_at?: string | null
          completed_at?: string | null
          download_url?: string | null
          download_count?: number
          expires_at?: string | null
          error_message?: string | null
          is_deleted?: boolean
          created_at?: string
          updated_at?: string | null
          email?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          export_type?: string
          export_format?: string
          status?: string
          requested_at?: string
          processed_at?: string | null
          completed_at?: string | null
          download_url?: string | null
          download_count?: number
          expires_at?: string | null
          error_message?: string | null
          is_deleted?: boolean
          created_at?: string
          updated_at?: string | null
          email?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "data_exports_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      create_privacy_consents_table: {
        Args: Record<string, never>
        Returns: void
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']