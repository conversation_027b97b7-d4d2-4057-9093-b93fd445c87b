-- Add event_categories table for running events
-- Migration: 0014_add_running_event_categories.sql

-- Start transaction for atomic migration
BEGIN;

-- Create event_categories table
CREATE TABLE IF NOT EXISTS event_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  capacity INTEGER,
  price DECIMAL(10, 2),
  start_time TIMESTAMP WITH TIME ZONE,
  early_bird_price DECIMAL(10, 2),
  early_bird_end_date TIMESTAMP WITH TIME ZONE,
  bib_prefix TEXT, -- For custom bib number prefixes per category
  bib_start_number INTEGER, -- Starting number for bib sequence
  bib_require_generation BOOLEAN DEFAULT true, -- Whether bib numbers should be auto-generated
  registration_open BOOLEAN DEFAULT true,
  registration_close_date TIMESTAMP WITH TIME ZONE,
  registration_limit INTEGER,
  registration_count INTEGER DEFAULT 0,
  custom_fields JSONB DEFAULT '[]'::jsonb, -- Override or add to event's custom fields for this category
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create index for faster lookup by event
CREATE INDEX IF NOT EXISTS event_categories_event_id_idx ON event_categories(event_id);

-- Add comment for documentation
COMMENT ON TABLE event_categories IS 'Categories for events, particularly useful for running events (marathon, half-marathon, etc.)';
COMMENT ON COLUMN event_categories.bib_prefix IS 'Optional prefix for bib numbers in this category (e.g., "FM-" for Full Marathon)';
COMMENT ON COLUMN event_categories.bib_start_number IS 'Starting number in the bib number sequence for this category';
COMMENT ON COLUMN event_categories.custom_fields IS 'Category-specific registration fields that override or add to the event level fields';

-- Force schema cache reload
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('0014_add_running_event_categories', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit transaction
COMMIT; 