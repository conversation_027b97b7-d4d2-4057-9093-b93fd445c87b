import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default function CategoryNotFound() {
  return (
    <div className="container mx-auto px-4 py-16 flex flex-col items-center justify-center min-h-[60vh]">
      <h1 className="text-3xl font-bold mb-4 text-center">Category Not Found</h1>
      <p className="text-muted-foreground text-center max-w-md mb-8">
        The category you're trying to register for doesn't exist or is no longer available.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button asChild>
          <Link href="/events">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Browse Events
          </Link>
        </Button>
      </div>
    </div>
  );
}
