import { DashboardCard } from "@/components/dashboard/dashboard-card";
import { createClient } from '@/lib/supabase/server';
import { getAuthUser } from '@/lib/auth-utils';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/types';
import { logger } from '@/lib/logger';

/**
 * Helper function to fetch user stats by internal user ID
 * @param supabase Supabase client
 * @param userId Internal user ID
 * @returns User statistics
 */
async function fetchUserStatsById(
  supabase: SupabaseClient<Database>,
  userId: string
) {
  try {
    // Import repositories directly instead of using the API
    const { EventRepository } = await import('@/repositories/event-repository');
    const { UserRepository } = await import('@/repositories/user-repository');

    // Initialize repositories
    const eventRepository = new EventRepository();
    const userRepository = new UserRepository();

    // Get event counts
    const eventCounts = await eventRepository.getEventCountsByStatus(userId);

    // Get organization count
    const organizationCount = await userRepository.getUserOrganizationCount(userId);

    // Get ticket count (registrations)
    const { data: ticketData, error: ticketError } = await supabase
      .from('registrations')
      .select('count')
      .eq('user_id', userId)
      .single();

    const ticketCount = ticketError ? 0 : (ticketData?.count || 0);

    // Get payment count
    const { data: paymentData, error: paymentError } = await supabase
      .from('payments')
      .select('count')
      .eq('user_id', userId)
      .single();

    const paymentCount = paymentError ? 0 : (paymentData?.count || 0);

    // Return all stats
    return {
      events: {
        total: eventCounts.total,
        draft: eventCounts.draft,
        published: eventCounts.published,
        cancelled: eventCounts.cancelled,
        completed: eventCounts.completed
      },
      organizations: organizationCount,
      tickets: ticketCount,
      payments: paymentCount
    };
  } catch (error) {
    logger.error("Error in fetchUserStatsById:", error);
    return {
      events: { total: 0, draft: 0, published: 0, cancelled: 0, completed: 0 },
      organizations: 0,
      tickets: 0,
      payments: 0
    };
  }
}

/**
 * Fetch user statistics directly from the database
 * @returns Object containing counts for events, organizations, tickets, and payments
 */
async function getUserStats(authUserId: string) {
  try {
    // Use the server-side client for database operations
    const supabase = await createClient();

    // Log the auth user ID for debugging
    logger.debug('Getting stats for auth user ID:', authUserId);

    // Double-check that we still have a valid user before proceeding
    // Always use getUser() for security as recommended by Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      logger.error('User validation failed during getUserStats:', userError);
      // Return empty stats instead of redirecting
      return {
        events: { total: 0, draft: 0, published: 0, cancelled: 0, completed: 0 },
        organizations: 0,
        tickets: 0,
        payments: 0
      };
    }

    logger.debug('User still valid in getUserStats, proceeding with database queries');

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUserId)
      .single();

    if (userIdError || !userData) {
      logger.error("Error fetching user ID:", userIdError);

      // Try to find the user by email as a fallback
      const { data: { user } } = await supabase.auth.getUser();
      if (user?.email) {
        logger.debug('Trying to find user by email:', user.email);
        const { data: userByEmail, error: emailError } = await supabase
          .from('users')
          .select('id')
          .eq('email', user.email)
          .single();

        if (!emailError && userByEmail) {
          logger.debug('Found user by email, ID:', userByEmail.id);
          return await fetchUserStatsById(supabase, userByEmail.id);
        }
      }

      // User not found in database, but they are authenticated
      // Let's create a user record for them instead of redirecting
      logger.debug('User authenticated but not found in database, creating user record');

      try {
        // Get user details from auth
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          // Generate a UUID for the new user
          const userId = crypto.randomUUID();

          // Create a new user record
          const { data: newUser, error: createError } = await supabase
            .from('users')
            .insert({
              id: userId,
              email: user.email || '',
              first_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
              role: 'user', // Default role
              created_at: new Date().toISOString()
            })
            .select('id')
            .single();

          if (createError) {
            logger.error('Error creating user record:', createError);
          } else if (newUser) {
            logger.debug('Created new user record with ID:', newUser.id);
            return await fetchUserStatsById(supabase, newUser.id);
          }
        }
      } catch (createError) {
        logger.error('Exception creating user record:', createError);
      }

      // Return empty stats if we couldn't create a user
      return {
        events: { total: 0, draft: 0, published: 0, cancelled: 0, completed: 0 },
        organizations: 0,
        tickets: 0,
        payments: 0
      };
    }

    // Use the helper function to fetch stats by user ID
    return await fetchUserStatsById(supabase, userData.id);
  } catch (error) {
    logger.error("Error fetching user stats:", error);
    return {
      events: { total: 0, draft: 0, published: 0, cancelled: 0, completed: 0 },
      organizations: 0,
      tickets: 0,
      payments: 0
    };
  }
}

export default async function DashboardPage() {
  // Use our centralized auth utilities to get the authenticated user
  const authUser = await getAuthUser();

  if (!authUser) {
    // This should never happen since middleware protects this route
    logger.error('User not found in dashboard page despite middleware protection');
    return (
      <div className="px-6 pt-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold">Session Error</h1>
          <p className="text-muted-foreground">
            Please try <a href="/sign-in?reset_auth=true" className="text-primary underline">signing in again</a>.
          </p>
        </div>
      </div>
    );
  }

  // Get user stats
  const stats = await getUserStats(authUser.id);

  return (
    <div className="px-6 pt-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">Overview</h1>
        <p className="text-muted-foreground">Welcome to your dashboard</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <DashboardCard
          title="Events"
          description="Manage your events"
          href="/dashboard/events"
          count={stats.events.total ?? 0}
        />
        <DashboardCard
          title="Organizations"
          description="View your organizations"
          href="/dashboard/organizations"
          count={stats.organizations ?? 0}
        />
        <DashboardCard
          title="Tickets"
          description="Check your tickets"
          href="/dashboard/tickets"
          count={stats.tickets ?? 0}
        />
        <DashboardCard
          title="Payments"
          description="Review your payments"
          href="/dashboard/payments"
          count={stats.payments ?? 0}
        />
      </div>
    </div>
  );
}