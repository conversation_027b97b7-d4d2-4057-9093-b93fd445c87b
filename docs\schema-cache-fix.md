# Fixing Supabase Schema Cache Issues

This document explains how to fix schema cache issues with the Supabase database, specifically focusing on the `saved_contacts` table's emergency contact fields.

## Understanding the Problem

The issue occurs when Supabase's schema cache becomes out of sync with the actual database schema. This results in errors like:

```
Could not find the 'emergency_contact_name' column of 'saved_contacts' in the schema cache
```

or 

```
Could not find the 'first_name' column of 'saved_contacts' in the schema cache
```

This happens because the PostgREST service (which powers Supabase's REST API) builds a schema cache at startup by introspecting the database. When this cache is incomplete or corrupted, API operations fail.

## Fix Options

### Option 1: Use the Fix Script (Recommended)

We've created a script that will apply the necessary SQL to fix the schema cache:

1. Ensure your Supabase service key is available
2. Run the script:

```bash
# Set your environment variables first
export SUPABASE_SERVICE_KEY=your_service_role_key_here

# Run the fix script
node scripts/fix-schema-cache.js
```

### Option 2: Manual Fix via SQL Editor

1. Log in to your Supabase dashboard
2. Open the SQL Editor
3. Execute the following SQL:

```sql
-- Fix emergency contact fields in saved_contacts table
ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_name TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_no TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_relationship TYPE TEXT;

-- Update basic columns as well to ensure full refresh
ALTER TABLE saved_contacts ALTER COLUMN first_name TYPE TEXT;
ALTER TABLE saved_contacts ALTER COLUMN last_name TYPE TEXT;

-- Force schema cache reload (may require admin privileges)
SELECT pg_notify('pgrst', 'reload schema');
```

4. If the issue persists, restart the PostgREST service from your Supabase dashboard

### Option 3: Apply the Migration

Use the existing migration file:

```bash
# Find the migration file
find ./src/db/migrations -name "0010_fix_emergency_contact_fields.sql"

# Apply it using your database migration tool
```

## Testing the Fix

To verify that the schema cache issue is resolved, run a test query:

```sql
SELECT emergency_contact_name FROM saved_contacts LIMIT 1;
```

If this query executes without errors, the fix has been applied successfully.

## Preventing Future Issues

To prevent schema cache issues in the future:

1. Always use migration files for schema changes
2. Consider adding a schema cache refresh step in your deployment pipeline
3. When adding new columns, explicitly set their types
4. Apply migrations during off-peak hours

## Code Changes After Fix

Once the schema cache is fixed, re-enable the emergency contact fields in the code by uncommenting the fields in:
- `src/app/actions/contacts.ts` (in the REST API call section)

For more information on schema cache issues in Supabase, see the [official documentation](https://supabase.com/docs/guides/database/extensions/pgrouting#postgrest-schema-cache). 