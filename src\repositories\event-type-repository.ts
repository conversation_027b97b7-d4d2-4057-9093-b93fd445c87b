import { BaseRepository } from '@/lib/db/base-repository';
import { z } from 'zod';
import { EventType, eventTypeSchema } from '@/types/event-types';
import { createClient } from '@/lib/supabase/pages-client';
import { asAny } from '@/lib/supabase/extended-types';

/**
 * Event Type database schema for the repository
 */
const EventTypeDBSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  slug: z.string(),
  description: z.string().nullable(),
  base_fields: z.any(), // This will be a JSON field
  custom_fields: z.any(), // This will be a JSON field
  icon: z.string().nullable(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

/**
 * Type for the EventType database model
 */
type EventTypeDB = z.infer<typeof EventTypeDBSchema>;

/**
 * Repository for event type operations
 */
export class EventTypeRepository extends BaseRepository<EventTypeDB> {
  constructor() {
    super('event_types', EventTypeDBSchema);
  }

  /**
   * Transform the database event type to the application event type
   */
  private toEventType(dbEventType: EventTypeDB): EventType {
    return {
      id: dbEventType.id,
      name: dbEventType.name,
      slug: dbEventType.slug,
      description: dbEventType.description || "",
      baseFields: dbEventType.base_fields || {},
      customFields: dbEventType.custom_fields || [],
      icon: dbEventType.icon || "",
      createdAt: new Date(dbEventType.created_at),
      updatedAt: new Date(dbEventType.updated_at),
    };
  }

  /**
   * Transform the application event type to the database event type
   */
  private toEventTypeDB(eventType: Partial<EventType>): Partial<EventTypeDB> {
    const result: Partial<EventTypeDB> = {};

    if (eventType.name !== undefined) result.name = eventType.name;
    if (eventType.slug !== undefined) result.slug = eventType.slug;
    if (eventType.description !== undefined) result.description = eventType.description || null;
    if (eventType.baseFields !== undefined) result.base_fields = eventType.baseFields;
    if (eventType.customFields !== undefined) result.custom_fields = eventType.customFields;
    if (eventType.icon !== undefined) result.icon = eventType.icon || null;

    return result;
  }

  /**
   * Get all event types
   */
  async getAllEventTypes(): Promise<EventType[]> {
    const result = await this.find();
    if (!result.success) {
      throw new Error(`Failed to get event types: ${result.message}`);
    }
    return (result.data || []).map(this.toEventType);
  }

  /**
   * Get event type by ID
   */
  async getEventTypeById(id: string): Promise<EventType | null> {
    const result = await this.findById(id);
    if (!result.success) {
      if (result.message?.includes('not found')) {
        return null;
      }
      throw new Error(`Failed to get event type: ${result.message}`);
    }
    return this.toEventType(result.data!);
  }

  /**
   * Get event type by slug
   */
  async getEventTypeBySlug(slug: string): Promise<EventType | null> {
    // Use find method from the base repository instead of direct supabase call
    const result = await this.find({ slug });

    if (!result.success) {
      throw new Error(`Failed to get event type by slug: ${result.message}`);
    }

    if (!result.data || result.data.length === 0) {
      return null;
    }

    if (result.data && result.data.length > 0) {
      if (result.data && result.data[0]) {
        return this.toEventType(result.data[0]);
      }
      return null;
    }
    return null;
  }

  /**
   * Create a new event type
   */
  async createEventType(eventType: Omit<EventType, 'id' | 'createdAt' | 'updatedAt'>): Promise<EventType> {
    // Validate the event type
    eventTypeSchema.parse(eventType);

    const result = await this.create({
      id: crypto.randomUUID(),
      name: eventType.name,
      slug: eventType.slug,
      description: eventType.description || null,
      base_fields: eventType.baseFields || {},
      custom_fields: eventType.customFields || [],
      icon: eventType.icon || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    if (!result.success) {
      throw new Error(`Failed to create event type: ${result.message}`);
    }

    return this.toEventType(result.data!);
  }

  /**
   * Update an event type
   */
  async updateEventType(id: string, eventType: Partial<Omit<EventType, 'id' | 'createdAt' | 'updatedAt'>>): Promise<EventType> {
    // Prepare the data to update
    const dbEventType = this.toEventTypeDB(eventType);

    // Always update the updated_at timestamp
    dbEventType.updated_at = new Date().toISOString();

    const result = await this.update(id, dbEventType);

    if (!result.success) {
      throw new Error(`Failed to update event type: ${result.message}`);
    }

    return this.toEventType(result.data!);
  }

  /**
   * Delete an event type
   */
  async deleteEventType(id: string): Promise<void> {
    const result = await this.delete(id);

    if (!result.success) {
      throw new Error(`Failed to delete event type: ${result.message}`);
    }
  }
}