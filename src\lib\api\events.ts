import { Event } from "@/repositories/event-repository";

/**
 * Fetches all events from the API
 */
export async function getAllEvents(): Promise<Event[]> {
  const response = await fetch('/api/events');
  if (!response.ok) {
    throw new Error('Failed to fetch events');
  }
  return response.json();
}

/**
 * Fetches a single event by ID
 */
export async function getEventById(id: string): Promise<Event> {
  const response = await fetch(`/api/events?id=${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch event');
  }
  return response.json();
}

/**
 * Fetches upcoming events for the logged-in user
 */
export async function getUpcomingEvents(): Promise<(Event & { registration?: any })[]> {
  const response = await fetch('/api/events?type=upcoming');
  if (!response.ok) {
    throw new Error('Failed to fetch upcoming events');
  }
  return response.json();
}

/**
 * Fetches past events for the logged-in user
 */
export async function getPastEvents(): Promise<(Event & { registration?: any })[]> {
  const response = await fetch('/api/events?type=past');
  if (!response.ok) {
    throw new Error('Failed to fetch past events');
  }
  return response.json();
}

/**
 * Fetches recommended events for the logged-in user
 */
export async function getRecommendedEvents(): Promise<Event[]> {
  const response = await fetch('/api/events?type=recommended');
  if (!response.ok) {
    throw new Error('Failed to fetch recommended events');
  }
  return response.json();
}

/**
 * Creates a new event
 */
export async function createEvent(eventData: any): Promise<{ eventId: string }> {
  const response = await fetch('/api/events', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(eventData),
  });
  
  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to create event: ${error}`);
  }
  
  const result = await response.json();
  return { eventId: result.data.eventId };
}

/**
 * Updates an existing event
 */
export async function updateEvent(id: string, eventData: any): Promise<Event> {
  const response = await fetch('/api/events', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      id,
      ...eventData,
    }),
  });
  
  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to update event: ${error}`);
  }
  
  const result = await response.json();
  return result.data.event;
}

/**
 * Deletes an event
 */
export async function deleteEvent(id: string): Promise<{ success: boolean }> {
  const response = await fetch(`/api/events?id=${id}`, {
    method: 'DELETE',
  });
  
  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to delete event: ${error}`);
  }
  
  return { success: true };
} 