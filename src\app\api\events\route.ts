import { NextResponse } from "next/server";
import { createClient } from '@/lib/supabase/pages-client';
import { EventRepository } from "@/repositories/event-repository";
import { toDate } from '@/lib/utils/date-utils';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/lib/logger';

export async function GET(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const eventId = searchParams.get('id');

    // Get a single event by ID
    if (eventId) {
      return await getEventById(eventId);
    }

    // Handle different types of event queries
    switch (type) {
      case 'upcoming':
        return await getUpcomingEvents(userId);
      case 'past':
        return await getPastEvents(userId);
      case 'recommended':
        return await getRecommendedEvents(userId);
      default:
        return await getAllEvents();
    }
  } catch (error) {
    logger.error("Error fetching events:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

async function getEventById(eventId: string) {
  try {
    const eventRepository = new EventRepository();
    const event = await eventRepository.getEventById(eventId);

    if (!event) {
      return new NextResponse("Event not found", { status: 404 });
    }

    // Get the event images (still using Supabase for now)
    const supabase = await createClient();
    const { data: images, error: imagesError } = await supabase
      .from('event_images')
      .select('*')
      .eq('event_id', eventId)
      .order('created_at', { ascending: false });

    if (imagesError) {
      logger.error("Error fetching event images:", imagesError);
      // Continue without images
    }

    // Process images if available
    const poster = images?.find(img => img.type === 'poster');
    const gallery = images?.filter(img => img.type === 'gallery') || [];

    return NextResponse.json({
      ...event,
      poster: poster ? poster.url : null,
      gallery: gallery.map(img => img.url)
    });
  } catch (error) {
    logger.error("Error fetching event:", error);
    return new NextResponse("Error fetching event", { status: 500 });
  }
}

async function getUpcomingEvents(userId: string) {
  try {
    const supabase = await createClient();
    const eventRepository = new EventRepository();
    const currentDate = new Date().toISOString();

    // First get the user's registrations
    const { data: registrations, error } = await supabase
      .from('registrations')
      .select('event_id')
      .eq('user_id', userId);

    if (error) {
      logger.error("Error fetching registrations:", error);
      return new NextResponse("Error fetching events", { status: 500 });
    }

    if (!registrations || registrations.length === 0) {
      return NextResponse.json([]);
    }

    // Get all the event IDs from registrations
    const eventIds = registrations.map(reg => (reg as { event_id: string }).event_id);

    // Get all events
    const allEvents = await eventRepository.getAllEvents();

    // Filter for the user's registered upcoming events
    const userEvents = allEvents.filter(event =>
      eventIds.includes(event.id) &&
      event.endDate && new Date(event.endDate) >= new Date(currentDate)
    );

    // Sort by start date (ascending)
    userEvents.sort((a, b) => {
      // Handle null or undefined dates safely
      const aDate = a.startDate ? new Date(a.startDate).getTime() : 0;
      const bDate = b.startDate ? new Date(b.startDate).getTime() : 0;
      return aDate - bDate;
    });

    // Get registration details to add to events
    const { data: registrationDetails, error: detailsError } = await supabase
      .from('registrations')
      .select('event_id, ticket_number, status')
      .in('event_id', userEvents.map(e => e.id))
      .eq('user_id', userId);

    if (detailsError) {
      logger.error("Error fetching registration details:", detailsError);
      // Continue without registration details
    }

    // Create a map of registration details by event ID
    const registrationDetailsByEventId = (registrationDetails || []).reduce<Record<string, { ticket_number: string | null, status: string | null }>>((acc, reg) => {
      const regData = reg as { event_id: string; ticket_number?: string; status?: string };
      const eventId = regData.event_id;
      const ticketNumber = regData.ticket_number;

      acc[eventId] = {
        ticket_number: ticketNumber || null,
        status: regData.status || null
      };
      return acc;
    }, {});

    // Add registration details to events
    const eventsWithDetails = userEvents.map(event => ({
      ...event,
      registration: registrationDetailsByEventId[event.id] || {}
    }));

    return NextResponse.json(eventsWithDetails);
  } catch (error) {
    logger.error("Error fetching upcoming events:", error);
    return new NextResponse("Error fetching events", { status: 500 });
  }
}

async function getPastEvents(userId: string) {
  try {
    const supabase = await createClient();
    const eventRepository = new EventRepository();
    const currentDate = new Date().toISOString();

    // First get the user's registrations
    const { data: registrations, error } = await supabase
      .from('registrations')
      .select('event_id, rating, feedback')
      .eq('user_id', userId);

    if (error) {
      logger.error("Error fetching registrations:", error);
      return new NextResponse("Error fetching events", { status: 500 });
    }

    if (!registrations || registrations.length === 0) {
      return NextResponse.json([]);
    }

    // Get all the event IDs from registrations
    const eventIds = registrations.map(reg => (reg as { event_id: string }).event_id);

    // Get all events
    const allEvents = await eventRepository.getAllEvents();

    // Filter for the user's registered past events
    const userEvents = allEvents.filter(event =>
      eventIds.includes(event.id) &&
      event.endDate && new Date(event.endDate) < new Date(currentDate)
    );

    // Sort by start date (descending for past events)
    userEvents.sort((a, b) => {
      // Handle null or undefined dates safely
      const aDate = a.startDate ? new Date(a.startDate).getTime() : 0;
      const bDate = b.startDate ? new Date(b.startDate).getTime() : 0;
      return bDate - aDate;
    });

    // Create a map of registration details by event ID
    type RegistrationDetail = { event_id: string; rating?: number; feedback?: string };

    const registrationDetailsByEventId = (registrations || []).reduce<Record<string, { rating: number | null, feedback: string | null }>>((acc, reg) => {
      const regObj = reg as unknown as RegistrationDetail;

      if (regObj && typeof regObj === 'object' && regObj.event_id) {
        acc[regObj.event_id] = {
          rating: regObj.rating || null,
          feedback: regObj.feedback || null
        };
      }
      return acc;
    }, {});

    // Add registration details to events
    const eventsWithDetails = userEvents.map(event => ({
      ...event,
      registration: registrationDetailsByEventId[event.id] || {}
    }));

    return NextResponse.json(eventsWithDetails);
  } catch (error) {
    logger.error("Error fetching past events:", error);
    return new NextResponse("Error fetching events", { status: 500 });
  }
}

async function getRecommendedEvents(userId: string) {
  try {
    const supabase = await createClient();
    const currentDate = new Date().toISOString();
    const eventRepository = new EventRepository();

    // Get all events (in a real app, would have more complex recommendation logic)
    const events = await eventRepository.getAllEvents();

    // Filter for events that haven't ended yet
    const upcomingEvents = events.filter(event =>
      event.endDate && new Date(event.endDate) >= new Date()
    ).slice(0, 10); // Limit to 10 events

    // Fetch poster images for each event
    if (upcomingEvents.length > 0) {
      const eventIds = upcomingEvents.map(event => event.id);

      const { data: eventImages } = await supabase
        .from('event_images')
        .select('*')
        .in('event_id', eventIds)
        .eq('type', 'poster');

      // Add poster URLs to events
      if (eventImages) {
        const posterMap = eventImages.reduce((acc, img) => {
          // Use type assertion to handle potential type errors
          const typedImg = img as { event_id: string; url?: string };
          if (typedImg && typedImg.event_id) {
            acc[typedImg.event_id] = typedImg.url || '';
          }
          return acc;
        }, {} as Record<string, string>);

        upcomingEvents.forEach(event => {
          (event as { poster?: string | null }).poster = posterMap[event.id] || null;
        });
      }
    }

    return NextResponse.json(upcomingEvents);
  } catch (error) {
    logger.error("Error fetching recommended events:", error);
    return new NextResponse("Error fetching events", { status: 500 });
  }
}

async function getAllEvents() {
  try {
    const eventRepository = new EventRepository();
    const events = await eventRepository.getAllEvents();

    // Fetch poster images for each event
    if (events.length > 0) {
      const supabase = await createClient();
      const eventIds = events.map(event => event.id);

      const { data: eventImages } = await supabase
        .from('event_images')
        .select('*')
        .in('event_id', eventIds)
        .eq('type', 'poster');

      // Add poster URLs to events
      if (eventImages) {
        const posterMap = eventImages.reduce((acc, img) => {
          // Use type assertion to handle potential type errors
          const typedImg = img as { event_id: string; url?: string };
          if (typedImg && typedImg.event_id) {
            acc[typedImg.event_id] = typedImg.url || '';
          }
          return acc;
        }, {} as Record<string, string>);

        const eventsWithPosters = events.map(event => ({
          ...event,
          poster: posterMap[event.id] || null
        }));

        return NextResponse.json(eventsWithPosters);
      }
    }

    return NextResponse.json(events);
  } catch (error) {
    logger.error("Error fetching all events:", error);
    return new NextResponse("Error fetching events", { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    const body = await request.json();

    // Basic validation
    if (!body.title || !body.startDate) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    // Use the event repository for creation
    try {
      const eventRepository = new EventRepository();
      const result = await eventRepository.createEvent({
        title: body.title,
        description: body.description,
        eventTypeId: body.eventTypeId || '00000000-0000-0000-0000-000000000000', // Default ID, should be replaced with actual
        location: body.venue || body.location,
        state: body.state || 'Unknown',
        country: body.country || null,
        startDate: body.startDate,
        endDate: body.endDate || body.startDate,
        timezone: body.timezone || 'UTC',
        organizerId: userId,
        createdBy: userId,
        status: body.status || 'draft',
        emergencyContactSettings: body.emergencyContactSettings || null
      });

      // Check if the result was successful
      if (!result.success || !result.data) {
        return new NextResponse(result.message || "Failed to create event", { status: 500 });
      }

      // Get the event from the result
      const event = result.data;

      // Handle poster image if provided
      if (body.poster) {
        const supabase = await createClient();
        const { error: imageError } = await supabase
          .from('event_images')
          .insert({
            event_id: event.id,
            type: 'poster',
            url: body.poster.url,
            path: body.poster.path,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (imageError) {
          logger.error("Error adding poster image:", imageError);
          // Continue without poster
        }
      }

      return NextResponse.json({
        success: true,
        data: { event, eventId: event.id }
      });
    } catch (error) {
      logger.error("Error creating event:", error);
      return new NextResponse(`Error creating event: ${error instanceof Error ? error.message : 'Unknown error'}`, { status: 500 });
    }
  } catch (error) {
    logger.error("Error in POST handler:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    const body = await request.json();

    if (!body.id) {
      return new NextResponse("Missing event ID", { status: 400 });
    }

    try {
      const eventRepository = new EventRepository();

      // Verify the user owns this event
      const existingEvent = await eventRepository.getEventById(body.id);

      if (!existingEvent) {
        return new NextResponse("Event not found", { status: 404 });
      }

      if (existingEvent.organizerId !== userId && existingEvent.createdBy !== userId) {
        return new NextResponse("Unauthorized - You don't have permission to update this event", { status: 403 });
      }

      // Update the event
      const updatedEvent = await eventRepository.updateEvent(body.id, {
        title: body.title,
        description: body.description,
        eventTypeId: body.eventTypeId,
        location: body.venue || body.location,
        state: body.state,
        country: body.country,
        startDate: body.startDate,
        endDate: body.endDate,
        timezone: body.timezone,
        status: body.status,
        emergencyContactSettings: body.emergencyContactSettings
      });

      // Handle poster image if provided
      if (body.poster) {
        const supabase = await createClient();

        // Check if poster already exists
        const { data: existingPosters } = await supabase
          .from('event_images')
          .select('*')
          .eq('event_id', body.id)
          .eq('type', 'poster');

        if (existingPosters && existingPosters.length > 0) {
          // Update existing poster
          // Check if existingPosters has elements before accessing index 0
          if (existingPosters && existingPosters.length > 0 && existingPosters[0]?.id) {
            const { error: updateError } = await supabase
              .from('event_images')
              .update({
                url: body.poster.url,
                path: body.poster.path,
                updated_at: new Date().toISOString()
              })
              .eq('id', existingPosters[0].id);

            if (updateError) {
              logger.error("Error updating poster image:", updateError);
            }
          } else {
            logger.error("No existing poster found with valid ID");
          }


        } else {
          // Insert new poster
          const { error: insertError } = await supabase
            .from('event_images')
            .insert({
              event_id: body.id,
              type: 'poster',
              url: body.poster.url,
              path: body.poster.path,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            logger.error("Error adding poster image:", insertError);
          }
        }
      }

      return NextResponse.json({
        success: true,
        data: { event: updatedEvent }
      });
    } catch (error) {
      logger.error("Error updating event:", error);
      return new NextResponse(`Error updating event: ${error instanceof Error ? error.message : 'Unknown error'}`, { status: 500 });
    }
  } catch (error) {
    logger.error("Error in PUT handler:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    const { searchParams } = new URL(request.url);
    const eventId = searchParams.get('id');

    if (!eventId) {
      return new NextResponse("Missing event ID", { status: 400 });
    }

    try {
      const eventRepository = new EventRepository();

      // Verify the user owns this event
      const existingEvent = await eventRepository.getEventById(eventId);

      if (!existingEvent) {
        return new NextResponse("Event not found", { status: 404 });
      }

      if (existingEvent.organizerId !== userId && existingEvent.createdBy !== userId) {
        return new NextResponse("Unauthorized - You don't have permission to delete this event", { status: 403 });
      }

      // Delete the event
      await eventRepository.deleteEvent(eventId);

      // Perform any additional cleanup (e.g., removing event images)
      const supabase = await createClient();

      const { error: imageError } = await supabase
        .from('event_images')
        .delete()
        .eq('event_id', eventId);

      if (imageError) {
        logger.error("Error deleting event images:", imageError);
        // Continue even if image deletion failed
      }

      return NextResponse.json({
        success: true,
        message: "Event deleted successfully"
      });
    } catch (error) {
      logger.error("Error deleting event:", error);
      return new NextResponse(`Error deleting event: ${error instanceof Error ? error.message : 'Unknown error'}`, { status: 500 });
    }
  } catch (error) {
    logger.error("Error in DELETE handler:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}