import Link from "next/link";
import { NavItem, renderNavIcon } from "./nav-utils";
import { useState } from "react";

interface SidebarProps {
  navItems: NavItem[];
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

export function Sidebar({ navItems, isOpen, setIsOpen }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Classes to control visibility and width
  const sidebarClasses = `
    ${isOpen ? 'translate-x-0' : '-translate-x-full'}
    md:translate-x-0 fixed md:relative top-0 z-30
    h-screen max-h-screen
    ${isCollapsed ? 'md:w-17' : 'w-64'}
    transition-all duration-300 ease-in-out
  `;

  return (
    <>
      <div className={sidebarClasses}>
        <div className="flex flex-col h-full pt-5 bg-[hsl(var(--background))] dark:bg-[hsl(var(--dark-background))] border-r border-[hsl(var(--border))] overflow-hidden">
          {/* Mobile close button */}
          <div className="flex items-center justify-between px-4 pb-6 md:hidden">
            <button
              onClick={() => setIsOpen(false)}
              className="p-2 text-muted-foreground rounded-md hover:bg-[hsl(var(--primary-50))]"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Desktop header with collapse button */}
          <div className="hidden px-4 pb-6 md:flex items-center justify-between bg-[hsl(var(--background))] dark:bg-[hsl(var(--dark-background))] z-20">
            {!isCollapsed && <h2 className="text-lg font-semibold text-[hsl(var(--primary))] dark:text-[hsl(var(--primary-foreground))]">Dashboard</h2>}
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-2 text-muted-foreground rounded-md hover:bg-[hsl(var(--primary-50))]"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 h-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d={isCollapsed
                    ? "M13 5l7 7-7 7M5 5l7 7-7 7"
                    : "M11 19l-7-7 7-7M19 19l-7-7 7-7"
                  }
                />
              </svg>
            </button>
          </div>

          {/* Navigation items */}
          <div className="flex-1 overflow-y-auto">
            <nav className="px-2 space-y-1 bg-[hsl(var(--background))] dark:bg-[hsl(var(--dark-background))]">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center px-4 py-2 text-sm font-medium rounded-md text-[hsl(var(--foreground))] hover:bg-[hsl(var(--primary-50))] hover:text-[hsl(var(--primary))] dark:text-[hsl(var(--primary-foreground))] dark:hover:text-[hsl(var(--primary))] group"
                  onClick={() => setIsOpen(false)}
                >
                  <div className={`${isCollapsed ? 'w-full flex justify-center' : ''}`}>
                    {renderNavIcon(item.iconName)}
                  </div>
                  {!isCollapsed && <span className="ml-3 text-[hsl(var(--foreground))] dark:text-[hsl(var(--primary-foreground))]">{item.label}</span>}
                </Link>
              ))}
            </nav>
          </div>
        </div>
      </div>
    </>
  );
}