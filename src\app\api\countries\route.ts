import { NextResponse } from 'next/server';

interface Country {
  name: string;
  code: string;
  flag: string;
}

// Interface for the REST Countries API response
interface RestCountryAPIResponse {
  name: {
    common: string;
    official?: string;
  };
  cca2: string;
  flag: string;
  [key: string]: unknown;
}

// Fallback countries data in case the API fails
const FALLBACK_COUNTRIES: Country[] = [
  { name: 'United States', code: 'US', flag: '🇺🇸' },
  { name: 'United Kingdom', code: 'GB', flag: '🇬🇧' },
  { name: 'Canada', code: 'CA', flag: '🇨🇦' },
  { name: 'Australia', code: 'AU', flag: '🇦🇺' },
  { name: 'Germany', code: 'DE', flag: '🇩🇪' },
  { name: 'France', code: 'FR', flag: '🇫🇷' },
  { name: 'Japan', code: 'JP', flag: '🇯🇵' },
  { name: 'China', code: 'CN', flag: '🇨🇳' },
  { name: 'India', code: 'IN', flag: '🇮🇳' },
  { name: 'Brazil', code: 'BR', flag: '🇧🇷' },
  { name: 'Mexico', code: 'MX', flag: '🇲🇽' },
  { name: 'South Africa', code: 'ZA', flag: '🇿🇦' },
  { name: 'Nigeria', code: 'NG', flag: '🇳🇬' },
  { name: 'Singapore', code: 'SG', flag: '🇸🇬' },
  { name: 'New Zealand', code: 'NZ', flag: '🇳🇿' },
  { name: 'Malaysia', code: 'MY', flag: '🇲🇾' },
  { name: 'Indonesia', code: 'ID', flag: '🇮🇩' },
  { name: 'Thailand', code: 'TH', flag: '🇹🇭' },
  { name: 'Philippines', code: 'PH', flag: '🇵🇭' },
  { name: 'Vietnam', code: 'VN', flag: '🇻🇳' },
];

// In-memory cache with expiration time
let countriesCache: {
  data: Country[];
  timestamp: number;
} | null = null;

// Cache validity period - 24 hours in milliseconds
const CACHE_VALIDITY = 24 * 60 * 60 * 1000;

export async function GET() {
  // Check if we have a valid cache
  if (countriesCache && (Date.now() - countriesCache.timestamp) < CACHE_VALIDITY) {
    return NextResponse.json(countriesCache.data);
  }

  try {
    // Fetch countries from the external API
    const response = await fetch('https://restcountries.com/v3.1/all', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 86400 }, // Cache for 24 hours
    });

    if (!response.ok) {
      console.warn('Countries API returned non-OK response, using fallback data');
      return NextResponse.json(FALLBACK_COUNTRIES);
    }

    const data: RestCountryAPIResponse[] = await response.json();

    const countries = data.map((country: RestCountryAPIResponse) => ({
      name: country.name.common,
      code: country.cca2,
      flag: country.flag
    })).sort((a: Country, b: Country) => a.name.localeCompare(b.name));

    // Update the cache
    countriesCache = {
      data: countries,
      timestamp: Date.now()
    };

    return NextResponse.json(countries);
  } catch (error) {
    console.error('Error fetching countries:', error);
    // Return fallback data on error
    return NextResponse.json(FALLBACK_COUNTRIES);
  }
}