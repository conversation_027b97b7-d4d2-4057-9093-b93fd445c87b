import React from 'react';
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, MapPin, Users, BarChart, Settings, ArrowLeft, Edit } from 'lucide-react';
import { createClient } from '@/lib/supabase/server';
import { EventRepository } from '@/repositories/event-repository';
import { formatDate } from '@/utils/formatDate';
import { EventActions } from '@/components/events/dashboard/event-actions';

interface EventDashboardProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EventDashboardPage({ params }: EventDashboardProps) {
  // Await params to fix the "params should be awaited" error in Next.js 14+
  const { id } = await params;

  // Get the Supabase client
  const supabase = await createClient();

  // Get the authenticated user
  const { data: { user: authUser } } = await supabase.auth.getUser();

  if (!authUser) {
    redirect('/sign-in?redirect_url=/dashboard/events/' + id);
  }

  // Get the internal user ID from the users table
  const { data: userData, error: userIdError } = await supabase
    .from('users')
    .select('id')
    .eq('auth_user_id', authUser.id)
    .single();

  if (userIdError || !userData) {
    console.error("Error fetching user ID:", userIdError);
    console.log(`[DEBUG] EventDashboardPage - Failed to get internal user ID for auth user: ${authUser.id}`);
    redirect("/sign-in?redirect_url=/dashboard/events/" + id);
  }

  console.log(`[DEBUG] EventDashboardPage - Found internal user ID: ${userData.id} for auth user: ${authUser.id}`);

  const userId = userData.id;

  // Fetch the event
  const eventRepository = new EventRepository();
  console.log(`[DEBUG] EventDashboardPage - Fetching event with ID: ${id}`);
  const event = await eventRepository.getEventById(id);

  if (!event) {
    console.log(`[DEBUG] EventDashboardPage - Event not found with ID: ${id}`);
    notFound();
  }

  console.log(`[DEBUG] EventDashboardPage - Successfully loaded event: ${event.title} (${event.id})`);

  // Check if the user has permission to view this event
  if (event.organizerId !== userId) {
    console.log(`[DEBUG] EventDashboardPage - User ${userId} is not the organizer of event ${event.id}, checking admin status`);

    // Check if user is an admin (implement this based on your role system)
    const { data: userRole } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();

    const isAdmin = userRole?.role === 'admin' || userRole?.role === 'super_admin';

    console.log(`[DEBUG] EventDashboardPage - User role check:`, {
      userId,
      role: userRole?.role,
      isAdmin
    });

    if (!isAdmin) {
      console.log(`[DEBUG] EventDashboardPage - User ${userId} is not an admin, redirecting to events list`);
      redirect('/dashboard/events');
    }
  }

  // Format dates for display
  const formattedStartDate = event.startDate
    ? formatDate(typeof event.startDate === 'string' ? event.startDate : event.startDate.toISOString())
    : 'Not set';
  const formattedEndDate = event.endDate
    ? formatDate(typeof event.endDate === 'string' ? event.endDate : event.endDate.toISOString())
    : 'Not set';

  // Calculate event statistics
  const registrationCount = 0; // Replace with actual count from database
  const totalCapacity = event.totalCapacity || 0;
  const registrationPercentage = totalCapacity > 0
    ? Math.round((registrationCount / totalCapacity) * 100)
    : 0;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link href="/dashboard/events" className="flex items-center text-sm text-muted-foreground hover:text-foreground mb-4">
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to Events
        </Link>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold">{event.title}</h1>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant={event.status === 'published' ? 'default' : 'secondary'}>
                {event.status === 'published' ? 'Published' : 'Draft'}
              </Badge>
              <span className="text-sm text-muted-foreground">
                ID: {event.id}
              </span>
            </div>
          </div>

          <div className="flex gap-2">
            <Link href={`/dashboard/events/new?id=${event.id}`}>
              <Button variant="outline" size="sm">
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </Link>
            <EventActions
              eventId={event.id}
              eventTitle={event.title}
              isPublished={event.status === 'published'}
            />
            {event.status === 'published' && (
              <Link href={`/events/${event.slug || event.id}`} target="_blank">
                <Button variant="default" size="sm">
                  View Public Page
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Registrations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{registrationCount} / {totalCapacity}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {registrationPercentage}% capacity filled
            </p>
            <div className="w-full bg-secondary h-2 rounded-full mt-2">
              <div
                className="bg-primary h-2 rounded-full"
                style={{ width: `${registrationPercentage}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Event Date</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-muted-foreground" />
              {formattedStartDate}
            </div>
            {event.startDate !== event.endDate && (
              <p className="text-xs text-muted-foreground mt-1">
                Ends: {formattedEndDate}
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Location</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold flex items-center">
              <MapPin className="w-5 h-5 mr-2 text-muted-foreground" />
              {event.location || 'Not set'}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {event.city ? `${event.city}, ` : ''}{event.state || ''}
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="attendees">Attendees</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Event Details</CardTitle>
              <CardDescription>
                Basic information about your event
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Description</h3>
                <p className="text-sm text-muted-foreground whitespace-pre-line">
                  {event.description || 'No description provided.'}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium mb-2">Date & Time</h3>
                  <div className="flex items-start gap-2">
                    <Calendar className="w-4 h-4 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm">{formattedStartDate} - {formattedEndDate}</p>
                      <p className="text-xs text-muted-foreground">
                        Timezone: {event.timezone || 'Not set'}
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Location</h3>
                  <div className="flex items-start gap-2">
                    <MapPin className="w-4 h-4 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm">{event.location || 'Not set'}</p>
                      <p className="text-xs text-muted-foreground">
                        {event.city ? `${event.city}, ` : ''}{event.state || ''}{event.country ? `, ${event.country}` : ''}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Registration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start gap-2">
                    <Users className="w-4 h-4 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm">Capacity: {event.totalCapacity || 'Unlimited'}</p>
                      <p className="text-xs text-muted-foreground">
                        {registrationCount} registrations so far
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <Clock className="w-4 h-4 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm">
                        Registration closes: {event.registrationCloseDate
                          ? formatDate(typeof event.registrationCloseDate === 'string'
                            ? event.registrationCloseDate
                            : event.registrationCloseDate.toISOString())
                          : 'Not set'}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {event.allowCategorySpecificClosingDates ? 'Category-specific closing dates enabled' : 'Same closing date for all categories'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest updates and registrations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground text-center py-6">
                No recent activity to display.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attendees">
          <Card>
            <CardHeader>
              <CardTitle>Attendees</CardTitle>
              <CardDescription>
                Manage event registrations and attendees
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground text-center py-6">
                No attendees registered yet.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <Card>
            <CardHeader>
              <CardTitle>Event Statistics</CardTitle>
              <CardDescription>
                Analytics and insights for your event
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <BarChart className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-sm text-muted-foreground">
                    Statistics will be available once your event has registrations.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Event Settings</CardTitle>
              <CardDescription>
                Configure event options and permissions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Event Status</h3>
                <div className="flex items-center gap-2">
                  <Badge variant={event.status === 'published' ? 'default' : 'secondary'}>
                    {event.status === 'published' ? 'Published' : 'Draft'}
                  </Badge>
                  {event.status === 'published' ? (
                    <Button variant="outline" size="sm">
                      Unpublish Event
                    </Button>
                  ) : (
                    <Button variant="default" size="sm">
                      Publish Event
                    </Button>
                  )}
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Registration Settings</h3>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    Open Registration
                  </Button>
                  <Button variant="outline" size="sm">
                    Close Registration
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Danger Zone</h3>
                <Button variant="destructive" size="sm">
                  Cancel Event
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
