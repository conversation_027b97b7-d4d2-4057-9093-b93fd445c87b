import { NextResponse } from 'next/server';
import { SchemaManager } from '@/lib/db/schema-manager';

/**
 * POST endpoint to refresh the Supabase schema cache
 * This can be called with a secret key to bypass authentication
 */
export async function POST(request: Request) {
  try {
    // Get request body to check for secret key
    let secretKey = '';
    try {
      const body = await request.json();
      secretKey = body?.secretKey || '';
    } catch (e) {
      // Ignore JSON parsing errors
    }
    
    // Check if secret key matches - only allow admin operations with correct key
    const validSecret = process.env.ADMIN_SECRET_KEY;
    if (!validSecret || secretKey !== validSecret) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const result = await SchemaManager.refreshSchemaCache();
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error refreshing schema cache:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 