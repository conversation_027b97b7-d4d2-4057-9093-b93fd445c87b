-- <PERSON>reate saved_contacts table for storing friends and family information
CREATE TABLE IF NOT EXISTS saved_contacts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL,
  first_name TEXT NOT NULL,
  last_name TEXT,
  relationship TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  date_of_birth DATE,
  gender TEXT,
  tshirt_size TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  country TEXT,
  postcode TEXT,
  emergency_contact_name TEXT,
  emergency_contact_no TEXT,
  emergency_contact_relationship TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS saved_contacts_user_id_idx ON saved_contacts(user_id);

-- Add this table to the Supabase Database types
COMMENT ON TABLE saved_contacts IS 'Stores user saved contacts for friends and family'; 