# Supplier Reporting System for Running Events

## System Overview

The Supplier Reporting System enables event organizers to generate accurate, detailed reports for vendors supplying race materials, including t-shirts, bibs, medals, and race kits. This system streamlines communication with suppliers and ensures all participants receive the correct items.

## Key Reports

### T-Shirt Report

#### Purpose
Provide detailed breakdown of t-shirt requirements by size, gender, design, and category for accurate production orders.

#### Content
- **Total Count**: Overall number of shirts needed
- **Size Breakdown**: Quantities by size (XS, S, M, L, XL, XXL, etc.)
- **Gender-Specific Breakdown**: Men's vs. Women's cuts (if applicable)
- **Design Variation**: Quantities by design variant (if multiple designs)
- **Category Distribution**: Breakdown by race category (e.g., 5K vs. Marathon shirts)
- **Staff/Volunteer Shirts**: Additional shirts for event staff
- **Extra Inventory**: Buffer stock recommendations (typically 5-10%)

#### Format Options
- Excel spreadsheet (XLSX)
- CSV file
- PDF report with visual charts
- Direct API integration with supplier systems

#### Generation Timing
- Preliminary report (based on registration projections)
- Mid-registration update (at 50% capacity)
- Final production order (at registration close)
- Last-minute adjustments (limited to critical changes)

### Bib Number Report

#### Purpose
Provide comprehensive details for bib production including number assignments, category designations, and special cases.

#### Content
- **Total Count**: Overall number of bibs needed
- **Category Breakdown**: 
  - Number ranges by category
  - Color coding specifications
  - Category-specific markings or designs
- **Bib Number Allocations**:
  - Start and end numbers for each range
  - Reserved numbers (elites, celebrities, etc.)
  - Special bib numbers with custom handling
- **Technical Specifications**:
  - Timing chip requirements
  - Barcode/QR code specifications
  - RFID integration details (if applicable)
- **Design Elements**:
  - Sponsor logos placement
  - Color schemes
  - Category indicators
  - Emergency information

#### Format Options
- Structured CSV/Excel for production systems
- PDF specification document with visual examples
- Production-ready design files (AI, EPS, PDF)
- Data file for automated bib number printing

#### Special Features
- Customizable bib templates by category
- Integration with participant data
- Emergency contact information inclusion
- Medical alert indicators for at-risk participants

### Race Kit & Merchandise Report

#### Purpose
Detail the complete contents and quantities for race kits, including all participant giveaways and merchandise.

#### Content
- **Kit Components Breakdown**:
  - Required items per participant
  - Category-specific items
  - Sponsored products
- **Merchandise Options**:
  - Pre-ordered items by participant
  - Size and style distributions
  - Optional add-ons
- **Packaging Requirements**:
  - Bag/container specifications
  - Assembly instructions
  - Special handling notes
- **Distribution Planning**:
  - Sorting methodology (alphabetical, bib number, etc.)
  - Pickup location allocations
  - VIP/special case kits

## Bib Number Generation System

### Generation Algorithms

#### Category-Based Assignment
```typescript
function generateCategoryBibs(category: RaceCategory): BibAssignment[] {
  const bibs: BibAssignment[] = [];
  const startNumber = category.bibNumberStart;
  
  // Get registered participants for this category
  const participants = getParticipantsByCategory(category.id);
  
  // Sort participants based on assignment rules
  const sortedParticipants = sortParticipants(
    participants, 
    category.bibAssignmentRules
  );
  
  // Assign sequential numbers, skipping reserved numbers
  let currentNumber = startNumber;
  for (const participant of sortedParticipants) {
    // Skip reserved numbers
    while (isReservedNumber(currentNumber, category.reservedNumbers)) {
      currentNumber++;
    }
    
    // Create the bib assignment
    bibs.push({
      id: generateId(),
      eventId: category.eventId,
      registrationId: participant.registrationId,
      ticketId: participant.ticketId,
      categoryId: category.id,
      bibNumber: formatBibNumber(category.bibPrefix, currentNumber),
      assignedAt: new Date(),
      status: 'assigned'
    });
    
    currentNumber++;
  }
  
  return bibs;
}
```

#### Special Cases Handling
- Elite athletes (special number ranges)
- VIPs and celebrities (custom requested numbers)
- Charity participants (special prefix or number range)
- Teams (sequential or grouped numbers)
- Accessibility needs (specific number ranges for identification)

### Bib Format Customization

#### Pattern Options
- Simple sequential numbers: `1, 2, 3, ...`
- Category prefixes: `5K-001, 10K-001, ...`
- Year prefixes: `2023-0001, ...`
- Custom prefixes: `ABC-0001, ...`

#### Format Rules
- Minimum digits (e.g., padding with zeros)
- Prefix separator characters
- Upper/lowercase handling
- Maximum length constraints

## T-Shirt Inventory Management

### Size Collection
- Standard international sizing (XS, S, M, L, XL, XXL, 3XL)
- Different cut options (Men's, Women's, Unisex)
- Fit options (Regular, Athletic, Relaxed)
- Youth sizes if applicable

### Inventory Tracking
- Real-time count by size
- Threshold alerts for low inventory
- Size substitution recommendations
- Historical size distribution analysis

### Forecasting Tools
- Predict final size distribution from early registrations
- Compare to historical events
- Adjust for demographic differences
- Calculate buffer stock requirements

## Reporting Interface

### Dashboard View
- Registration progress vs. goals
- Current inventory status
- Required vs. available stock
- Production timeline tracking

### Report Generation Interface
1. Report type selection
2. Customization options:
   - Date range
   - Categories to include
   - Export format
   - Grouping options
3. Preview functionality
4. Distribution options:
   - Download
   - Email to supplier
   - Print
   - API export

### Supplier Portal (Optional)
- Secure login for vendors
- Real-time registration data
- Production milestone tracking
- Communication channel with organizers

## Technical Architecture

### Data Flow
1. Registration data collected from participants
2. Stored in primary event database
3. Processed through reporting engine
4. Formatted according to supplier specifications
5. Distributed via selected channels

### Integration Points
- Event registration system
- Inventory management system
- Check-in/fulfillment system
- Supplier order systems (optional)

### Security Considerations
- Personally identifiable information (PII) handling
- Data anonymization where appropriate
- Secure transmission to suppliers
- Access controls for sensitive reports

## Implementation Phases

### Phase 1: Basic Reporting
- Standard t-shirt and bib reports
- Manual export to CSV/Excel
- Basic bib number assignment

### Phase 2: Enhanced Reports
- Custom report templates
- Automated generation schedules
- Advanced bib assignment algorithms
- Inventory forecasting

### Phase 3: Supplier Integration
- Direct API connections to suppliers
- Real-time inventory updates
- Production tracking
- Digital proofing system

## Best Practices

### Report Generation Timing
- Generate preliminary reports at 50% registration
- Update at 75% and 90% capacity
- Final reports 2-3 weeks before event (or supplier deadline)
- Include buffer stock in all calculations

### Quality Control
- Sample verification process
- Digital proofing workflow
- Pre-production approval checkpoints
- Post-production quality checks

### Contingency Planning
- Rush order procedures
- Size substitution policies
- On-site solutions for shortages
- Backup supplier relationships 