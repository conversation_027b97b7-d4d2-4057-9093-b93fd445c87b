import { ReactNode } from 'react';

interface LegalLayoutProps {
  children: ReactNode;
}

export const metadata = {
  title: 'Legal - Fuiyoo Events',
  description: 'Legal information for the Fuiyoo event platform',
};

export default function LegalLayout({ children }: LegalLayoutProps) {
  return (
    <div className="max-w-4xl mx-auto px-4 py-12 md:py-16">
      <main className="bg-white rounded-lg shadow-sm p-6 md:p-8">
        {children}
      </main>
    </div>
  );
} 