import { EventRepository, Event } from '@/lib/repositories/event-repository';
import { z } from 'zod';

// Define the schema for creating an event
export const createEventSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  location: z.string().min(3, 'Location must be at least 3 characters'),
  start_date: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    { message: 'Invalid start date format' }
  ),
  end_date: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    { message: 'Invalid end date format' }
  ),
  organization_id: z.string().optional(),
  ticketTypes: z.array(
    z.object({
      name: z.string().min(1, 'Ticket type name is required'),
      price: z.number().min(0, 'Price must be 0 or greater'),
      capacity: z.number().int().min(1, 'Capacity must be at least 1'),
    })
  ).optional(),
});

// Define the input type for creating an event
export type CreateEventInput = z.infer<typeof createEventSchema>;

export class EventService {
  private eventRepository: EventRepository;

  constructor() {
    this.eventRepository = new EventRepository();
  }

  /**
   * Create a new event
   */
  async createEvent(input: CreateEventInput): Promise<Event | null> {
    try {
      // Validate input
      const validated = createEventSchema.parse(input);

      // Extract ticket types from input
      const { ticketTypes, ...eventData } = validated;

      // Create the event
      const event = await this.eventRepository.create({
        ...eventData,
        status: 'draft',
      } as Omit<Event, 'id'>);

      if (!event) {
        return null;
      }

      // TODO: Create ticket types if provided
      // This would require a TicketTypeRepository

      return event;
    } catch (error) {
      console.error('Error creating event:', error);
      throw error;
    }
  }

  /**
   * Get event dashboard data for the current user
   */
  async getEventDashboardData(): Promise<{
    draftEvents: Event[];
    publishedEvents: Event[];
    pastEvents: Event[];
  }> {
    try {
      const allEvents = await this.eventRepository.getByCurrentUser();
      const now = new Date();

      return {
        draftEvents: allEvents.filter((e: Event) => e.status === 'draft'),
        publishedEvents: allEvents.filter(
          (e: Event) => e.status === 'published' && new Date(e.end_date) >= now
        ),
        pastEvents: allEvents.filter(
          (e: Event) => e.status === 'published' && new Date(e.end_date) < now
        ),
      };
    } catch (error) {
      console.error('Error getting event dashboard data:', error);
      return {
        draftEvents: [],
        publishedEvents: [],
        pastEvents: [],
      };
    }
  }

  /**
   * Publish an event
   */
  async publishEvent(eventId: string): Promise<Event | null> {
    try {
      return this.eventRepository.publishEvent(eventId);
    } catch (error) {
      console.error('Error publishing event:', error);
      return null;
    }
  }

  /**
   * Cancel an event
   */
  async cancelEvent(eventId: string): Promise<Event | null> {
    try {
      return this.eventRepository.cancelEvent(eventId);
    } catch (error) {
      console.error('Error cancelling event:', error);
      return null;
    }
  }

  /**
   * Get event details
   */
  async getEventDetails(eventId: string): Promise<Event | null> {
    try {
      return this.eventRepository.getEventWithDetails(eventId);
    } catch (error) {
      console.error('Error getting event details:', error);
      return null;
    }
  }

  /**
   * Search events
   */
  async searchEvents(query: string, limit = 10): Promise<Event[]> {
    try {
      return this.eventRepository.searchEvents(query, limit);
    } catch (error) {
      console.error('Error searching events:', error);
      return [];
    }
  }
}
