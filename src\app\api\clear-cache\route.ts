import { NextResponse } from 'next/server';
import { revalidatePath } from 'next/cache';

/**
 * POST endpoint to clear Next.js cache
 * This will revalidate all paths to ensure the latest data is fetched
 */
export async function POST() {
  try {
    // Revalidate all paths by using '/'
    revalidatePath('/', 'layout');
    
    return NextResponse.json({
      success: true,
      message: 'Next.js cache cleared successfully',
      revalidated: true,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error clearing Next.js cache:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
} 