#!/bin/bash

# This script provides a unified way to start the Next.js application
# in production mode

# Kill any existing Next.js servers
lsof -ti:3000 | xargs kill -9 2>/dev/null || true

# Parse command line options
CLEAN_CACHE=false

while [[ "$#" -gt 0 ]]; do
  case $1 in
    --clean-cache) CLEAN_CACHE=true ;;
    *) echo "Unknown parameter: $1"; exit 1 ;;
  esac
  shift
done

# Set environment variables
export NODE_OPTIONS="--max-old-space-size=4096"

if [ "$CLEAN_CACHE" = true ]; then
  echo "Cleaning Next.js cache..."
  rm -rf .next/cache
fi

echo "Starting Next.js production server..."
pnpm start