# Product Context

## Purpose
The platform serves as an event management system where users can discover events, and event organizers can create and manage their events. The system requires role-based access control to differentiate between regular users, event organizers, and administrators.

## Problems Solved
- Regular users need a clear path to become event organizers
- Admins need tools to manage users, roles, and platform content
- The platform needs controlled access to features based on user roles
- Organization registration requires a comprehensive yet user-friendly process

## Key User Journeys

### User to Event Organizer Promotion
1. User navigates to the Organizations tab in their dashboard
2. User sees a prompt to upgrade to become an event organizer
3. User clicks on the CTA to start the application process
4. User completes a multi-step registration form
5. User can save progress and return to complete the application later
6. Admin reviews and approves/rejects the application
7. User receives notification of the decision
8. If approved, user gains access to event creation capabilities

### Admin Management
1. Ad<PERSON> logs into the platform and accesses the admin dashboard
2. Admin can view and manage all users, changing roles as needed
3. <PERSON>min can review pending organization applications
4. Admin can manage events across the platform
5. Admin can view platform analytics and reports
6. Admin can configure platform settings and RBAC

## User Experience Goals
- Create a smooth, guided process for user promotion to event organizer
- Ensure the multi-step form is intuitive with clear progress indication
- Provide a way for users to save their progress and resume later
- Make the admin dashboard comprehensive yet easy to navigate
- Ensure appropriate access controls based on user roles

## Problem Statement
Event organizers face challenges in accurately tracking participant data, managing event logistics, and providing a consistent user experience. Current solutions often:
- Fail to validate and normalize user data
- Lack accurate tagging and categorizing mechanisms
- Don't handle multi-tenant requirements appropriately
- Struggle with accurate participant tracking
- Cannot properly manage goody bag distribution
- Provide poor data export capabilities

Fuiyoo (formerly Fuiyoh) solves these problems by providing a comprehensive event management platform tailored to event organizers' needs.

## Solution Overview
Fuiyoo is a comprehensive event management and ticketing platform designed with a focus on:

1. **Accurate Data Management**
   - Schema validation for participant data
   - Proper normalization of user information
   - Consistent data structure across events

2. **Multi-tenant Architecture**
   - Separate subdomain for each organization
   - Isolated data access with proper tenant boundaries
   - Tenant-specific customization options

3. **Event Check-in & Logistics**
   - QR code-based attendance tracking
   - Goody bag distribution management
   - Real-time attendee list updates

4. **User Experience**
   - Streamlined event registration
   - Secure payment processing
   - Mobile-friendly interface

## Deployment
- **Development Environment**: http://localhost:3000
- **Production Environment**: https://fuiyoo.netlify.app (Updated from fuiyoh.netlify.app)
- **Edge-optimized**: Leveraging Netlify's edge functions for optimal performance
- **Global CDN**: Content delivery via Netlify's global CDN

## User Types & Personas

### Event Attendees
**Persona: Alex, 28, Fitness Enthusiast**
- Regularly participates in charity runs and fitness events
- Wants easy registration and payment process
- Expects digital tickets and event reminders
- Needs clear information about event logistics

### Event Organizers
**Persona: Sarah, 35, NGO Event Coordinator**
- Organizes 5-10 events per year
- Needs accurate participant data
- Requires efficient check-in system
- Wants simple export options for logistics planning
- Focused on minimizing manual data entry

### Platform Administrators
**Persona: Michael, 40, Tech Lead**
- Manages the platform infrastructure
- Monitors system performance
- Handles organizer verification
- Ensures proper data handling and security
- Supports organizers with technical issues

## Key Features

### For Attendees
- Discover events by category, date, or location
- Register and pay securely
- Receive QR code tickets via email
- Track event history and participation

### For Organizers
- Create and customize events
- Define custom registration forms
- Track ticket sales and revenue
- Manage attendee check-in
- Handle goody bag distribution
- Export participant data

### For Administrators
- Verify organizer applications
- Review submitted documents
- Monitor system metrics
- Manage platform configuration
- Handle support requests

## Success Metrics
1. **For Platform**
   - Number of active organizers
   - Total events created per month
   - Ticket sales volume
   - Platform revenue (6% fee)

2. **For Organizers**
   - Registration conversion rate
   - Check-in efficiency
   - Data export accuracy
   - Repeat event creation

3. **For Attendees**
   - Registration completion time
   - Payment success rate
   - Event satisfaction rating

## Market Differentiation
Fuiyoo differentiates itself from competitors through:
- **Superior Data Handling**: Validated schemas and consistent data structure
- **Multi-tenant Architecture**: True isolation between organizations
- **Logistics Focus**: Specialized tools for event day management
- **Local Focus**: Optimized for Malaysian market needs and payment methods
- **Affordable Pricing**: 6% fee or RM2.50 minimum, lower than market average 