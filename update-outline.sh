#!/bin/bash

# Update outline-none to outline-hidden for Tailwind CSS v4
echo "Updating outline-none to outline-hidden for Tailwind CSS v4..."

# Find all files containing outline-none and replace with outline-hidden
find ./src -type f -name "*.tsx" -o -name "*.jsx" -o -name "*.ts" -o -name "*.js" | xargs grep -l "outline-none" | xargs sed -i '' 's/outline-none/outline-hidden/g'

echo "Update complete!" 