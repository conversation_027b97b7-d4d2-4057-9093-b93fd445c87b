/**
 * Currency utilities for formatting prices based on country
 */

// Map of country codes to currency information
export interface CurrencyInfo {
  code: string;
  symbol: string;
  position: 'prefix' | 'suffix';
  decimalSeparator: string;
  thousandsSeparator: string;
}

// Currency information for common countries
export const CURRENCY_MAP: Record<string, CurrencyInfo> = {
  // Default (USD)
  'DEFAULT': {
    code: 'USD',
    symbol: '$',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // Malaysia
  'MY': {
    code: 'MYR',
    symbol: 'RM',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // United States
  'US': {
    code: 'USD',
    symbol: '$',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // United Kingdom
  'GB': {
    code: 'GBP',
    symbol: '£',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // European Union
  'EU': {
    code: 'EUR',
    symbol: '€',
    position: 'suffix',
    decimalSeparator: ',',
    thousandsSeparator: '.'
  },
  // Germany, France, Italy, Spain, etc.
  'DE': {
    code: 'EUR',
    symbol: '€',
    position: 'suffix',
    decimalSeparator: ',',
    thousandsSeparator: '.'
  },
  'FR': {
    code: 'EUR',
    symbol: '€',
    position: 'suffix',
    decimalSeparator: ',',
    thousandsSeparator: '.'
  },
  'IT': {
    code: 'EUR',
    symbol: '€',
    position: 'suffix',
    decimalSeparator: ',',
    thousandsSeparator: '.'
  },
  'ES': {
    code: 'EUR',
    symbol: '€',
    position: 'suffix',
    decimalSeparator: ',',
    thousandsSeparator: '.'
  },
  // Japan
  'JP': {
    code: 'JPY',
    symbol: '¥',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // China
  'CN': {
    code: 'CNY',
    symbol: '¥',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // Australia
  'AU': {
    code: 'AUD',
    symbol: 'A$',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // Canada
  'CA': {
    code: 'CAD',
    symbol: 'C$',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // Singapore
  'SG': {
    code: 'SGD',
    symbol: 'S$',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // India
  'IN': {
    code: 'INR',
    symbol: '₹',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // Brazil
  'BR': {
    code: 'BRL',
    symbol: 'R$',
    position: 'prefix',
    decimalSeparator: ',',
    thousandsSeparator: '.'
  },
  // South Africa
  'ZA': {
    code: 'ZAR',
    symbol: 'R',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // Thailand
  'TH': {
    code: 'THB',
    symbol: '฿',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // Indonesia
  'ID': {
    code: 'IDR',
    symbol: 'Rp',
    position: 'prefix',
    decimalSeparator: ',',
    thousandsSeparator: '.'
  },
  // Philippines
  'PH': {
    code: 'PHP',
    symbol: '₱',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
  // Vietnam
  'VN': {
    code: 'VND',
    symbol: '₫',
    position: 'suffix',
    decimalSeparator: ',',
    thousandsSeparator: '.'
  },
  // New Zealand
  'NZ': {
    code: 'NZD',
    symbol: 'NZ$',
    position: 'prefix',
    decimalSeparator: '.',
    thousandsSeparator: ','
  },
};

/**
 * Get currency information for a country code
 * @param countryCode ISO country code (e.g., 'US', 'MY')
 * @returns Currency information for the country
 */
export function getCurrencyInfo(countryCode?: string): CurrencyInfo {
  const defaultCurrency = CURRENCY_MAP['DEFAULT'];
  if (!defaultCurrency) {
    throw new Error('Default currency not found');
  }

  if (!countryCode) return defaultCurrency;

  return CURRENCY_MAP[countryCode] || defaultCurrency;
}

/**
 * Format a price according to the country's currency format
 * @param price Price to format
 * @param countryCode ISO country code (e.g., 'US', 'MY')
 * @returns Formatted price string
 */
export function formatCurrency(price?: number, countryCode?: string): string {
  // Handle undefined, null, or NaN prices
  if (price === undefined || price === null || isNaN(price)) {
    return 'Free';
  }

  // Get currency info for the country
  const currencyInfo = getCurrencyInfo(countryCode);

  // Format the number with proper separators
  const formattedNumber = formatNumber(
    price,
    currencyInfo.decimalSeparator,
    currencyInfo.thousandsSeparator
  );

  // Apply the currency symbol in the correct position
  if (currencyInfo.position === 'prefix') {
    return `${currencyInfo.symbol}${formattedNumber}`;
  } else {
    return `${formattedNumber} ${currencyInfo.symbol}`;
  }
}

/**
 * Format a number with the specified decimal and thousands separators
 * @param num Number to format
 * @param decimalSeparator Character to use as decimal separator
 * @param thousandsSeparator Character to use as thousands separator
 * @returns Formatted number string
 */
function formatNumber(
  num: number,
  decimalSeparator: string = '.',
  thousandsSeparator: string = ','
): string {
  // Handle special case for JPY and similar currencies that don't use decimals
  const isNoDecimalCurrency = ['JPY', 'KRW', 'VND', 'IDR'].includes(getCurrencyInfo().code);

  // Format with 0 or 2 decimal places based on currency type
  const parts = isNoDecimalCurrency
    ? Math.round(num).toString().split('.')
    : num.toFixed(2).split('.');

  // Add thousands separator
  if (parts[0]) {
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
  }

  // Join with decimal separator if there's a decimal part
  return parts.length > 1 ? parts.join(decimalSeparator) : (parts[0] || '0');
}

/**
 * Format a price range according to the country's currency format
 * @param minPrice Minimum price
 * @param maxPrice Maximum price
 * @param countryCode ISO country code (e.g., 'US', 'MY')
 * @returns Formatted price range string
 */
export function formatPriceRange(
  minPrice?: number,
  maxPrice?: number,
  countryCode?: string
): string {
  // If both prices are undefined, null, or NaN, return 'Free'
  if (
    (minPrice === undefined || minPrice === null || isNaN(minPrice)) &&
    (maxPrice === undefined || maxPrice === null || isNaN(maxPrice))
  ) {
    return 'Free';
  }

  // If only one price is defined, format it as a single price
  if (minPrice === undefined || minPrice === null || isNaN(minPrice)) {
    return formatCurrency(maxPrice, countryCode);
  }
  if (maxPrice === undefined || maxPrice === null || isNaN(maxPrice)) {
    return formatCurrency(minPrice, countryCode);
  }

  // If min and max are the same, format as a single price
  if (minPrice === maxPrice) {
    return formatCurrency(minPrice, countryCode);
  }

  // Format as a range
  return `${formatCurrency(minPrice, countryCode)} - ${formatCurrency(maxPrice, countryCode)}`;
}
