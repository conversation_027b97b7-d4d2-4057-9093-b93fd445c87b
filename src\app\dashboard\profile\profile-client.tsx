'use client';

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PersonalInfo } from "@/components/profile/personal-info";
import { ContactsList } from "@/components/profile/contacts-list";
import { PrivacySettings } from "@/components/profile/privacy-settings";
import { ActivityHistory } from "@/components/profile/activity-history";
import { DataExport } from "@/components/profile/data-export";
import { ProfileCompletion } from "@/components/profile/profile-completion";
import { Button } from "@/components/ui/button";
import { Pencil, ChevronDown } from "lucide-react";
import { createPortal } from "react-dom";

interface UserData {
  id: string;
  first_name: string;
  last_name?: string;
  email: string;
  role: string;
  gender?: string;
  username?: string;
}

const navigationItems = [
  {
    href: "/dashboard/profile",
    label: "Profile",
    iconName: "UserCircle",
  },
  {
    href: "/dashboard",
    label: "Overview",
    iconName: "LayoutDashboard",
  },
  {
    href: "/dashboard/events",
    label: "Events",
    iconName: "Calendar",
  },
  {
    href: "/dashboard/organizations",
    label: "Organizations",
    iconName: "Users",
  },
  {
    href: "/dashboard/tickets",
    label: "Tickets",
    iconName: "Ticket",
  },
  {
    href: "/dashboard/payments",
    label: "Payments",
    iconName: "CreditCard",
  },
];

export default function ProfileClient({ user }: { user: UserData }) {
  const [activeTab, setActiveTab] = useState("info");
  const [isEditing, setIsEditing] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);

  useEffect(() => {
    setPortalContainer(document.body);
  }, []);

  // Function to toggle edit mode state that can be passed to the PersonalInfo component
  const toggleEditMode = () => {
    setIsEditing(!isEditing);
  };

  // Tab options mapping for dropdown
  const tabOptions = {
    "info": "Personal Info",
    "contacts": "Contacts",
    "privacy": "Privacy",
    "activity": "Activity",
    "export": "Data Export"
  };

  // Close dropdown when clicked outside
  useEffect(() => {
    if (!mobileMenuOpen) return;

    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.mobile-tabs-dropdown-button') &&
        !target.closest('.mobile-tabs-dropdown-menu')) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [mobileMenuOpen]);

  return (
    <div className="w-full py-4 space-y-4 relative">

      <div className="flex flex-col md:flex-row items-center justify-between text-center gap-4 px-2">
        <div className="w-full flex items-center justify-center md:justify-start">
          <h1 className="text-3xl font-bold">Your Profile</h1>

          {!isEditing && (
            <Button
              onClick={toggleEditMode}
              variant="outline"
              size="icon"
              className="shadow-sm font-medium ml-4
                        h-9 w-9 rounded-full p-0
                        md:h-9 md:w-auto md:px-4 md:py-2 md:rounded-md
                        border-primary bg-background text-primary
                        hover:bg-primary hover:text-primary-foreground"
              aria-label="Edit profile"
            >
              <Pencil className="h-4 w-4" />
              <span className="hidden md:inline ml-1">Edit Profile</span>
            </Button>
          )}
        </div>
        <div className="w-full md:w-auto flex justify-center">
          <ProfileCompletion userId={user.id} />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        {/* Mobile Dropdown for Tabs */}
        <div className="md:hidden relative">
          <Button
            variant="outline"
            onClick={(e) => {
              e.stopPropagation();
              setMobileMenuOpen(!mobileMenuOpen);
            }}
            className="w-full flex items-center justify-between py-3 px-4 border rounded-md mb-4 mobile-tabs-dropdown-button"
          >
            <span>{tabOptions[activeTab as keyof typeof tabOptions]}</span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>

          {mobileMenuOpen && portalContainer && createPortal(
            <>
              {/* Semi-transparent backdrop */}
              <div
                className="fixed inset-0 bg-black/40 backdrop-blur-sm"
                onClick={() => setMobileMenuOpen(false)}
                style={{ zIndex: 9998 }}
              />

              {/* Dropdown menu with solid background */}
              <div
                className="fixed left-0 right-0 mx-4 mt-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-lg mobile-tabs-dropdown-menu"
                style={{
                  zIndex: 9999,
                  top: '10.5rem',
                  maxHeight: '80vh',
                  overflowY: 'auto'
                }}
              >
                {Object.entries(tabOptions).map(([value, label]) => (
                  <button
                    key={value}
                    className={`w-full text-left px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 ${value === activeTab ? 'bg-[hsl(var(--accent))] text-[hsl(var(--accent-foreground))]' : 'text-gray-700 dark:text-gray-300'
                      }`}
                    onClick={() => {
                      setActiveTab(value);
                      setMobileMenuOpen(false);
                    }}
                  >
                    {label}
                  </button>
                ))}
              </div>
            </>,
            portalContainer
          )}
        </div>

        {/* Desktop Tabs */}
        <div className="relative hidden md:block">
          <TabsList className="grid grid-cols-5 w-full">
            <TabsTrigger value="info" className="justify-center">Personal Info</TabsTrigger>
            <TabsTrigger value="contacts" className="justify-center">Contacts</TabsTrigger>
            <TabsTrigger value="privacy" className="justify-center">Privacy</TabsTrigger>
            <TabsTrigger value="activity" className="justify-center">Activity</TabsTrigger>
            <TabsTrigger value="export" className="justify-center">Data Export</TabsTrigger>
          </TabsList>
        </div>

        <div className="mt-6">
          {/* Personal Info Tab */}
          <TabsContent value="info" className="space-y-4">
            <PersonalInfo isEditing={isEditing} onToggleEdit={toggleEditMode} />
          </TabsContent>

          {/* Contacts Tab */}
          <TabsContent value="contacts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Saved Contacts</CardTitle>
                <CardDescription>
                  Manage your saved contacts for faster event registration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ContactsList />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Privacy Tab */}
          <TabsContent value="privacy" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Privacy Settings</CardTitle>
                <CardDescription>
                  Manage your consent settings and profile visibility
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PrivacySettings />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Activity History</CardTitle>
                <CardDescription>
                  View your recent profile changes and activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ActivityHistory />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Data Export Tab */}
          <TabsContent value="export" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Data Export</CardTitle>
                <CardDescription>
                  Request exports of your personal data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DataExport userId={user.id} />
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}