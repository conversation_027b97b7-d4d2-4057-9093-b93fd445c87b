/**
 * Environment detection utilities for Next.js 15
 *
 * These functions provide a simplified and more efficient way to detect
 * the current environment (development, production, etc.)
 */

export type BuildEnvironment = 'development' | 'production' | 'test' | 'unknown';

// Cache environment values to avoid repeated checks
const ENV_CACHE = {
  nodeEnv: typeof process !== 'undefined' ? process.env.NODE_ENV : undefined,
  isTurbo: typeof process !== 'undefined' ? process.env.NEXT_TURBO === '1' : false,
  isEdge: typeof process !== 'undefined' ? process.env.NEXT_RUNTIME === 'edge' : false,
  isServer: typeof window === 'undefined',
  isClient: typeof window !== 'undefined',
};

/**
 * Detects if the current build is using Turbopack
 */
export function isTurbopack(): boolean {
  return ENV_CACHE.isTurbo;
}

/**
 * Detects if the current build is using Webpack
 */
export function isWebpack(): boolean {
  return !ENV_CACHE.isTurbo;
}

/**
 * Gets the current build environment
 */
export function getBuildEnv(): BuildEnvironment {
  return ENV_CACHE.nodeEnv as BuildEnvironment || 'unknown';
}

/**
 * Check if the app is running in development mode
 */
export function isDev(): boolean {
  return ENV_CACHE.nodeEnv === 'development';
}

/**
 * Check if the app is running in production mode
 */
export function isProd(): boolean {
  return ENV_CACHE.nodeEnv === 'production';
}

/**
 * Check if the app is running in test mode
 */
export function isTest(): boolean {
  return ENV_CACHE.nodeEnv === 'test';
}

/**
 * Check if the code is running on the server
 */
export function isServer(): boolean {
  return ENV_CACHE.isServer;
}

/**
 * Check if the code is running in the browser
 */
export function isClient(): boolean {
  return ENV_CACHE.isClient;
}

/**
 * Check if the code is running in an edge runtime
 */
export function isEdge(): boolean {
  return ENV_CACHE.isEdge;
}

/**
 * Logs the current build environment information to the console
 * Useful for debugging environment issues
 */
export function logEnvironmentInfo(): void {
  console.info('Environment Information:');
  console.info(`- NODE_ENV: ${ENV_CACHE.nodeEnv}`);
  console.info(`- Turbopack: ${ENV_CACHE.isTurbo ? 'Yes' : 'No'}`);
  console.info(`- Edge Runtime: ${ENV_CACHE.isEdge ? 'Yes' : 'No'}`);
  console.info(`- Context: ${ENV_CACHE.isServer ? 'Server' : 'Client'}`);
}