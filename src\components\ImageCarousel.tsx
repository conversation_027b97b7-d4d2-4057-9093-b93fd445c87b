'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'

// High-quality Pexels images for event categories
const images = [
  // Sports events
  {
    src: "https://images.pexels.com/photos/3755440/pexels-photo-3755440.jpeg",
    alt: "Basketball tournament with players in action",
    description: "Basketball Tournament",
    category: "Sports"
  },
  {
    src: "https://images.pexels.com/photos/863988/pexels-photo-863988.jpeg",
    alt: "Swimming competition in olympic pool",
    description: "Swimming Competition",
    category: "Sports"
  },
  {
    src: "https://images.pexels.com/photos/46798/the-ball-stadion-football-the-pitch-46798.jpeg",
    alt: "Soccer stadium with crowd",
    description: "Soccer Match",
    category: "Sports"
  },
  // Entertainment events
  {
    src: "https://images.pexels.com/photos/1540406/pexels-photo-1540406.jpeg",
    alt: "Concert with crowd and stage lights",
    description: "Music Concert",
    category: "Entertainment"
  },
  {
    src: "https://images.pexels.com/photos/2747449/pexels-photo-2747449.jpeg",
    alt: "Comedy show audience",
    description: "Comedy Night",
    category: "Entertainment"
  },
  // Wellness events
  {
    src: "https://images.pexels.com/photos/3822906/pexels-photo-3822906.jpeg",
    alt: "Group yoga session in studio",
    description: "Yoga Workshop",
    category: "Wellness"
  },
  {
    src: "https://images.pexels.com/photos/3760607/pexels-photo-3760607.jpeg",
    alt: "Meditation retreat in nature",
    description: "Meditation Retreat",
    category: "Wellness"
  },
  // Business & Education
  {
    src: "https://images.pexels.com/photos/2833037/pexels-photo-2833037.jpeg",
    alt: "Business conference with audience",
    description: "Business Conference",
    category: "Business"
  },
  {
    src: "https://images.pexels.com/photos/7092613/pexels-photo-7092613.jpeg",
    alt: "Financial seminar with presenter",
    description: "Financial Seminar",
    category: "Education"
  },
  // Charity
  {
    src: "https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg",
    alt: "Charity marathon with participants",
    description: "Charity Run",
    category: "Charity"
  }
]

interface ImageCarouselProps {
  overlay?: boolean;
  overlayOpacity?: number;
  interval?: number;
  children?: React.ReactNode;
}

export default function ImageCarousel({
  overlay = true,
  overlayOpacity = 0.5,
  interval = 3800,
  children
}: ImageCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLoaded, setIsLoaded] = useState(false)

  // Advance to next image
  const nextImage = () => {
    const nextIndex = (currentIndex + 1) % images.length
    setCurrentIndex(nextIndex)
  }

  // Go to previous image
  const prevImage = () => {
    const prevIndex = (currentIndex - 1 + images.length) % images.length
    setCurrentIndex(prevIndex)
  }

  // Go to specific image
  const goToImage = (index: number) => {
    setCurrentIndex(index)
  }

  // Set up automatic slideshow
  useEffect(() => {
    const timer = setInterval(nextImage, interval)
    return () => clearInterval(timer)
  }, [interval])

  // Animation variants
  const slideVariants = {
    enter: {
      scale: 1.05,
      opacity: 0,
    },
    center: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 1.2,
        ease: "easeInOut",
      }
    },
    exit: {
      scale: 1.03,
      opacity: 0,
      transition: {
        duration: 1.0,
        ease: "easeInOut",
      }
    }
  }

  // Subtle movement animation for the current image
  const panAnimation = {
    initial: { scale: 1.05 },
    animate: {
      scale: 1.09,
      transition: {
        duration: interval / 1000 * 0.9,
        ease: "easeInOut"
      }
    }
  }

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Images */}
      <AnimatePresence mode="sync" initial={false}>
        <motion.div
          key={currentIndex}
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          className="absolute inset-0 w-full h-full"
        >
          <motion.div
            initial="initial"
            animate="animate"
            variants={panAnimation}
            className="w-full h-full"
          >
            <div className="relative w-full h-full">
              <Image
                src={images[currentIndex]?.src || '/images/fallback/fallback-default.svg'}
                alt={images[currentIndex]?.alt || 'Carousel image'}
                fill
                priority={currentIndex < 2}
                quality={95}
                className="object-cover"
                sizes="100vw"
                onLoad={() => setIsLoaded(true)}
              />

              {/* Image mask for better text readability */}
              <div className="absolute inset-0 mask-linear-from-black mask-linear-to-transparent mask-linear-to-70% mask-linear-at-left"></div>
            </div>
          </motion.div>
        </motion.div>
      </AnimatePresence>

      {/* Overlay for better text contrast */}
      {overlay && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/70 pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: overlayOpacity }}
          transition={{ duration: 0.3 }}
        />
      )}

      {/* Content */}
      <div className="relative z-10 w-full h-full">
        {children}
      </div>

      {/* Loading indicator */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-black">
          <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin" />
        </div>
      )}

      {/* Minimal navigation controls - only visible on hover */}
      <motion.div
        className="absolute inset-x-0 bottom-0 z-10 opacity-0 hover:opacity-100 transition-opacity duration-300"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0 }}
        whileHover={{ opacity: 1 }}
      >
        {/* Semi-transparent background for controls */}
        <div className="bg-gradient-to-t from-black/60 to-transparent h-24 w-full"></div>

        {/* Controls container */}
        <div className="absolute bottom-4 inset-x-0 flex justify-between items-center px-4">
          {/* Image info and category */}
          <div className="flex items-center space-x-2">
            <div className="text-white text-xs font-medium bg-black/60 px-2 py-1 rounded-full">
              {images[currentIndex]?.description || 'Image'}
            </div>
            <div className="text-white text-xs font-medium bg-primary/80 px-3 py-1 rounded-full uppercase tracking-wider">
              {images[currentIndex]?.category || 'General'}
            </div>
          </div>

          {/* Navigation controls */}
          <div className="flex items-center space-x-3">
            {/* Dot indicators */}
            <div className="hidden md:flex space-x-1.5">
              {images.map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => goToImage(index)}
                  className={`w-2 h-2 rounded-full ${index === currentIndex ? 'bg-white' : 'bg-white/40'}`}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  transition={{ duration: 0.2 }}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>

            {/* Counter */}
            <div className="bg-black/50 backdrop-blur-sm text-white rounded-full px-2 py-1 text-xs">
              {currentIndex + 1} / {images.length}
            </div>

            {/* Navigation buttons */}
            <div className="flex space-x-2">
              <motion.button
                onClick={prevImage}
                className="bg-black/50 backdrop-blur-sm text-white rounded-full p-1.5 hover:bg-black/70"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Previous image"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M15 18l-6-6 6-6" />
                </svg>
              </motion.button>
              <motion.button
                onClick={nextImage}
                className="bg-black/50 backdrop-blur-sm text-white rounded-full p-1.5 hover:bg-black/70"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label="Next image"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M9 18l6-6-6-6" />
                </svg>
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}