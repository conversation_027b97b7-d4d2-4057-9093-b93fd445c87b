#!/bin/bash
# setup.sh - One-command development environment setup

# Check for required tools
command -v node >/dev/null 2>&1 || { echo "Node.js is required but not installed. Aborting." >&2; exit 1; }
command -v pnpm >/dev/null 2>&1 || { echo "pnpm is required but not installed. Aborting." >&2; exit 1; }

# Install dependencies
echo "Installing dependencies..."
pnpm install

# Set up environment variables
if [ ! -f .env.local ]; then
  echo "Creating .env.local file..."
  cp .env.example .env.local
  echo "Please update .env.local with your credentials"
fi

# Run initial type check and lint
echo "Running initial type check..."
pnpm type-check

echo "Running initial lint..."
pnpm lint

echo "Development environment setup complete!"
echo "Run 'pnpm dev' to start the development server"
