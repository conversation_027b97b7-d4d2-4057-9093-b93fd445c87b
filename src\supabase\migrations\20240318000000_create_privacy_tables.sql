-- Create privacy_consent_versions table
CREATE TABLE IF NOT EXISTS privacy_consent_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  version VARCHAR(50) NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  consent_text TEXT NOT NULL,
  effective_date TIM<PERSON><PERSON>MP WITH TIME ZONE NOT NULL,
  expiry_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(version, name)
);

-- Create privacy_consents table
CREATE TABLE IF NOT EXISTS privacy_consents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  consent_type VARCHAR(50) NOT NULL,
  consent_given BOOLEAN NOT NULL DEFAULT false,
  consent_version VARCHAR(50) NOT NULL,
  consent_text TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE,
  ip_address VARCHAR(45),
  user_agent TEXT,
  previous_record_id UUID REFERENCES privacy_consents(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_privacy_consents_user_id ON privacy_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_consents_type ON privacy_consents(consent_type);

-- Insert initial consent versions
INSERT INTO privacy_consent_versions (version, name, description, consent_text, effective_date) VALUES
('1.0', 'Marketing', 'Receive marketing communications and updates', 'I agree to receive marketing communications, newsletters, and updates about events and services.', CURRENT_TIMESTAMP),
('1.0', 'Analytics', 'Allow usage data collection for analytics', 'I agree to the collection and processing of my usage data for analytics and service improvement purposes.', CURRENT_TIMESTAMP),
('1.0', 'Third Party', 'Share data with trusted third parties', 'I agree to share my data with trusted third-party partners for service delivery and improvement.', CURRENT_TIMESTAMP),
('1.0', 'Data Sharing', 'Share profile data with event organizers', 'I agree to share my profile information with event organizers for event registration and management.', CURRENT_TIMESTAMP),
('1.0', 'Profile Display', 'Display profile in public listings', 'I agree to display my profile information in public event attendee listings.', CURRENT_TIMESTAMP); 