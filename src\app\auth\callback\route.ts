import { type EmailOtpType } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { isEventRegistrationUrl, getBaseUrl } from '@/utils/url-utilities'
import { logger } from '@/lib/logger'
// Don't import createClient from pages-client, we're using createServerClient directly

// Force dynamic rendering to ensure the callback works properly
export const dynamic = 'force-dynamic'

/**
 * Handle Supabase Auth callback
 * This route is called by Supabase Auth after a user signs in or signs up
 *
 * It handles:
 * 1. OAuth code exchange
 * 2. Email verification with OTP
 * 3. Redirecting the user to their intended destination
 */
export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const token_hash = requestUrl.searchParams.get('token_hash')
  const type = requestUrl.searchParams.get('type') as EmailOtpType | null

  // Log the incoming request for debugging (only in development)
  if (process.env.NODE_ENV === 'development') {
    logger.debug('Auth callback received:', requestUrl.toString())
    logger.debug('Auth callback search params:', Object.fromEntries(requestUrl.searchParams))

    // Check for error parameters
    const error = requestUrl.searchParams.get('error')
    const errorDescription = requestUrl.searchParams.get('error_description')

    if (error) {
      logger.error('Auth callback error:', error)
      logger.error('Auth callback error description:', errorDescription)
    }
  }

  // Store the redirect_to parameter before exchanging the code
  // This is important because the exchange might clear the URL parameters
  let redirectTo = requestUrl.searchParams.get('redirect_to')

  // Check if the redirect_to is pointing to another auth callback (redirect loop)
  if (redirectTo && redirectTo.includes('/auth/callback')) {
    logger.warn('Detected redirect loop in auth callback, redirecting to dashboard instead')
    redirectTo = '/dashboard'
  }

  // Check if this is an event registration URL
  if (redirectTo && isEventRegistrationUrl(redirectTo)) {
    logger.debug('Auth callback detected event registration URL:', redirectTo)
  }

  // Create a response object that we'll modify and return
  const response = NextResponse.redirect(
    new URL(redirectTo || '/dashboard', request.url)
  )

  // Create a Supabase client using the cookies API
  const cookieStore = await cookies()

  // Log cookies for debugging
  if (process.env.NODE_ENV === 'development') {
    const allCookies = await cookieStore.getAll();
    logger.debug('Auth callback cookies before client creation:', allCookies.map(c => c.name));
  }

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        async getAll() {
          return await cookieStore.getAll()
        },
        async setAll(cookiesToSet) {
          // Log cookie operations in development
          if (process.env.NODE_ENV === 'development' && cookiesToSet.length > 0) {
            logger.debug('Auth callback setting cookies:', cookiesToSet.map(c => c.name));
          }

          for (const { name, value, options } of cookiesToSet) {
            await cookieStore.set({ name, value, ...options })
            response.cookies.set({ name, value, ...options })
          }
        },
      },
    }
  )

  // Check for OAuth errors first
  const error = requestUrl.searchParams.get('error')
  const errorCode = requestUrl.searchParams.get('error_code')
  const errorDescription = requestUrl.searchParams.get('error_description')

  if (error) {
    logger.error('OAuth error detected:', error)
    logger.error('OAuth error code:', errorCode || 'No error code')
    logger.error('OAuth error description:', errorDescription || 'No description provided')

    // For bad_oauth_state errors, we'll try to recover by redirecting to sign-in
    // with a special parameter that will trigger a fresh authentication flow
    if (errorCode === 'bad_oauth_state' || (errorDescription && errorDescription.includes('state'))) {
      logger.debug('Detected bad_oauth_state error, redirecting to sign-in with reset_auth=true')
      return NextResponse.redirect(
        new URL(`/sign-in?reset_auth=true&error=${encodeURIComponent(error)}`, request.url)
      )
    }

    // For other errors, redirect to sign-in page with the error message
    return NextResponse.redirect(
      new URL(`/sign-in?error=${encodeURIComponent(error)}&error_description=${encodeURIComponent(errorDescription || '')}`, request.url)
    )
  }

  // Handle email verification with OTP
  if (token_hash && type) {
    try {
      // Verify the OTP
      const { error } = await supabase.auth.verifyOtp({
        type,
        token_hash,
      })

      if (error) {
        logger.error('Error verifying OTP:', error.message)
        // Redirect to sign-in page with error message
        return NextResponse.redirect(
          new URL(`/sign-in?error=${encodeURIComponent(error.message)}`, request.url)
        )
      }

      if (process.env.NODE_ENV === 'development') {
        logger.debug('OTP verification successful')
      }
    } catch (err) {
      logger.error('Exception during OTP verification:', err)
      return NextResponse.redirect(
        new URL('/sign-in?error=Authentication+failed', request.url)
      )
    }
  }
  // Handle OAuth or magic link code exchange
  else if (code) {
    try {
      // Exchange the code for a session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        logger.error('Error exchanging code for session:', error.message)
        // Redirect to sign-in page with error message
        return NextResponse.redirect(
          new URL(`/sign-in?error=${encodeURIComponent(error.message)}`, request.url)
        )
      }

      // Verify that we have a session after the code exchange
      if (!data.session) {
        logger.error('No session created after code exchange')
        return NextResponse.redirect(
          new URL('/sign-in?error=No+session+created', request.url)
        )
      }

      // Log session details for debugging (only in development)
      if (process.env.NODE_ENV === 'development') {
        logger.debug('Code exchange successful')
        logger.debug('Session created with user ID:', data.session.user.id)
        // Check if expires_at exists before using it
        if (data.session.expires_at) {
          logger.debug('Session expires at:', new Date(data.session.expires_at * 1000).toISOString())
        } else {
          logger.debug('Session expiration time not available')
        }

        // Log cookies after code exchange
        const cookiesAfterExchange = await cookieStore.getAll();
        logger.debug('Cookies after code exchange:', cookiesAfterExchange.map(c => c.name));
      }

      // Explicitly set the session in the response cookies
      // This ensures the session is properly established
      for (const cookie of await cookieStore.getAll()) {
        if (cookie.name.startsWith('sb-')) {
          response.cookies.set(cookie);
        }
      }
    } catch (err) {
      logger.error('Exception during auth code exchange:', err)
      return NextResponse.redirect(
        new URL('/sign-in?error=Authentication+failed', request.url)
      )
    }
  } else {
    logger.warn('No code or token_hash parameter in callback URL')
    // Redirect to sign-in page with error message if no auth parameters are present
    return NextResponse.redirect(
      new URL('/sign-in?error=Missing+authentication+parameters', request.url)
    )
  }

  // URL to redirect to after sign in process completes
  // Use the redirect_to parameter if available, otherwise default to dashboard
  const redirectUrl = redirectTo || '/dashboard'

  if (process.env.NODE_ENV === 'development') {
    logger.debug('Redirecting to:', redirectUrl)
  }

  // Get the current environment URL using our robust getBaseUrl function
  const siteUrl = getBaseUrl();
  logger.debug('Current site URL for auth callback:', siteUrl);

  // Check if the redirect URL is for a different environment
  // This prevents redirecting from localhost to production or vice versa
  const currentOrigin = new URL(request.url).origin;
  const redirectUrlObj = redirectUrl.startsWith('http') ? new URL(redirectUrl) : null;

  // If we're in production but the origin in the URL is localhost, fix it
  if (process.env.NODE_ENV === 'production' && redirectUrlObj && redirectUrlObj.hostname === 'localhost') {
    logger.warn(`Detected localhost redirect in production: ${redirectUrlObj.toString()}`);

    // Extract the path and query from the redirect URL
    const pathWithSearch = redirectUrlObj.pathname + redirectUrlObj.search;

    // Create a new URL using the production origin
    const productionUrl = 'https://fuiyoo.netlify.app';
    const fixedUrl = new URL(pathWithSearch, productionUrl).toString();

    logger.debug('Fixed redirect URL to use production origin:', fixedUrl);

    // Update the redirect URL
    return NextResponse.redirect(fixedUrl);
  }

  // For other cross-environment redirects, go to dashboard
  if (redirectUrlObj && redirectUrlObj.origin !== currentOrigin) {
    logger.warn(`Detected cross-environment redirect from ${currentOrigin} to ${redirectUrlObj.origin}`);
    logger.warn('Redirecting to dashboard instead to prevent cross-environment issues');

    // Redirect to the dashboard on the current origin instead
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Double-check that the user is authenticated before redirecting
  // This helps prevent redirect loops and ensures the session is properly established
  const { data: { session }, error: sessionError } = await supabase.auth.getSession()

  if (!session) {
    logger.error('No active session found after authentication')
    // Log additional information for debugging
    const cookiesPresent = await cookieStore.getAll();
    logger.debug('Cookies present:', cookiesPresent.map(c => c.name).join(', '))

    // Try to refresh the session one more time
    try {
      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession()

      if (refreshError || !refreshData.session) {
        logger.error('Failed to refresh session:', refreshError)

        // As a last resort, try to get the user
        const { data: userData } = await supabase.auth.getUser();
        if (userData.user) {
          logger.debug('User exists but no session, attempting to create a new session');

          // Try to create a new session by signing in with the stored token
          try {
            // We'll continue with the redirect and let the middleware handle the session
            logger.debug('Proceeding with redirect despite session issues, middleware will handle');

            // Make sure to copy any auth cookies to the response
            for (const cookie of cookiesPresent) {
              if (cookie.name.startsWith('sb-')) {
                response.cookies.set(cookie);
              }
            }
          } catch (signInErr) {
            logger.error('Failed to create new session:', signInErr);
          }
        } else {
          // Redirect to sign-in with an error message
          return NextResponse.redirect(
            new URL('/sign-in?error=Session+not+established', request.url)
          )
        }
      } else {
        logger.debug('Successfully refreshed session for user:', refreshData.session.user.id)

        // Explicitly set the session cookies in the response
        const refreshedCookies = await cookieStore.getAll();
        for (const cookie of refreshedCookies) {
          if (cookie.name.startsWith('sb-')) {
            response.cookies.set(cookie);
          }
        }
      }
    } catch (refreshErr) {
      logger.error('Exception during session refresh:', refreshErr)
      // Redirect to sign-in with an error message
      return NextResponse.redirect(
        new URL('/sign-in?error=Session+refresh+failed', request.url)
      )
    }
  } else {
    // Session exists, make sure the cookies are in the response
    const sessionCookies = await cookieStore.getAll();
    for (const cookie of sessionCookies) {
      if (cookie.name.startsWith('sb-')) {
        response.cookies.set(cookie);
      }
    }
  }

  // Check again after potential refresh
  const { data: { session: finalSession } } = await supabase.auth.getSession()

  if (process.env.NODE_ENV === 'development') {
    if (finalSession) {
      logger.debug('Verified session exists with user ID:', finalSession.user.id)
      // Check if expires_at exists before using it
      if (finalSession.expires_at) {
        logger.debug('Session expires at:', new Date(finalSession.expires_at * 1000).toISOString())
      } else {
        logger.debug('Session expiration time not available')
      }
    } else {
      logger.warn('Still no session after refresh attempt')
    }

    // Log final cookies
    const finalCookies = await cookieStore.getAll();
    logger.debug('Final cookies in auth callback:', finalCookies.map(c => c.name));
  }

  // Get the current environment URL using our robust getBaseUrl function
  // This is already declared above, so we'll just log the current site URL
  logger.debug('Current site URL for auth callback (before final redirect):', getBaseUrl());

  // Ensure the redirect URL is absolute
  let absoluteRedirectUrl = '';

  if (redirectUrl.startsWith('http')) {
    // Check if the redirect URL is for a different environment
    const redirectUrlObj = new URL(redirectUrl);
    const currentUrlObj = new URL(request.url);

    // If the origins don't match, we need to fix the redirect
    if (redirectUrlObj.origin !== currentUrlObj.origin) {
      logger.warn(`Cross-environment redirect detected: from ${currentUrlObj.origin} to ${redirectUrlObj.origin}`);

      // Extract the path and query from the redirect URL
      const pathWithSearch = redirectUrlObj.pathname + redirectUrlObj.search;

      // Create a new URL using the current origin
      absoluteRedirectUrl = new URL(pathWithSearch, currentUrlObj.origin).toString();
      logger.debug('Fixed redirect URL to use current origin:', absoluteRedirectUrl);
    } else {
      // The URL is for the correct environment, use it as is
      absoluteRedirectUrl = redirectUrl;
    }
  } else {
    // It's a relative URL, make it absolute using the current request origin
    // This ensures we stay in the same environment
    absoluteRedirectUrl = new URL(redirectUrl, new URL(request.url).origin).toString();
    logger.debug('Converted relative URL to absolute using current origin:', absoluteRedirectUrl);
  }

  logger.debug('Final redirect URL:', absoluteRedirectUrl);

  // Add a timestamp to the URL to prevent caching issues
  const separator = absoluteRedirectUrl.includes('?') ? '&' : '?'
  absoluteRedirectUrl = `${absoluteRedirectUrl}${separator}_ts=${Date.now()}`

  // Update the response with the new redirect URL
  response.headers.set('Location', absoluteRedirectUrl)

  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

  // Add cache control headers to prevent caching
  response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
  response.headers.set('Pragma', 'no-cache')
  response.headers.set('Expires', '0')

  return response
}
