-- Create privacy_consents table for GDPR compliance
CREATE TABLE IF NOT EXISTS privacy_consents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL,
  consent_type TEXT NOT NULL,
  consent_given BOOLEAN NOT NULL,
  consent_version TEXT NOT NULL,
  consent_text TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE,
  ip_address TEXT,
  user_agent TEXT,
  previous_record_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_previous_record FOREIGN KEY (previous_record_id) REFERENCES privacy_consents(id) ON DELETE SET NULL
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS privacy_consents_user_id_idx ON privacy_consents(user_id);
CREATE INDEX IF NOT EXISTS privacy_consents_type_idx ON privacy_consents(consent_type);
CREATE INDEX IF NOT EXISTS privacy_consents_version_idx ON privacy_consents(consent_version);

-- Create a table for privacy consent versions
CREATE TABLE IF NOT EXISTS privacy_consent_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  version TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  description TEXT,
  consent_text TEXT NOT NULL,
  effective_date TIMESTAMP WITH TIME ZONE NOT NULL,
  expiry_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add initial consent versions
INSERT INTO privacy_consent_versions (version, name, description, consent_text, effective_date)
VALUES 
  ('1.0', 'Marketing Communications', 'Consent to receive marketing communications', 'I agree to receive marketing communications about events, promotions, and news from the platform.', now()),
  ('1.0', 'Analytics Data Collection', 'Consent to data collection for analytics', 'I agree to the collection and processing of my usage data for analytics purposes to improve the platform.', now()),
  ('1.0', 'Third-Party Data Sharing', 'Consent to share data with third parties', 'I agree to share my data with third parties for event processing and management purposes.', now()),
  ('1.0', 'Data Sharing With Event Organizers', 'Consent to share data with event organizers', 'I agree to share my profile information with event organizers when I register for events.', now()),
  ('1.0', 'Public Profile Display', 'Consent to display profile publicly', 'I agree to have my profile information displayed publicly on the platform.', now());

-- Add comments for documentation
COMMENT ON TABLE privacy_consents IS 'Stores user consent records for GDPR compliance';
COMMENT ON TABLE privacy_consent_versions IS 'Stores available consent versions and their text'; 