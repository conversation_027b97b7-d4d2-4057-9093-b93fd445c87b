-- Migration to update event_categories table to use JSON-based properties
-- This migration preserves existing data by copying legacy fields to the properties column

-- First, ensure the properties column exists (add it if it doesn't)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'event_categories' AND column_name = 'properties'
    ) THEN
        ALTER TABLE event_categories ADD COLUMN properties JSONB DEFAULT '{}'::jsonb;
    END IF;
END
$$;

-- Update the properties column with data from legacy fields for all existing records
UPDATE event_categories
SET properties = jsonb_build_object(
    'price', price,
    'startTime', start_time,
    'earlyBirdPrice', early_bird_price,
    'earlyBirdEndDate', early_bird_end_date,
    'bibPrefix', bib_prefix,
    'bibStartNumber', bib_start_number,
    'bibRequireGeneration', bib_require_generation,
    'registrationOpen', registration_open,
    'registrationCloseDate', registration_close_date,
    'registrationLimit', registration_limit,
    'registrationCount', registration_count
) || COALESCE(properties, '{}'::jsonb)
WHERE properties IS NULL OR NOT properties ? 'registrationCount';

-- Add a comment to the properties column
COMMENT ON COLUMN event_categories.properties IS 'JSON-based properties for the category, including price, registration settings, and event type-specific properties';

-- Add a comment to the capacity column indicating it's deprecated
COMMENT ON COLUMN event_categories.capacity IS 'DEPRECATED: Use properties->registrationLimit instead';

-- Add a comment to the table
COMMENT ON TABLE event_categories IS 'Event categories with JSON-based properties for flexible schema';

-- Create an index on the properties column for better query performance
CREATE INDEX IF NOT EXISTS idx_event_categories_properties ON event_categories USING gin (properties);

-- Add a total_capacity column to the events table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'events' AND column_name = 'total_capacity'
    ) THEN
        ALTER TABLE events ADD COLUMN total_capacity INTEGER;
    END IF;
END
$$;

-- Add registration_close_date column to the events table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'events' AND column_name = 'registration_close_date'
    ) THEN
        ALTER TABLE events ADD COLUMN registration_close_date TIMESTAMP WITH TIME ZONE;
    END IF;
END
$$;

-- Add allow_category_specific_closing_dates column to the events table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'events' AND column_name = 'allow_category_specific_closing_dates'
    ) THEN
        ALTER TABLE events ADD COLUMN allow_category_specific_closing_dates BOOLEAN DEFAULT FALSE;
    END IF;
END
$$;

-- Add tshirt_options column to the events table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'events' AND column_name = 'tshirt_options'
    ) THEN
        ALTER TABLE events ADD COLUMN tshirt_options JSONB DEFAULT '{
            "enabled": false,
            "sizes": ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
            "description": null,
            "sizeChartImage": null
        }'::jsonb;
    END IF;
END
$$;
