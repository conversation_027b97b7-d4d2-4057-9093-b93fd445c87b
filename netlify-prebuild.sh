#!/bin/bash

# This script is run before the build process
echo "Running pre-build script..."

# Detect the current Netlify context (production, staging, etc.)
NETLIFY_CONTEXT=${CONTEXT:-production}
echo "Detected Netlify context: $NETLIFY_CONTEXT"

# Set NODE_ENV based on the Netlify context
if [ "$NETLIFY_CONTEXT" = "production" ]; then
  export NODE_ENV=production
elif [ "$NETLIFY_CONTEXT" = "staging" ] || [ "$NETLIFY_CONTEXT" = "deploy-preview" ]; then
  export NODE_ENV=staging
else
  export NODE_ENV=development
fi
echo "NODE_ENV set to: $NODE_ENV"

# Remove any local .env files to ensure they don't interfere with Netlify variables
echo "Removing local .env files to ensure clean build with Netlify variables..."
rm -f .env.local .env.development.local .env.staging.local .env.production.local

# Create a special file to indicate we're in a Netlify build
# This will be used by Next.js to prioritize Netlify environment variables
echo "Creating .netlify-build marker file..."
echo "NETLIFY_CONTEXT=$NETLIFY_CONTEXT" > .netlify-build

# Log environment information
echo "NETLIFY_SITE_NAME: $SITE_NAME"
echo "NETLIFY_URL: $URL"
echo "NETLIFY_DEPLOY_URL: $DEPLOY_URL"
echo "NETLIFY_DEPLOY_PRIME_URL: $DEPLOY_PRIME_URL"

echo "Pre-build setup completed successfully!"
