# Supabase JavaScript Client Documentation

This document contains key information about using the Supabase JavaScript client in our application.

## Installation

```bash
npm install @supabase/supabase-js
```

## Client Setup

```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

## Authentication

### Sign Up

```typescript
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'example-password',
})
```

### Sign In

```typescript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'example-password',
})
```

### Sign Out

```typescript
const { error } = await supabase.auth.signOut()
```

### Get User

```typescript
const { data: { user } } = await supabase.auth.getUser()
```

### Get Session

```typescript
const { data: { session } } = await supabase.auth.getSession()
```

## Database Operations

### Select Data

```typescript
const { data, error } = await supabase
  .from('table_name')
  .select('*')
```

### Insert Data

```typescript
const { data, error } = await supabase
  .from('table_name')
  .insert([
    { some_column: 'someValue', other_column: 'otherValue' },
  ])
```

### Update Data

```typescript
const { data, error } = await supabase
  .from('table_name')
  .update({ some_column: 'newValue' })
  .eq('id', 1)
```

### Delete Data

```typescript
const { error } = await supabase
  .from('table_name')
  .delete()
  .eq('id', 1)
```

## Storage

### Upload File

```typescript
const { data, error } = await supabase
  .storage
  .from('bucket_name')
  .upload('file_path', file)
```

### Download File

```typescript
const { data, error } = await supabase
  .storage
  .from('bucket_name')
  .download('file_path')
```

### Get Public URL

```typescript
const { data } = supabase
  .storage
  .from('bucket_name')
  .getPublicUrl('file_path')
```

## Realtime

### Subscribe to Changes

```typescript
const channel = supabase
  .channel('table_db_changes')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'table_name',
    },
    (payload) => {
      console.log('Change received!', payload)
    }
  )
  .subscribe()
```

### Unsubscribe

```typescript
supabase.removeChannel(channel)
```

## Error Handling

Always check for errors when making Supabase calls:

```typescript
const { data, error } = await supabase.from('table_name').select('*')

if (error) {
  console.error('Error fetching data:', error)
  return
}

// Process data
console.log('Data:', data)
```

## TypeScript Support

Define types for your database tables:

```typescript
type User = {
  id: string
  email: string
  name: string
  created_at: string
}

const { data, error } = await supabase
  .from<User>('users')
  .select('*')
```

## References

- [Supabase JavaScript Client Documentation](https://supabase.com/docs/reference/javascript/introduction)
- [Supabase Auth Documentation](https://supabase.com/docs/reference/javascript/auth-signup)
- [Supabase Database Documentation](https://supabase.com/docs/reference/javascript/select)
- [Supabase Storage Documentation](https://supabase.com/docs/reference/javascript/storage-createbucket)
- [Supabase Realtime Documentation](https://supabase.com/docs/reference/javascript/subscribe)
