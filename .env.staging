# Staging Environment Variables
# Note: For security, it's recommended to set these values in Netlify environment variables
# rather than storing them in this file, especially for sensitive credentials.
NEXT_PUBLIC_SITE_URL=https://staging--fuiyoo.netlify.app
NEXT_PUBLIC_DEV_URL=https://staging--fuiyoo.netlify.app
NEXT_PUBLIC_DEV_API_URL=https://staging--fuiyoo.netlify.app/api

# Authentication URLs
NEXT_PUBLIC_AUTH_REDIRECT_URL=https://staging--fuiyoo.netlify.app/auth/callback

# Supabase Configuration
# Note: These should be the same as production unless you have a separate staging project
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Authentication
NEXT_PUBLIC_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_AFTER_SIGN_UP_URL=/dashboard

# Build Configuration
NODE_OPTIONS=--max-old-space-size=8192
NEXT_TELEMETRY_DISABLED=1
