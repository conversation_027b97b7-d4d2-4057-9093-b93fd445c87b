# Event Ticketing System Data Model

## Core Tables

### User Profile & Authentication

```typescript
interface User {
  id: string;
  email?: string;
  phone?: string;
  authMethods: AuthMethod[];
  isVerified: boolean;
  lastLoginAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface AuthMethod {
  type: 'password' | 'google' | 'apple' | 'phone';
  identifier: string; // email, phone, or provider-specific ID
  verified: boolean;
  verifiedAt?: Date;
  metadata?: Record<string, any>; // Provider-specific data
}

interface UserProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: string;
  nationality?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  tShirtSize?: string;
  runningPace?: number; // Minutes per km/mile
  preferredLanguage: string;
  customFields: Record<string, any>; // Additional profile fields
  createdAt: Date;
  updatedAt: Date;
  lastCompletedAt?: Date; // When profile was last fully completed
}

interface SavedContact {
  id: string;
  userId: string; // Owner of this contact
  relationship: string; // e.g., "Friend", "Family", "Spouse"
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: string;
  tShirtSize?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

interface UserPreferences {
  id: string;
  userId: string;
  notificationPreferences: {
    email: boolean;
    sms: boolean;
    push: boolean;
    eventReminders: boolean;
    marketingCommunications: boolean;
    resultNotifications: boolean;
  };
  privacyPreferences: {
    shareResultsPublicly: boolean;
    displayInEventPhotos: boolean;
    allowSearchByName: boolean;
  };
  uiPreferences: {
    theme: 'light' | 'dark' | 'system';
    dashboardLayout: string;
    defaultView: string;
  };
  updatedAt: Date;
}

interface ConsentRecord {
  id: string;
  userId: string;
  type: 'privacy_policy' | 'terms_of_service' | 'marketing' | 'data_sharing';
  version: string;
  consentedAt: Date;
  ipAddress?: string;
  userAgent?: string;
}
```

### Events

```typescript
interface Event {
  id: string;
  organizerId: string;
  title: string;
  description: string;
  eventType: EventType; // 'workshop', 'concert', 'run', etc.
  startDate: Date;
  endDate: Date;
  location: {
    name: string;
    address: string;
    coordinates?: { lat: number; lng: number };
    venueDetails?: Record<string, any>;
  };
  timeZone: string;
  capacity: number;
  isPublished: boolean;
  hasWaitlist: boolean;
  requiresIdentification: boolean;
  requiresEmergencyContact: boolean; // Whether emergency contact is required
  configuration: EventConfiguration; // Type-specific config
  createdAt: Date;
  updatedAt: Date;
}

interface EventConfiguration {
  // Common fields
  registrationStart: Date;
  registrationEnd: Date;
  
  // Emergency contact configuration
  emergencyContactConfig: {
    isRequired: boolean;
    nameRequired: boolean;
    phoneRequired: boolean;
    relationshipRequired: boolean;
    showOnTicket: boolean;
    additionalFields: string[]; // Any custom emergency contact fields
  };
  
  // Type-specific fields (union type)
  details: WorkshopConfig | ConcertConfig | RunningEventConfig;
}

// Running event specific configuration
interface RunningEventConfig {
  raceCategories: RaceCategory[];
  bibConfiguration: BibConfiguration;
  merchandiseOptions: MerchandiseOption[];
  collectionPoints: CollectionPoint[];
  waves: Wave[];
  ageGroups: AgeGroup[];
  additionalFields: CustomField[];
}

interface RaceCategory {
  id: string;
  name: string; // e.g., "5K", "10K", "Half Marathon"
  description: string;
  startTime: Date;
  capacity: number;
  price: number;
  bibPrefix?: string; // For bib number generation
  bibNumberStart: number;
  ageRestrictions?: {
    minAge?: number;
    maxAge?: number;
  };
  requiredFields: string[]; // IDs of required fields
  merchandiseInclusions: string[]; // IDs of included merchandise
  requiresEmergencyContact: boolean; // Override event-level setting
}

interface BibConfiguration {
  generateAutomatically: boolean;
  pattern: string; // e.g., "CAT-{number}" or "{category}-{number}"
  startNumber: number; // Default starting number
  reservedNumbers: number[]; // Numbers to exclude
  assignmentRules: {
    byRegistrationDate: boolean;
    byCategory: boolean;
    byCustomField: string; // Field ID to sort by
  };
}

interface CustomField {
  id: string;
  eventId: string;
  name: string;
  type: 'text' | 'number' | 'select' | 'checkbox' | 'date';
  label: string;
  placeholder?: string;
  isRequired: boolean;
  options?: string[]; // For select fields
  validation?: string; // Regex or validation rules
  categoryIds?: string[]; // Categories where this field applies
  profileFieldMapping?: string; // Corresponding field in user profile
}
```

### Tickets & Registrations

```typescript
interface Order {
  id: string;
  userId: string;
  eventId: string;
  tickets: Ticket[];
  totalAmount: number;
  status: 'reserved' | 'confirmed' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed';
  createdAt: Date;
  expiresAt: Date; // For reservation expiration
}

interface Ticket {
  id: string;
  orderId: string;
  ticketTypeId: string;
  eventId: string;
  categoryId?: string; // For running events
  price: number;
  attendee: Attendee;
  ticketCode: string; // For QR code generation
  isCheckedIn: boolean;
  checkInTime?: Date;
  checkInLocationId?: string;
  savedFromProfileId?: string; // If data was populated from a profile
  savedContactId?: string; // If for a saved contact
}

interface Attendee {
  name: string;
  email: string;
  phone?: string;
  identificationNumber?: string;
  dateOfBirth?: Date;
  gender?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
  };
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
    customFields?: Record<string, any>; // For any additional fields
  };
  customFields: Record<string, any>; // Dynamic fields
}

interface RegistrationUpdate {
  id: string;
  registrationId: string;
  updatedBy: string;
  updatedAt: Date;
  previousData: Partial<Attendee>;
  newData: Partial<Attendee>;
  reason?: string;
}
```

## Running Event Specific Tables

### Bib Assignments

```typescript
interface BibAssignment {
  id: string;
  eventId: string;
  registrationId: string;
  ticketId: string;
  categoryId: string;
  bibNumber: string; // Could be alphanumeric
  assignedAt: Date;
  status: 'assigned' | 'printed' | 'collected';
  waveId?: string;
  corralId?: string;
}
```

### Merchandise & T-Shirts

```typescript
interface MerchandiseOption {
  id: string;
  eventId: string;
  name: string; // e.g., "Event T-Shirt", "Race Medal"
  description: string;
  isMandatory: boolean;
  price: number; // 0 if included
  variants: MerchandiseVariant[];
}

interface MerchandiseVariant {
  id: string;
  optionId: string;
  name: string; // e.g., "Size", "Color"
  values: string[]; // e.g., ["S", "M", "L"] or ["Red", "Blue"]
  inventory: Record<string, number>; // Inventory by value
}

interface RegistrationMerchandise {
  id: string;
  registrationId: string;
  merchandiseOptionId: string;
  selections: Record<string, string>; // e.g., {"Size": "M", "Color": "Blue"}
  status: 'selected' | 'allocated' | 'collected';
}
```

### Check-In System

```typescript
interface CheckInLocation {
  id: string;
  eventId: string;
  name: string; // e.g., "Main Entrance", "VIP Gate"
  deviceIds: string[]; // Devices authorized to check in at this location
}

interface CheckInDevice {
  id: string;
  deviceName: string;
  deviceId: string; // Unique device identifier
  lastSync: Date;
  isOnline: boolean;
}

interface CheckInRecord {
  id: string;
  ticketId: string;
  eventId: string;
  locationId: string;
  deviceId: string;
  operatorId: string; // Staff member who performed check-in
  timestamp: Date;
  status: 'successful' | 'duplicate' | 'invalid';
  notes?: string;
}
```

### Participant History

```typescript
interface ParticipantHistory {
  id: string;
  userId: string;
  eventId: string;
  categoryId: string;
  registrationId: string;
  bibNumber?: string;
  finishTime?: number; // In seconds
  placing?: {
    overall?: number;
    gender?: number;
    ageGroup?: number;
  };
  checkpoints?: Array<{
    name: string;
    distance: number; // In meters
    timestamp: Date;
    splitTime: number; // In seconds
  }>;
  createdAt: Date;
}
```

### Supplier Reports

```typescript
interface SupplierReport {
  id: string;
  eventId: string;
  type: 'tshirt' | 'bib' | 'medal' | 'racekit';
  generatedAt: Date;
  generatedBy: string;
  data: any; // Report-specific data
  fileUrl?: string; // Generated file
}

interface TShirtReport {
  totalCount: number;
  sizeBreakdown: Record<string, number>; // e.g., {"S": 25, "M": 50}
  genderBreakdown?: Record<string, Record<string, number>>; // e.g., {"Men's": {"S": 15}, "Women's": {"S": 10}}
  designBreakdown?: Record<string, number>; // If multiple designs
  categoryBreakdown?: Record<string, Record<string, number>>; // Sizes by race category
}

interface BibReport {
  totalCount: number;
  categoryBreakdown: Record<string, {
    count: number;
    startNumber: number;
    endNumber: number;
    prefix?: string;
  }>;
  specialBibs: Array<{
    bibNumber: string;
    reason: string;
  }>;
}
```

### Race Day Management

```typescript
interface Wave {
  id: string;
  eventId: string;
  categoryId: string;
  name: string; // e.g., "Wave 1", "Elite Wave"
  startTime: Date;
  capacity: number;
  corrals: Corral[];
}

interface Corral {
  id: string;
  waveId: string;
  name: string; // e.g., "A", "B", "Elite"
  capacity: number;
  estimatedPaceMin?: number; // In minutes per mile/km
  estimatedPaceMax?: number;
}

interface AgeGroup {
  id: string;
  eventId: string;
  categoryId: string;
  name: string; // e.g., "18-29", "30-39"
  minAge: number;
  maxAge: number;
  genderSpecific: boolean;
  gender?: 'male' | 'female';
}
```

### Additional Features

```typescript
interface CollectionPoint {
  id: string;
  eventId: string;
  name: string;
  address: string;
  coordinates?: { lat: number; lng: number };
  openingTimes: Array<{
    date: Date;
    startTime: string; // e.g., "09:00"
    endTime: string; // e.g., "17:00"
  }>;
  items: string[]; // What can be collected here ("bib", "tshirt", "racekit")
}
```

### Emergency Contact Access Logs

```typescript
interface EmergencyContactAccessLog {
  id: string;
  eventId: string;
  registrationId: string;
  accessedBy: string; // User ID who accessed the data
  accessedAt: Date;
  reason: string; // Why the emergency contact was accessed
  ipAddress: string;
  userAgent?: string;
}
```

## Database Relationships

- A User has one UserProfile
- A User has many SavedContacts
- A User has one UserPreferences
- A User has many ConsentRecords
- A User has many Orders
- An Event has many TicketTypes
- An Event has many Registrations
- An Event has many CheckInLocations
- A RaceCategory belongs to an Event
- A Registration has one BibAssignment
- A Registration has many RegistrationMerchandise
- A Registration has many RegistrationUpdates
- A CheckInRecord belongs to a Ticket
- A Wave has many Corrals
- A BibAssignment belongs to a Registration and a RaceCategory
- A SupplierReport belongs to an Event
- A CustomField can map to a UserProfile field
- A Ticket can reference a UserProfile or SavedContact
- A Registration has many EmergencyContactAccessLogs 