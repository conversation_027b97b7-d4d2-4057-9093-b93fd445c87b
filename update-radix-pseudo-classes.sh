#!/bin/bash

# Update Radix UI custom state pseudo classes from :--radix- to :state(radix-)
echo "Updating Radix UI custom state pseudo classes..."

# Find all files containing --radix- and replace with --state-
find ./src -type f -name "*.tsx" -o -name "*.jsx" -o -name "*.ts" -o -name "*.js" -o -name "*.css" | xargs grep -l "\-\-radix\-" | xargs sed -i '' 's/--radix-/--state-/g'

# Find all files containing var(--radix- and replace with var(--state-
find ./src -type f -name "*.tsx" -o -name "*.jsx" -o -name "*.ts" -o -name "*.js" -o -name "*.css" | xargs grep -l "var(--radix-" | xargs sed -i '' 's/var(--radix-/var(--state-/g'

echo "Update complete!"
