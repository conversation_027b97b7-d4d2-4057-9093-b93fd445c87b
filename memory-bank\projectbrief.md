# Project Brief: Fuiyoo Events Platform

## Overview
Fuiyoo is an event discovery and registration platform designed for Malaysian events. It allows users to browse, search, and filter events across various categories and locations in Malaysia.

## Core Requirements
- Event discovery with search, filtering and categorization
- Responsive design for mobile and desktop users
- Support for various event categories (Sports, Wellness, Charity, Finance, Entertainment)
- Location-based filtering by Malaysian states
- Modern UI with Tailwind CSS and shadcn/ui components

## Technical Stack
- Next.js 15.3.0 with App Router
- TypeScript for type safety
- Tailwind CSS for styling
- shadcn/ui components built on Radix UI primitives
- Clerk for authentication
- Supabase for database
- Deployed with an edge-first approach

## Goals
- Create a seamless event discovery experience
- Allow users to find relevant events based on interests and location
- Provide detailed event pages with comprehensive information
- Support event organizers with robust event management features
- Build with edge-first principles for global performance 