import { createAdminClient } from '@/lib/supabase/admin-client';
import { SchemaManager } from '@/lib/db/schema-manager';
import { asAny } from '@/lib/supabase/extended-types';
import fs from 'fs';
import path from 'path';

/**
 * Apply event model migrations to the database
 */
async function applyEventModelMigrations() {
  console.log('Applying event model migrations...');

  try {
    const supabase = await createAdminClient();
    const supabaseAny = asAny(supabase);

    // Get the migration directory
    const migrationsDir = path.join(process.cwd(), 'src', 'db', 'migrations');

    // List of migrations to apply in order
    const migrationFiles = [
      '0013_add_event_types.sql',
      '0014_add_running_event_categories.sql',
      '0015_add_registration_fields.sql'
    ];

    // Apply each migration
    for (const filename of migrationFiles) {
      console.log(`Applying migration: ${filename}`);

      // Read the migration file
      const filePath = path.join(migrationsDir, filename);
      const sql = fs.readFileSync(filePath, 'utf8');

      // Execute the migration
      const { error } = await supabaseAny.rpc('exec_sql', { sql });

      if (error) {
        console.error(`Error applying migration ${filename}:`, error);
        return;
      }

      console.log(`Successfully applied migration: ${filename}`);
    }

    // Refresh the schema cache
    await SchemaManager.refreshSchemaCache();
    console.log('Schema cache refreshed');

    console.log('All event model migrations applied successfully!');
  } catch (error) {
    console.error('Error applying migrations:', error);
  }
}

// Run the migration
applyEventModelMigrations()
  .then(() => {
    console.log('Migration complete');
    process.exit(0);
  })
  .catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });