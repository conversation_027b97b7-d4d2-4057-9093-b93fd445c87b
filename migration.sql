-- Add auth_user_id column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'auth_user_id'
    ) THEN
        ALTER TABLE public.users ADD COLUMN auth_user_id UUID REFERENCES auth.users(id);
    END IF;
END
$$;

-- Update existing users with auth_user_id based on email matching
UPDATE public.users
SET auth_user_id = auth.users.id
FROM auth.users
WHERE public.users.email = auth.users.email
AND public.users.auth_user_id IS NULL;

-- Create an index on auth_user_id for better performance
CREATE INDEX IF NOT EXISTS idx_users_auth_user_id ON public.users(auth_user_id);

-- Add a comment to the column for documentation
COMMENT ON COLUMN public.users.auth_user_id IS 'References the auth.users table to link authentication with application users';
