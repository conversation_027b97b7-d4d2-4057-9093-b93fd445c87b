{"name": "fuiyoo", "version": "0.1.0", "private": true, "engines": {"node": ">=22.0.0", "pnpm": ">=8.0.0"}, "scripts": {"dev": "next dev --turbo", "dev:turbo": "next dev --turbo", "dev:standard": "next dev", "dev:turbo-max": "NODE_OPTIONS='--max-old-space-size=4096 --enable-source-maps' next dev --turbo", "dev:turbo-debug": "NEXT_TURBOPACK_TRACING=1 next dev --turbo", "dev:turbo-safe": "./dev.sh --turbo --safe", "prebuild:disabled": "eslint .", "build": "NODE_OPTIONS='--max-old-space-size=4096 --enable-source-maps' next build", "build:prod": "next build", "build:turbo": "NODE_OPTIONS='--max-old-space-size=4096 --enable-source-maps' next build --turbo", "build:force": "next build --force", "build:safe": "NEXT_SKIP_PREFLIGHT_CHECK=true next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "db:migrate": "tsx src/scripts/migrate.ts up", "db:create-migration": "tsx src/scripts/migrate.ts create", "db:status": "tsx src/scripts/migrate.ts status", "db:refresh-schema": "tsx src/scripts/migrate.ts refresh", "clean": "rm -rf .next && rm -rf node_modules/.cache", "type-check": "tsc --noEmit", "lint:fix": "next lint --fix", "validate": "pnpm run lint && pnpm run type-check", "prepare": "husky"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/postcss": "^4.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.12.2", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "netlify-cli": "^21.5.0", "next": "15.3.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-select": "^5.10.1", "react-type-animation": "^3.2.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "uuid": "^11.1.0", "zod": "^3.25.28", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@netlify/plugin-nextjs": "^5.11.2", "@next/bundle-analyzer": "^15.3.2", "@next/eslint-plugin-next": "^15.3.2", "@svgr/webpack": "^8.1.0", "@types/lodash": "^4.17.17", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^9.27.0", "eslint-config-next": "15.1.8", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "pg": "^8.16.0", "postcss": "^8.5.3", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3"}}