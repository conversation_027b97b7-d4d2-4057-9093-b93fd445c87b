# Migrating from Clerk to <PERSON><PERSON>base Auth

This document outlines the process for migrating authentication from Clerk to <PERSON><PERSON><PERSON> Auth in the Fuiyoo platform.

## Overview

The migration involves the following steps:

1. Setting up Supabase Auth
2. Updating database schema
3. Migrating users from Clerk to Supabase Auth
4. Updating application code to use Supabase Auth
5. Testing the migration
6. Deploying the changes

## Prerequisites

- Supabase project with database access
- Clerk account with API access
- Node.js and npm installed
- Access to the application codebase

## Environment Variables

Ensure the following environment variables are set:

```
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Clerk (needed for migration only)
CLERK_SECRET_KEY=your-clerk-secret-key
```

## Migration Steps

### 1. Setting Up Supabase Auth

1. Log in to your Supabase dashboard
2. Navigate to Authentication > Settings
3. Configure the following settings:
   - Enable Email/Password sign-in
   - Configure email templates for verification, password reset, etc.
   - Set up security settings (session duration, MFA options)
   - Configure redirect URLs for authentication flows

### 2. Database Schema Updates

The migration script will apply the necessary database schema changes, including:

- Adding `auth_user_id` column to the `users` table
- Creating foreign key constraints
- Setting up Row Level Security (RLS) policies

### 3. Migrating Users

Run the migration script to transfer users from Clerk to Supabase Auth:

```bash
# Install dependencies
npm install @supabase/supabase-js @clerk/clerk-sdk-node dotenv

# Run the migration script
node scripts/migrate-auth.js
```

The script will:
- Create Supabase Auth users for each Clerk user
- Link existing user records with Supabase Auth users
- Preserve user roles and permissions
- Generate temporary passwords for users

### 4. Testing the Migration

After migration, test the following functionality:

- Sign in with existing accounts
- Password reset flow
- User registration
- Role-based access control
- Protected routes and API endpoints

### 5. Post-Migration Tasks

1. Notify users about the authentication change
2. Provide instructions for resetting passwords
3. Monitor for any authentication issues
4. Update documentation

## Rollback Plan

If issues are encountered during migration:

1. Revert code changes to use Clerk authentication
2. Keep the Supabase Auth users for future migration attempts
3. Notify users of the rollback

## Support

For assistance with the migration process, contact the development team.

## References

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Next.js Auth Helpers](https://supabase.com/docs/guides/auth/auth-helpers/nextjs)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
