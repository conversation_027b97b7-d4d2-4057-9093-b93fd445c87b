import { createClient } from '@/lib/supabase/pages-client';
import { PostgrestSingleResponse } from '@supabase/supabase-js';

/**
 * Base repository for database operations
 */
export class BaseRepository<T extends Record<string, any>> {
  protected tableName: string;

  /**
   * Create a new repository
   * @param tableName Database table name
   */
  constructor(tableName: string) {
    this.tableName = tableName;
  }

  /**
   * Get the Supabase client
   */
  protected async getClient() {
    return await createClient();
  }

  /**
   * Find a record by ID
   */
  async findById(id: string): Promise<T | null> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name
      const { data, error } = await supabase
        .from(this.tableName as any)
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error(`Error fetching ${this.tableName} by ID:`, error);
        return null;
      }

      return data as unknown as T;
    } catch (error) {
      console.error(`Error in findById for ${this.tableName}:`, error);
      return null;
    }
  }

  /**
   * Find records by a query
   */
  async find(query: Record<string, any> = {}): Promise<T[]> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name
      let queryBuilder = supabase.from(this.tableName as any).select('*');

      // Apply filters
      Object.entries(query).forEach(([key, value]) => {
        queryBuilder = queryBuilder.eq(key, value);
      });

      const { data, error } = await queryBuilder;

      if (error) {
        console.error(`Error fetching ${this.tableName}:`, error);
        return [];
      }

      return data as unknown as T[];
    } catch (error) {
      console.error(`Error in find for ${this.tableName}:`, error);
      return [];
    }
  }

  /**
   * Create a new record
   */
  async create(data: Partial<T>): Promise<T | null> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name and data
      const { data: result, error } = await supabase
        .from(this.tableName as any)
        .insert(data as any)
        .select()
        .single();

      if (error) {
        console.error(`Error creating ${this.tableName}:`, error);
        return null;
      }

      return result as unknown as T;
    } catch (error) {
      console.error(`Error in create for ${this.tableName}:`, error);
      return null;
    }
  }

  /**
   * Update a record
   */
  async update(id: string, data: Partial<T>): Promise<T | null> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name and data
      const { data: result, error } = await supabase
        .from(this.tableName as any)
        .update(data as any)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error(`Error updating ${this.tableName}:`, error);
        return null;
      }

      return result as unknown as T;
    } catch (error) {
      console.error(`Error in update for ${this.tableName}:`, error);
      return null;
    }
  }

  /**
   * Delete a record
   */
  async delete(id: string): Promise<boolean> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name
      const { error } = await supabase
        .from(this.tableName as any)
        .delete()
        .eq('id', id);

      if (error) {
        console.error(`Error deleting ${this.tableName}:`, error);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`Error in delete for ${this.tableName}:`, error);
      return false;
    }
  }
}
