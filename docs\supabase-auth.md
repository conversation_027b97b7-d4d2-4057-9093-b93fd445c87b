# Supabase Auth Documentation

## Authentication Methods

### Email and Password

```javascript
// Sign up with email and password
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'sup3rs3cur3',
  options: {
    emailRedirectTo: 'http://localhost:3000/auth/callback',
  },
})

// Sign in with email and password
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password',
})
```

### OAuth Providers

```javascript
// Sign in with OAuth provider (e.g., Google, GitHub, etc.)
const { data, error } = await supabase.auth.signInWithOAuth({
  provider: 'google', // or 'github', 'facebook', etc.
  options: {
    redirectTo: 'http://localhost:3000/auth/callback',
  },
})
```

### Anonymous Authentication

```javascript
// Sign in anonymously
const { data, error } = await supabase.auth.signInAnonymously()

// Check for existing session and sign in anonymously if none exists
const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

let userId: string | null = null

if (sessionData.session) {
  userId = sessionData.session?.user.id ?? null
} else {
  const { data, error } = await supabase.auth.signInAnonymously()
  userId = data?.user?.id ?? null
}
```

## Session Management

### Getting the Current Session

```javascript
// Get the current session
const { data: { session } } = await supabase.auth.getSession()

// Get the current user
const { data: { user } } = await supabase.auth.getUser()
```

> **Important**: `auth.getSession` reads the auth token and the unencoded session data from the local storage medium. It doesn't send a request back to the Supabase Auth server unless the local session is expired.
>
> You should **never** trust the unencoded session data if you're writing server code, since it could be tampered with by the sender. If you need verified, trustworthy user data, call `auth.getUser` instead, which always makes a request to the Auth server to fetch trusted data.

### Listening for Auth Changes

```javascript
// Listen for auth state changes
const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'SIGNED_IN') {
    // Handle sign in event
  } else if (event === 'SIGNED_OUT') {
    // Handle sign out event
  }
})

// Unsubscribe when no longer needed
subscription.unsubscribe()
```

### Sign Out

```javascript
// Sign out
const { error } = await supabase.auth.signOut()
```

## Server-Side Authentication

### Next.js Middleware

```typescript
import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // IMPORTANT: DO NOT REMOVE auth.getUser()
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (
    !user &&
    !request.nextUrl.pathname.startsWith('/login') &&
    !request.nextUrl.pathname.startsWith('/auth')
  ) {
    // no user, potentially respond by redirecting the user to the login page
    const url = request.nextUrl.clone()
    url.pathname = '/login'
    return NextResponse.redirect(url)
  }

  return supabaseResponse
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

### Auth Callback Route

```typescript
import { redirect } from '@remix-run/node'
import { createServerClient } from '@supabase/auth-helpers-remix'

import type { Database } from 'db_types'
import type { LoaderFunctionArgs } from '@remix-run/node'

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const response = new Response()
  const url = new URL(request.url)
  const code = url.searchParams.get('code')

  if (code) {
    const supabaseClient = createServerClient<Database>(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      { request, response }
    )
    await supabaseClient.auth.exchangeCodeForSession(code)
  }

  return redirect('/', {
    headers: response.headers,
  })
}
```

## Advanced Features

### Multi-Factor Authentication (MFA)

```typescript
// Send an SMS or WhatsApp message to the user
const { data: { challengeId } } = await supabase.auth.mfa.challenge({
  factorId,
})

// To verify the code received by the user
await supabase.auth.mfa.verify({
  factorId,
  challengeId,
  code: '123456',
})

// The user's `aal` claim in the JWT 
// will be upgraded to aal2
```

### Identity Linking

```javascript
// Link an OAuth identity to a user's account
const { data, error } = await supabase.auth.linkIdentity({
  provider: 'google',
})

// Retrieve all identities linked to a user
const {
  data: { identities },
} = await supabase.auth.getUserIdentities()

// Find the google identity linked to the user
const googleIdentity = identities.find(({ provider }) => provider === 'google')

// Unlink the google identity from the user
const { data, error } = await supabase.auth.unlinkIdentity(googleIdentity)
```

### Custom Access Token Hook

```sql
create function custom_access_token_hook(event jsonb)
returns jsonb
language plpgsql
as $$
declare
  user_level jsonb;
begin
  -- fetch the current user's level
  select
    to_jsonb(level) into user_level
  from profiles
  where
    user_id = event->>'user_id'::uuid;

  -- change the event.claims.level
  return jsonb_set(
		event,
		'{claims,level}',
		user_level);

end;
$$

-- Grant execute permission to auth admin role
grant execute
  on function public.custom_access_token_hook
  to supabase_auth_admin;
```

## Third-Party Auth Integration

### Firebase Auth

```typescript
// Web
import { createClient } from '@supabase/supabase-js'

const supabase = createClient('https://<supabase-project>.supabase.co', 'SUPABASE_ANON_KEY', {
  accessToken: async () => {
    return (await firebase.auth().currentUser?.getIdToken(/* forceRefresh */ false)) ?? null
  },
})

// Flutter
await Supabase.initialize(
  url: supabaseUrl,
  anonKey: supabaseKey,
  debug: false,
  accessToken: () async {
    final token = await FirebaseAuth.instance.currentUser?.getIdToken();
    return token;
  },
);

// iOS (Swift)
let supabase = SupabaseClient(
  supabaseURL: URL(string: "https://<supabase-project>.supabase.co")!,
  supabaseKey: "SUPABASE_ANON_KEY",
  options: SupabaseClientOptions(
    auth: SupabaseClientOptions.AuthOptions(
      accessToken: {
        guard let token = await Auth.auth().currentUser?.getIDToken() else {
          throw MissingFirebaseTokenError()
        }

        return token
      }
    )
  )
)

// Android (Kotlin)
val supabase = createSupabaseClient(
    "https://<supabase-project>.supabase.co",
    "SUPABASE_ANON_KEY"
) {
    accessToken = {
        Firebase.auth.currentUser?.getIdToken(false)?.await()?.token
    }
}
```

### Auth0

```typescript
import { createClient } from '@supabase/supabase-js'
import Auth0Client from '@auth0/auth0-spa-js'

const auth0 = new Auth0Client({
  domain: '<AUTH0_DOMAIN>',
  clientId: '<AUTH0_CLIENT_ID>',
  authorizationParams: {
    redirect_uri: '<MY_CALLBACK_URL>',
  },
})

const supabase = createClient('https://<supabase-project>.supabase.co', 'SUPABASE_ANON_KEY', {
  accessToken: async () => {
    const accessToken = await auth0.getTokenSilently()

    // Alternatively you can use (await auth0.getIdTokenClaims()).__raw to
    // use an ID token instead.

    return accessToken
  },
})
```

## UI Components

### Auth UI

```javascript
// Install dependencies
// npm install @supabase/supabase-js @supabase/auth-ui-react @supabase/auth-ui-shared

import { createClient } from '@supabase/supabase-js'
import { Auth } from '@supabase/auth-ui-react'
import { ThemeSupa } from '@supabase/auth-ui-shared'

const supabase = createClient('<INSERT PROJECT URL>', '<INSERT PROJECT ANON KEY>')

// Basic implementation
const App = () => <Auth supabaseClient={supabase} />

// With theme and social providers
const App = () => (
  <Auth
    supabaseClient={supabase}
    appearance={{ theme: ThemeSupa }}
    providers={['google', 'facebook', 'twitter']}
  />
)
```

### React Implementation

```javascript
import './index.css'
import { useState, useEffect } from 'react'
import { createClient } from '@supabase/supabase-js'
import { Auth } from '@supabase/auth-ui-react'
import { ThemeSupa } from '@supabase/auth-ui-shared'

const supabase = createClient('https://<project>.supabase.co', '<your-anon-key>')

export default function App() {
  const [session, setSession] = useState(null)

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
    })

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session)
    })

    return () => subscription.unsubscribe()
  }, [])

  if (!session) {
    return (<Auth supabaseClient={supabase} appearance={{ theme: ThemeSupa }} />)
  }
  else {
    return (<div>Logged in!</div>)
  }
}
```
