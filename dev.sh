#!/bin/bash

# This script provides a unified way to start the Next.js development server
# with different options (standard webpack or Turbopack)

# Kill any existing Next.js dev servers
lsof -ti:3000 | xargs kill -9 2>/dev/null || true

# Set common environment variables
export NODE_OPTIONS="--max-old-space-size=4096 --enable-source-maps"

# Parse command line options
USE_TURBO=false
SAFE_MODE=false
AUTO_FALLBACK=true  # Default to auto fallback

while [[ "$#" -gt 0 ]]; do
  case $1 in
    --turbo) USE_TURBO=true ;;
    --safe) SAFE_MODE=true ;;
    --no-fallback) AUTO_FALLBACK=false ;;
    *) echo "Unknown parameter: $1"; exit 1 ;;
  esac
  shift
done

if [ "$USE_TURBO" = true ]; then
  export NEXT_TURBO=1
  echo "Starting Next.js with Turbopack..."

  if [ "$SAFE_MODE" = true ] || [ "$AUTO_FALLBACK" = true ]; then
    if [ "$SAFE_MODE" = true ]; then
      echo "Safe mode enabled: will fall back to standard webpack if Turbopack fails"
    else
      echo "Auto-fallback enabled: will fall back to standard webpack if Turbopack fails"
    fi
    echo "API calls will use fallback data if external APIs are unreachable"
    echo ""

    # Try to start with Turbopack and allow it to be killed if it takes too long
    pnpm dev:turbo &
    TURBO_PID=$!

    # Wait up to 30 seconds for Turbopack to start
    COUNTER=0
    while [ $COUNTER -lt 30 ]; do
      if ! kill -0 $TURBO_PID 2>/dev/null; then
        # Process has exited
        wait $TURBO_PID
        TURBO_EXIT_CODE=$?

        if [ $TURBO_EXIT_CODE -ne 0 ]; then
          echo ""
          echo "Turbopack failed with exit code $TURBO_EXIT_CODE. Falling back to standard webpack..."
          echo ""
          export NEXT_TURBO=
          pnpm dev:standard
        fi
        exit 0
      fi

      # Check if server is responding on port 3000
      if curl -s http://localhost:3000 >/dev/null; then
        # Turbopack started successfully
        wait $TURBO_PID
        exit 0
      fi

      sleep 1
      ((COUNTER++))
    done

    # If we're here, Turbopack took too long to start
    kill -9 $TURBO_PID 2>/dev/null
    wait $TURBO_PID 2>/dev/null

    echo ""
    echo "Turbopack timed out after 30 seconds. Falling back to standard webpack..."
    echo ""
    export NEXT_TURBO=
    pnpm dev:standard
  else
    pnpm dev:turbo
  fi
else
  export NEXT_TURBO=
  echo "Starting Next.js with standard webpack..."

  if [ "$SAFE_MODE" = true ]; then
    echo "Safe mode enabled: API calls will use fallback data if external APIs are unreachable"
    echo ""
  fi

  pnpm dev:standard
fi