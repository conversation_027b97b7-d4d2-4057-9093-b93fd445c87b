'use client'

import { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { extractAvatarFromOAuth } from '@/utils/imageHandling'
import { User as SupabaseUser } from '@supabase/supabase-js'
import { UserRole } from '@/types/roles'

/**
 * User type representing the application user
 * This combines data from Supabase Auth and our database
 */
type User = {
  id: string
  auth_user_id: string
  first_name: string
  last_name: string | null
  email: string
  avatar: string | null
  role: UserRole
  created_at?: string
  updated_at?: string | null
  // Add other optional fields that might be in the database
  username?: string | null
  gender?: string | null
  bio?: string | null
  nationality?: string | null
  contactNo?: string | null
  country?: string | null
  state?: string | null
  tshirt_size?: string | null
}

/**
 * Auth context type definition
 */
type AuthContextType = {
  isSignedIn: boolean
  user: User | null
  supabaseUser: SupabaseUser | null
  loading: boolean
  refreshSession: () => Promise<void>
}

/**
 * Create the auth context with default values
 */
const AuthContext = createContext<AuthContextType>({
  isSignedIn: false,
  user: null,
  supabaseUser: null,
  loading: true,
  refreshSession: async () => { }
})

/**
 * Hook to use the auth context
 * @returns The auth context
 */
export const useAuth = () => useContext(AuthContext)

/**
 * Auth provider component
 * This component provides authentication state to the application
 */
export function AuthProvider({ children }: { children: ReactNode }) {
  const [isSignedIn, setIsSignedIn] = useState<boolean>(false)
  const [user, setUser] = useState<User | null>(null)
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const supabase = createClient()

  // Function to sync user data from auth to database
  const syncUserDataToDatabase = async (authUser: SupabaseUser) => {
    try {
      // Extract user metadata
      const firstName = authUser.user_metadata?.first_name || authUser.user_metadata?.given_name || authUser.user_metadata?.name?.split(' ')[0] || ''
      const lastName = authUser.user_metadata?.last_name || authUser.user_metadata?.family_name || (authUser.user_metadata?.name?.split(' ').slice(1).join(' ') || null)
      const avatarUrl = extractAvatarFromOAuth(authUser.user_metadata)

      // Log the data we're trying to sync
      console.log('Syncing user data to database:', {
        auth_user_id: authUser.id,
        first_name: firstName,
        last_name: lastName,
        avatar: avatarUrl,
        email: authUser.email
      })

      if (!authUser.email) {
        console.error('Cannot sync user without email')
        return null
      }

      // First try to find the user by email (most reliable method)
      console.log('Checking if user exists with email:', authUser.email)

      const { data: userByEmail, error: emailCheckError } = await supabase
        .from('users')
        .select('*')
        .eq('email', authUser.email)
        .maybeSingle()

      if (emailCheckError) {
        console.error('Error checking user by email:', emailCheckError)
      } else {
        console.log('User lookup by email result:', userByEmail ? 'Found' : 'Not found')
      }

      // If we found the user by email, update that record
      if (userByEmail) {
        console.log('User exists in database with email, updating record:', userByEmail.id)

        const { data, error } = await supabase
          .from('users')
          .update({
            auth_user_id: authUser.id, // Ensure auth_user_id is set correctly
            first_name: firstName || 'User',
            last_name: lastName || null,
            avatar: avatarUrl || null,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userByEmail.id)
          .select()

        if (error) {
          console.error('Error updating user data by email:', error)
        } else {
          console.log('Successfully updated user data by email:', data)
          return data[0]
        }
      }

      // If not found by email, try by auth_user_id as a fallback
      console.log('Trying to find user by auth_user_id as text:', authUser.id)

      // Use a direct query instead of RPC
      const { data: userByAuthId, error: authIdCheckError } = await supabase
        .from('users')
        .select('*')
        .eq('auth_user_id', authUser.id)
        .maybeSingle()

      if (authIdCheckError) {
        console.error('Error checking user by auth_id:', authIdCheckError)
      } else {
        console.log('User lookup by auth_id result:', userByAuthId ? 'Found' : 'Not found')
      }

      // If we found the user by auth_id, update that record
      if (userByAuthId) {
        console.log('User exists in database with auth_id, updating record:', userByAuthId.id)

        const { data, error } = await supabase
          .from('users')
          .update({
            first_name: firstName || 'User',
            last_name: lastName || null,
            avatar: avatarUrl || null,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userByAuthId.id)
          .select()

        if (error) {
          console.error('Error updating user data by auth_id:', error)
        } else {
          console.log('Successfully updated user data by auth_id:', data)
          return data[0]
        }
      }

      // If user doesn't exist at all, insert a new record
      console.log('User not found in database, creating new record')

      // For insert, we'll use a random UUID for the primary key and store auth_user_id separately
      const { data: insertedUser, error: insertError } = await supabase
        .from('users')
        .insert({
          id: crypto.randomUUID(), // Generate a random UUID for the primary key
          email: authUser.email,
          auth_user_id: authUser.id,
          first_name: firstName || 'User',
          last_name: lastName || null,
          avatar: avatarUrl || null,
          role: UserRole.PUBLIC, // Default role
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()

      if (insertError) {
        console.error('Error creating user record:', insertError)

        // As a last resort, try to find the user one more time
        // This handles race conditions where another process might have created the user
        const { data: finalCheck, error: finalCheckError } = await supabase
          .from('users')
          .select('*')
          .eq('email', authUser.email)
          .maybeSingle()

        if (finalCheckError || !finalCheck) {
          console.error('Final check for user failed:', finalCheckError || 'No user found')

          // Return a temporary user object for the context
          return {
            id: authUser.id,
            auth_user_id: authUser.id,
            first_name: firstName || 'User',
            last_name: lastName || null,
            email: authUser.email,
            avatar: avatarUrl || null,
            role: UserRole.PUBLIC
          }
        }

        console.log('Found user in final check:', finalCheck)
        return finalCheck
      } else {
        console.log('Successfully created user record:', insertedUser)
        return insertedUser[0]
      }
    } catch (error) {
      console.error('Error syncing user data:', error)

      // Return a temporary user object for the context
      if (authUser.email) {
        const firstName = authUser.user_metadata?.first_name || authUser.user_metadata?.given_name || authUser.user_metadata?.name?.split(' ')[0] || 'User'
        const lastName = authUser.user_metadata?.last_name || authUser.user_metadata?.family_name || (authUser.user_metadata?.name?.split(' ').slice(1).join(' ') || null)
        const avatarUrl = extractAvatarFromOAuth(authUser.user_metadata)

        return {
          id: authUser.id,
          auth_user_id: authUser.id,
          first_name: firstName,
          last_name: lastName,
          email: authUser.email,
          avatar: avatarUrl || null,
          role: UserRole.PUBLIC
        }
      }

      return null
    }
  }

  // Function to fetch user data from the database
  const fetchUserData = async (userId: string) => {
    try {
      console.log('Fetching user data for auth_user_id:', userId)

      // Get the auth user data
      const { data: { user: authUser } } = await supabase.auth.getUser()

      if (!authUser) {
        console.error('No auth user found')
        return
      }

      // Check if we have avatar in auth metadata
      const authAvatar = extractAvatarFromOAuth(authUser.user_metadata)
      console.log('Auth avatar from metadata:', authAvatar)

      // Try to find the user in the database
      console.log('Looking up user in database...')

      // First try by email (most reliable)
      let userData = null
      let error = null

      if (authUser.email) {
        console.log('Trying to find user by email:', authUser.email)
        const emailLookup = await supabase
          .from('users')
          .select('*')
          .eq('email', authUser.email)
          .maybeSingle()

        if (emailLookup.error) {
          console.error('Error looking up user by email:', emailLookup.error)
        } else if (emailLookup.data) {
          console.log('User found by email:', emailLookup.data.id)
          userData = emailLookup.data
        } else {
          console.log('User not found by email')
        }
      }

      // If not found by email, try by auth_user_id
      if (!userData) {
        console.log('Trying to find user by auth_user_id:', userId)

        // Use direct query instead of RPC
        const authIdLookup = await supabase
          .from('users')
          .select('*')
          .eq('auth_user_id', userId)
          .maybeSingle()

        if (authIdLookup.error) {
          console.error('Error looking up user by auth_id:', authIdLookup.error)
          error = authIdLookup.error
        } else if (authIdLookup.data) {
          console.log('User found by auth_id:', authIdLookup.data.id)
          userData = authIdLookup.data
        } else {
          console.log('User not found by auth_id')
        }
      }

      // If we found the user in the database
      if (userData) {
        console.log('User found in database:', userData)

        // Always prioritize the Google avatar if available
        if (authAvatar && authAvatar.includes('googleusercontent.com')) {
          console.log('Using Google avatar from auth provider')

          // Check if we need to update the database
          if (userData.avatar !== authAvatar) {
            console.log('Avatar in database differs from auth provider, updating...')
            console.log('Database avatar:', userData.avatar)
            console.log('Auth avatar:', authAvatar)

            // Always sync the avatar to the database to ensure it's up to date
            await syncUserDataToDatabase(authUser)
          }

          // Use the auth avatar in the user object
          setUser({
            ...userData,
            avatar: authAvatar,
            last_name: userData.last_name || null,
            auth_user_id: userData.auth_user_id || authUser.id,
            role: (userData.role as UserRole) || UserRole.PUBLIC
          })
        } else if (userData.avatar) {
          // If we have a database avatar but no auth avatar, use the database avatar
          console.log('Using avatar from database:', userData.avatar)
          setUser({
            ...userData,
            last_name: userData.last_name || null,
            avatar: userData.avatar || null,
            auth_user_id: userData.auth_user_id || authUser.id,
            role: (userData.role as UserRole) || UserRole.PUBLIC
          })
        } else {
          // No avatar in either place
          console.log('No avatar found in auth or database')
          setUser({
            ...userData,
            last_name: userData.last_name || null,
            avatar: null,
            auth_user_id: userData.auth_user_id || authUser.id,
            role: (userData.role as UserRole) || UserRole.PUBLIC
          })
        }
      } else {
        // If we can't find the user in the database, create a new record
        console.warn('User data not found in database, creating new record', error)

        // Try to sync user data to database
        const syncedUser = await syncUserDataToDatabase(authUser)

        if (syncedUser && typeof syncedUser === 'object') {
          console.log('Successfully synced user to database:', syncedUser)
          setUser({
            ...syncedUser,
            last_name: syncedUser.last_name || null,
            avatar: syncedUser.avatar || null,
            auth_user_id: syncedUser.auth_user_id || authUser.id,
            role: (syncedUser.role as UserRole) || UserRole.PUBLIC
          })
        } else if (authUser.email) {
          // Fallback to just using auth data without database sync
          console.warn('Failed to sync user to database, using auth data only')

          // Extract name parts from metadata
          const firstName = authUser.user_metadata?.first_name ||
            authUser.user_metadata?.given_name ||
            authUser.user_metadata?.name?.split(' ')[0] ||
            'User'

          const lastName = authUser.user_metadata?.last_name ||
            authUser.user_metadata?.family_name ||
            (authUser.user_metadata?.name?.split(' ').slice(1).join(' ') || null)

          setUser({
            id: authUser.id,
            auth_user_id: authUser.id,
            first_name: firstName,
            last_name: lastName,
            email: authUser.email,
            avatar: authAvatar || null,
            role: UserRole.PUBLIC
          })
        } else {
          console.error('Cannot create user without email')
        }
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
    }
  }

  /**
   * Function to refresh the session
   * This function fetches the latest user data from Supabase Auth and the database
   */
  // Add a ref to track if a refresh is in progress to prevent multiple simultaneous refreshes
  const [isRefreshing, setIsRefreshing] = useState(false)
  const lastRefreshTime = useRef<number>(0)

  const refreshSession = async () => {
    try {
      // Check if we're in a browser environment
      const isBrowser = typeof window !== 'undefined';

      // Debounce refreshes - don't refresh more than once every 2 seconds
      const now = Date.now()
      const timeSinceLastRefresh = now - lastRefreshTime.current

      if (isRefreshing) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Session refresh already in progress, skipping')
        }
        return
      }

      if (timeSinceLastRefresh < 2000) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`Session was refreshed ${timeSinceLastRefresh}ms ago, debouncing`)
        }
        return
      }

      // Mark that we're refreshing and update the last refresh time
      setIsRefreshing(true)
      lastRefreshTime.current = now

      // Set loading to true during refresh
      setLoading(true)
      if (process.env.NODE_ENV === 'development') {
        console.log('Refreshing session...')
      }

      // Check for reset cookie on client side only
      let hasResetCookie = false;
      if (isBrowser) {
        hasResetCookie = document.cookie.includes('sb-reset-complete');
        if (hasResetCookie) {
          console.log('Reset cookie detected during session refresh');
        }
      }

      // Get the current user from Supabase Auth
      const { data: { user: authUser }, error } = await supabase.auth.getUser()

      // Also get the session to double-check authentication state
      const { data: { session } } = await supabase.auth.getSession()

      // Update the signed-in state - require both user and session
      // If we have a reset cookie, force signed-out state
      const isAuthenticated = !!authUser && !!session && !error && !hasResetCookie

      if (process.env.NODE_ENV === 'development') {
        console.log('Auth state check:', {
          hasUser: !!authUser,
          hasSession: !!session,
          hasError: !!error,
          hasResetCookie,
          isAuthenticated
        })
      }

      setIsSignedIn(isAuthenticated)

      // Update the Supabase user state
      setSupabaseUser(authUser)

      // Always try to fetch user data if we have an auth user, even if isAuthenticated is false
      // This helps with cases where the server-side auth is valid but client-side state is not yet synced
      if (authUser) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Auth user found, fetching user data regardless of session state')
        }

        // Always check for avatar in auth metadata
        const authAvatar = extractAvatarFromOAuth(authUser.user_metadata)

        // If we have a valid avatar from auth metadata (especially Google), make sure it's synced
        if (authAvatar) {
          if (process.env.NODE_ENV === 'development') {
            if (authAvatar.includes('googleusercontent.com')) {
              console.log('Refreshing Google avatar from auth provider:', authAvatar)
            } else {
              console.log('Refreshing avatar from auth provider:', authAvatar)
            }
          }
          // Always sync user data to ensure the avatar is up-to-date
          await syncUserDataToDatabase(authUser)
        }

        // Fetch the latest user data
        await fetchUserData(authUser.id)

        // Force isSignedIn to true if we have user data, even if session check failed
        // This helps with cases where cookies aren't properly synced between server and client
        if (user) {
          setIsSignedIn(true)
        }
      } else {
        // Clear user data if not authenticated
        if (process.env.NODE_ENV === 'development') {
          console.log('No auth user found, clearing user data')
        }
        setUser(null)
        setSupabaseUser(null)
      }
    } catch (error) {
      console.error('Error refreshing session:', error)
      setIsSignedIn(false)
      setUser(null)
      setSupabaseUser(null)
    } finally {
      // Short timeout to ensure smooth transitions
      setTimeout(() => {
        setLoading(false)
        setIsRefreshing(false)
        if (process.env.NODE_ENV === 'development') {
          console.log('Session refresh complete, loading set to false')
        }
      }, 300)
    }
  }

  /**
   * Effect to set up auth state change listener and periodic refresh
   */
  useEffect(() => {
    // Check if user is signed in
    refreshSession()

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, !!session)

        // Log more details about the event
        if (process.env.NODE_ENV === 'development') {
          console.log('Auth event details:', {
            event,
            hasSession: !!session,
            userId: session?.user?.id,
            eventTime: new Date().toISOString()
          })
        }

        // For SIGNED_IN events, always refresh immediately
        if (event === 'SIGNED_IN') {
          console.log('User signed in, refreshing session immediately')
          // Force a small delay to ensure Supabase has time to set cookies
          setTimeout(() => {
            refreshSession()
          }, 500)
          return
        }

        if (!session) {
          // Clear user data if not authenticated
          setUser(null)
          setSupabaseUser(null)
          setIsSignedIn(false)
          // Make sure to set loading to false when session is cleared
          setLoading(false)
          console.log('Session cleared, user data reset')
        } else {
          console.log('Session available, refreshing user data')
          // Always refresh the session for any auth event to ensure we have the latest data
          await refreshSession()
        }
      }
    )

    // Set up a periodic refresh to ensure we always have the latest data
    // This helps keep the session fresh and detect any changes made in other tabs/windows
    // But we'll make it less frequent to avoid unnecessary refreshes
    const intervalId = setInterval(() => {
      // Only refresh if signed in and we don't have a user object
      // or if it's been more than 5 minutes since the last refresh
      const now = Date.now()
      const fiveMinutes = 5 * 60 * 1000
      const shouldRefresh = isSignedIn &&
        (!user || (now - lastRefreshTime.current > fiveMinutes))

      if (shouldRefresh) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Periodic session refresh')
        }
        refreshSession()
      }
    }, 300000) // Refresh every 5 minutes instead of every minute

    // Set up a listener for cookie changes to detect when auth state is reset via API
    // Only run this on the client side
    let cookieCheckInterval: NodeJS.Timeout | null = null;

    // Check if we're in a browser environment
    if (typeof window !== 'undefined') {
      cookieCheckInterval = setInterval(async () => {
        // Check if auth cookies exist
        const hasCookies = document.cookie.includes('sb-') ||
          document.cookie.includes('supabase-auth');

        // If we think we're signed in but have no auth cookies, refresh the session
        if (isSignedIn && !hasCookies) {
          console.log('Auth cookies missing but state is signed in, refreshing session');
          await refreshSession();
        }

        // Also check for the reset cookie
        const hasResetCookie = document.cookie.includes('sb-reset-complete');
        if (hasResetCookie && isSignedIn) {
          console.log('Reset cookie detected, clearing auth state');
          setIsSignedIn(false);
          setUser(null);
          setSupabaseUser(null);
          setLoading(false);
        }

        // Check for new auth cookies when we think we're signed out
        if (!isSignedIn && hasCookies && !loading) {
          console.log('Auth cookies detected but state is signed out, refreshing session');
          await refreshSession();
        }
      }, 1000); // Check every 1 second for more responsiveness
    }

    // Clean up subscription and intervals on unmount
    return () => {
      subscription.unsubscribe()
      clearInterval(intervalId)
      if (cookieCheckInterval) {
        clearInterval(cookieCheckInterval)
      }
    }
  }, []) // Empty dependency array to only run once on mount

  // Provide auth context to children
  return (
    <AuthContext.Provider value={{ isSignedIn, user, supabaseUser, loading, refreshSession }}>
      {children}
    </AuthContext.Provider>
  )
}
